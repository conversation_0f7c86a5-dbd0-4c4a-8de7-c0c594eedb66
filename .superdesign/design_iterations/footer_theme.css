:root {
  --background: oklch(0.1200 0.0150 240.0000);
  --foreground: oklch(0.9500 0.0100 240.0000);
  --card: oklch(0.1400 0.0200 240.0000);
  --card-foreground: oklch(0.9500 0.0100 240.0000);
  --popover: oklch(0.1400 0.0200 240.0000);
  --popover-foreground: oklch(0.9500 0.0100 240.0000);
  --primary: oklch(0.6500 0.2200 25.0000);
  --primary-foreground: oklch(0.9800 0.0050 25.0000);
  --secondary: oklch(0.2000 0.0300 240.0000);
  --secondary-foreground: oklch(0.8500 0.0150 240.0000);
  --muted: oklch(0.1800 0.0250 240.0000);
  --muted-foreground: oklch(0.6500 0.0200 240.0000);
  --accent: oklch(0.6500 0.2200 25.0000);
  --accent-foreground: oklch(0.9800 0.0050 25.0000);
  --destructive: oklch(0.5800 0.2400 28.0000);
  --destructive-foreground: oklch(0.9800 0.0050 28.0000);
  --border: oklch(0.2500 0.0300 240.0000);
  --input: oklch(0.2500 0.0300 240.0000);
  --ring: oklch(0.6500 0.2200 25.0000);
  --chart-1: oklch(0.6500 0.2200 25.0000);
  --chart-2: oklch(0.7200 0.1800 45.0000);
  --chart-3: oklch(0.6800 0.2000 200.0000);
  --chart-4: oklch(0.7000 0.1900 280.0000);
  --chart-5: oklch(0.6600 0.2100 320.0000);
  --sidebar: oklch(0.1400 0.0200 240.0000);
  --sidebar-foreground: oklch(0.9500 0.0100 240.0000);
  --sidebar-primary: oklch(0.6500 0.2200 25.0000);
  --sidebar-primary-foreground: oklch(0.9800 0.0050 25.0000);
  --sidebar-accent: oklch(0.2000 0.0300 240.0000);
  --sidebar-accent-foreground: oklch(0.8500 0.0150 240.0000);
  --sidebar-border: oklch(0.2500 0.0300 240.0000);
  --sidebar-ring: oklch(0.6500 0.2200 25.0000);
  --font-sans: Inter, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-serif: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
  --font-mono: JetBrains Mono, 'Fira Code', monospace;
  --radius: 0.5rem;
  --shadow-2xs: 0 1px 2px 0 hsl(240 10% 3% / 0.05);
  --shadow-xs: 0 1px 3px 0 hsl(240 10% 3% / 0.10), 0 1px 2px -1px hsl(240 10% 3% / 0.10);
  --shadow-sm: 0 1px 3px 0 hsl(240 10% 3% / 0.10), 0 1px 2px -1px hsl(240 10% 3% / 0.10);
  --shadow: 0 4px 6px -1px hsl(240 10% 3% / 0.10), 0 2px 4px -2px hsl(240 10% 3% / 0.10);
  --shadow-md: 0 10px 15px -3px hsl(240 10% 3% / 0.10), 0 4px 6px -4px hsl(240 10% 3% / 0.10);
  --shadow-lg: 0 20px 25px -5px hsl(240 10% 3% / 0.10), 0 8px 10px -6px hsl(240 10% 3% / 0.10);
  --shadow-xl: 0 25px 50px -12px hsl(240 10% 3% / 0.25);
  --shadow-2xl: 0 50px 100px -20px hsl(240 10% 3% / 0.25);
  --tracking-normal: 0em;
  --spacing: 1rem;

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}