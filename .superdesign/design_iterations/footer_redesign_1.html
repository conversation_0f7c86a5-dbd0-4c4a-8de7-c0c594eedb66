<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON> AI - Redesigned Footer</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.min.js"></script>
    <link rel="stylesheet" href="footer_theme.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        /* Custom styles using CSS variables */
        body {
            font-family: var(--font-sans) !important;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%) !important;
            margin: 0 !important;
            padding: 0 !important;
        }

        .footer-container {
            background: linear-gradient(135deg, 
                hsl(var(--background)) 0%, 
                hsl(var(--card)) 50%, 
                hsl(var(--background)) 100%) !important;
            border-top: 1px solid hsl(var(--border)) !important;
            position: relative !important;
        }

        .footer-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, 
                transparent 0%, 
                hsl(var(--primary)) 50%, 
                transparent 100%);
            opacity: 0.6;
        }

        .brand-section {
            background: hsl(var(--card)) !important;
            border: 1px solid hsl(var(--border)) !important;
            border-radius: var(--radius-lg) !important;
            box-shadow: var(--shadow-sm) !important;
            transition: all 0.3s ease !important;
        }

        .brand-section:hover {
            transform: translateY(-2px) !important;
            box-shadow: var(--shadow-md) !important;
            border-color: hsl(var(--primary) / 0.3) !important;
        }

        .brand-logo {
            background: hsl(var(--primary)) !important;
            color: hsl(var(--primary-foreground)) !important;
            border-radius: var(--radius-md) !important;
            font-weight: 600 !important;
            transition: all 0.3s ease !important;
        }

        .brand-logo:hover {
            transform: scale(1.05) !important;
            box-shadow: 0 0 20px hsl(var(--primary) / 0.4) !important;
        }

        .brand-title {
            color: hsl(var(--foreground)) !important;
            font-weight: 600 !important;
            font-size: 1.25rem !important;
            margin-bottom: 0.5rem !important;
        }

        .brand-description {
            color: hsl(var(--muted-foreground)) !important;
            font-size: 0.875rem !important;
            line-height: 1.5 !important;
            transition: color 0.3s ease !important;
        }

        .brand-section:hover .brand-description {
            color: hsl(var(--foreground)) !important;
        }

        .footer-section h3 {
            color: hsl(var(--foreground)) !important;
            font-weight: 600 !important;
            font-size: 1.125rem !important;
            margin-bottom: 1.5rem !important;
            position: relative !important;
            transition: all 0.3s ease !important;
        }

        .footer-section h3::after {
            content: '';
            position: absolute;
            bottom: -0.5rem;
            left: 0;
            width: 0;
            height: 2px;
            background: hsl(var(--primary));
            transition: width 0.3s ease;
        }

        .footer-section:hover h3::after {
            width: 2rem;
        }

        .footer-section h3:hover {
            color: hsl(var(--primary)) !important;
            letter-spacing: 0.02em !important;
        }

        .footer-link {
            color: hsl(var(--muted-foreground)) !important;
            text-decoration: none !important;
            font-size: 0.875rem !important;
            transition: all 0.2s ease !important;
            display: inline-block !important;
            padding: 0.25rem 0 !important;
            position: relative !important;
        }

        .footer-link:hover {
            color: hsl(var(--primary)) !important;
            transform: translateY(-1px) !important;
        }

        .footer-link::before {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 0;
            height: 1px;
            background: hsl(var(--primary));
            transition: width 0.2s ease;
        }

        .footer-link:hover::before {
            width: 100%;
        }

        .social-icon {
            background: hsl(var(--secondary)) !important;
            color: hsl(var(--secondary-foreground)) !important;
            border-radius: var(--radius-md) !important;
            transition: all 0.3s ease !important;
            border: 1px solid hsl(var(--border)) !important;
        }

        .social-icon:hover {
            background: hsl(var(--primary)) !important;
            color: hsl(var(--primary-foreground)) !important;
            transform: scale(1.1) rotate(5deg) !important;
            box-shadow: var(--shadow-md) !important;
        }

        .social-icon:active {
            transform: scale(1.2) !important;
        }

        .copyright-section {
            border-top: 1px solid hsl(var(--border)) !important;
            background: hsl(var(--muted) / 0.3) !important;
            backdrop-filter: blur(10px) !important;
        }

        .copyright-text {
            color: hsl(var(--muted-foreground)) !important;
            font-size: 0.875rem !important;
            transition: color 0.3s ease !important;
        }

        .copyright-text:hover {
            color: hsl(var(--foreground)) !important;
        }

        /* Animation classes */
        .animate-fade-in {
            animation: fadeIn 0.6s ease-out forwards;
        }

        .animate-slide-up {
            animation: slideUp 0.6s ease-out forwards;
        }

        .animate-border-expand {
            animation: borderExpand 0.8s ease-out 0.2s forwards;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideUp {
            from { opacity: 0; transform: translateY(40px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes borderExpand {
            from { width: 0; }
            to { width: 100%; }
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .footer-grid {
                grid-template-columns: 1fr !important;
                gap: 2rem !important;
            }
            
            .brand-section {
                text-align: center !important;
            }
        }
    </style>
</head>
<body>
    <!-- Demo Dashboard Content (keeping original for context) -->
    <div class="min-h-screen bg-gradient-to-br from-slate-50 to-slate-200 p-8">
        <div class="max-w-6xl mx-auto">
            <h1 class="text-4xl font-bold text-slate-800 mb-8">Dashboard</h1>
            
            <!-- Welcome Section -->
            <div class="bg-slate-100 rounded-2xl p-8 mb-8 text-center">
                <h2 class="text-3xl font-semibold text-slate-700 mb-4">
                    Welcome to <span class="text-orange-600">Robynn.ai</span>
                </h2>
                <p class="text-slate-600 text-lg">Your augmented marketing team. Powered by agents.</p>
            </div>

            <!-- AI Marketing Agents Section -->
            <h2 class="text-2xl font-bold text-slate-800 mb-6">AI Marketing Agents</h2>
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-16">
                <!-- Deep Researcher -->
                <div class="bg-white rounded-xl p-6 shadow-sm border border-slate-200">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-orange-600 rounded-lg flex items-center justify-center mr-4">
                            <i data-lucide="bar-chart-3" class="w-6 h-6 text-white"></i>
                        </div>
                        <div>
                            <h3 class="font-semibold text-slate-800">Deep Researcher</h3>
                            <div class="flex gap-2 mt-1">
                                <span class="px-2 py-1 bg-slate-100 text-slate-600 text-xs rounded">AI-Powered</span>
                                <span class="px-2 py-1 bg-slate-100 text-slate-600 text-xs rounded">Multi-Channel</span>
                            </div>
                        </div>
                    </div>
                    <p class="text-slate-600 mb-4">Coordinate and automate multi-channel marketing campaigns with AI-driven orchestration.</p>
                    <ul class="text-sm text-slate-600 space-y-1">
                        <li>• Multi-channel campaign planning</li>
                        <li>• Automated content generation</li>
                        <li>• Timeline & budget optimization</li>
                    </ul>
                </div>

                <!-- SEO Agent -->
                <div class="bg-white rounded-xl p-6 shadow-sm border border-slate-200">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-orange-600 rounded-lg flex items-center justify-center mr-4">
                            <i data-lucide="search" class="w-6 h-6 text-white"></i>
                        </div>
                        <div>
                            <h3 class="font-semibold text-slate-800">SEO Agent</h3>
                            <div class="flex gap-2 mt-1">
                                <span class="px-2 py-1 bg-slate-100 text-slate-600 text-xs rounded">AI-Powered</span>
                                <span class="px-2 py-1 bg-slate-100 text-slate-600 text-xs rounded">Keyword Intelligence</span>
                            </div>
                        </div>
                    </div>
                    <p class="text-slate-600 mb-4">Get comprehensive keyword research and SEO strategy recommendations for your business.</p>
                    <ul class="text-sm text-slate-600 space-y-1">
                        <li>• Keyword analysis & research</li>
                        <li>• Competitive SEO insights</li>
                        <li>• Content strategy recommendations</li>
                    </ul>
                </div>

                <!-- Content Agent -->
                <div class="bg-white rounded-xl p-6 shadow-sm border border-slate-200">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-orange-600 rounded-lg flex items-center justify-center mr-4">
                            <i data-lucide="edit-3" class="w-6 h-6 text-white"></i>
                        </div>
                        <div>
                            <h3 class="font-semibold text-slate-800">Content Agent</h3>
                            <div class="flex gap-2 mt-1">
                                <span class="px-2 py-1 bg-slate-100 text-slate-600 text-xs rounded">AI-Powered</span>
                                <span class="px-2 py-1 bg-slate-100 text-slate-600 text-xs rounded">Rich Editor</span>
                            </div>
                        </div>
                    </div>
                    <p class="text-slate-600 mb-4">Create, edit, and optimize content with AI assistance. Generate outlines, improve writing, and manage citations.</p>
                    <ul class="text-sm text-slate-600 space-y-1">
                        <li>• Content generation & outlines</li>
                        <li>• Grammar & style correction</li>
                        <li>• Citation management</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- REDESIGNED FOOTER -->
    <footer class="footer-container animate-fade-in">
        <div class="max-w-7xl mx-auto px-6 py-16">
            <!-- Main Footer Content -->
            <div class="footer-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
                
                <!-- Brand Section -->
                <div class="brand-section p-6 lg:col-span-1 animate-slide-up">
                    <div class="flex items-center mb-4">
                        <div class="brand-logo w-10 h-10 flex items-center justify-center text-sm font-bold mr-3">
                            R
                        </div>
                        <h3 class="brand-title">Robynn AI</h3>
                    </div>
                    <p class="brand-description">
                        AI-powered marketing specialists that grow your business.
                    </p>
                </div>

                <!-- Product Section -->
                <div class="footer-section animate-slide-up" style="animation-delay: 0.1s;">
                    <h3>Product</h3>
                    <div class="space-y-3">
                        <div><a href="#" class="footer-link">Overview</a></div>
                        <div><a href="#" class="footer-link">Blog</a></div>
                    </div>
                </div>

                <!-- Support Section -->
                <div class="footer-section animate-slide-up" style="animation-delay: 0.2s;">
                    <h3>Support</h3>
                    <div class="space-y-3">
                        <div><a href="#" class="footer-link">Contact Us</a></div>
                        <div><a href="#" class="footer-link">Sign In</a></div>
                    </div>
                </div>

                <!-- Connect Section -->
                <div class="footer-section animate-slide-up" style="animation-delay: 0.3s;">
                    <h3>Connect</h3>
                    <div class="flex space-x-4 mt-6">
                        <a href="#" class="social-icon w-10 h-10 flex items-center justify-center">
                            <i data-lucide="github" class="w-5 h-5"></i>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Copyright Section -->
            <div class="copyright-section pt-8 mt-8 text-center animate-fade-in" style="animation-delay: 0.4s;">
                <p class="copyright-text">
                    Copyright © 2025 Robynn AI. All rights reserved.
                </p>
            </div>
        </div>
    </footer>

    <script>
        // Initialize Lucide icons
        lucide.createIcons();

        // Add intersection observer for animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.animationPlayState = 'running';
                }
            });
        }, observerOptions);

        // Observe footer elements
        document.querySelectorAll('.animate-fade-in, .animate-slide-up').forEach(el => {
            el.style.animationPlayState = 'paused';
            observer.observe(el);
        });

        // Add ripple effect to social icons
        document.querySelectorAll('.social-icon').forEach(icon => {
            icon.addEventListener('click', function(e) {
                const ripple = document.createElement('div');
                const rect = this.getBoundingClientRect();
                const size = Math.max(rect.width, rect.height);
                const x = e.clientX - rect.left - size / 2;
                const y = e.clientY - rect.top - size / 2;
                
                ripple.style.cssText = `
                    position: absolute;
                    width: ${size}px;
                    height: ${size}px;
                    left: ${x}px;
                    top: ${y}px;
                    background: rgba(255, 255, 255, 0.3);
                    border-radius: 50%;
                    transform: scale(0);
                    animation: ripple 0.4s ease-out;
                    pointer-events: none;
                `;
                
                this.style.position = 'relative';
                this.style.overflow = 'hidden';
                this.appendChild(ripple);
                
                setTimeout(() => ripple.remove(), 400);
            });
        });

        // Add CSS for ripple animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes ripple {
                to {
                    transform: scale(2);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>