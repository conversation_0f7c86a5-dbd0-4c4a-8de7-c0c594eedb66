// Auto-generated Mastra tool from exa-search-enhanced-tool.ts
// This file is automatically generated by src/scripts/sync-mastra.js
// Do not edit manually - changes will be overwritten

import { z } from "zod"
import { createTool } from "@mastra/core"
import Exa from "exa-js"
import { ENV_VARS } from '../shared/env.js'

const exaSearchEnhancedSchema = z.object({
  company_name: z.string().describe("Company name to research"),
  search_type: z.enum(["competitors", "intelligence", "both"]).default("both").describe("Type of search to perform"),
  industry: z.string().optional().describe("Industry context for better competitor matching"),
  numResults: z.number().default(15).describe("Number of search results to return (max 25)"),
})

export const exaSearchEnhancedTool = createTool({
  id: "exa_search",
  description: "Find competitors and deep company intelligence using Exa AI",
  inputSchema: exaSearchEnhancedSchema,
  execute: async (context) => {
    const { company_name, search_type, industry, numResults } = context.context

    const apiKey = ENV_VARS.EXA_API_KEY()
    if (!apiKey) {
      console.error('Exa API credentials missing - returning mock data for testing')
      // Return mock data instead of throwing error
      return {
        success: true,
        company_intelligence: {
          overview: `${company_name} is a technology company in the ${industry || 'technology'} sector.`,
          business_model: 'B2B SaaS platform',
          key_products: ['Product A', 'Product B'],
          target_market: 'Enterprise customers',
          funding: 'Series B funded',
          recent_news: ['Recent product launch', 'Partnership announcement']
        },
        competitors: [
          {
            name: 'Competitor 1',
            domain: 'competitor1.com',
            similarity_reason: 'Similar product offering',
            market_position: 'Direct competitor'
          },
          {
            name: 'Competitor 2', 
            domain: 'competitor2.com',
            similarity_reason: 'Same target market',
            market_position: 'Indirect competitor'
          }
        ],
        note: 'Mock data returned - Exa API credentials not configured'
      }
    }

    const exa = new Exa(apiKey)

    try {
      let companyIntelligence = null
      let competitors = []

      // Company Intelligence Search
      if (search_company overview business model`,
          `${company_name} products services offerings`,
          `${company_name} funding revenue financial performance`,
          `${company_name} recent news updates announcements`,
          `${company_name} target market customers strategy`
        ]

        const intelligenceResults = await Promise.all(
          intelligenceQueries.map(async (query) => {
            try {
              return await exa.searchAndContents(query, {
                numResults: 3,
                text: true,
                category: "company"
              })
            } catch (error) {
              console.warn(`Intelligence search failed for query: ${query}`, error)
              return { results: [] }
            }
          })
        )

        // Process intelligence results
        const allIntelligenceResults = intelligenceResults.flatMap(result => result.results || [])
        
        companyIntelligence = {
          overview: extractIntelligence(allIntelligenceResults, 'overview'),
          business_model: extractIntelligence(allIntelligenceResults, 'business model'),
          key_products: extractIntelligence(allIntelligenceResults, 'products'),
          target_market: extractIntelligence(allIntelligenceResults, 'target market'),
          funding: extractIntelligence(allIntelligenceResults, 'funding'),
          recent_news: extractIntelligence(allIntelligenceResults, 'news'),
          sources: allIntelligenceResults.map(result => ({
            title: result.title,
            url: result.url,
            snippet: result.text?.substring(0, 200) + '...'
          }))
        }
      }

      // Competitor Search
      if (search_competitors alternatives similar companies`,
          `companies like ${company_name} ${industry || ''}`,
          `${company_name} vs competitors comparison`,
          `${industry || 'technology'} companies similar to ${company_name}`
        ]

        const competitorResults = await Promise.all(
          competitorQueries.map(async (query) => {
            try {
              return await exa.searchAndContents(query, {
                numResults: Math.ceil(numResults / competitorQueries.length),
                text: true,
                category: "company"
              })
            } catch (error) {
              console.warn(`Competitor search failed for query: ${query}`, error)
              return { results: [] }
            }
          })
        )

        // Process competitor results
        const allCompetitorResults = competitorResults.flatMap(result => result.results || [])
        competitors = extractCompetitors(allCompetitorResults, company_name)
      }

      return {
        success: true,
        company_intelligence: companyIntelligence,
        competitors: competitors.slice(0, numResults),
        total_sources: (companyIntelligence?.sources?.length || 0) + competitors.length
      }

    } catch (error) {
      console.error('Exa enhanced search error:', error)
      throw new Error(`Exa API error: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }
})

// Helper function to extract intelligence from search results
function extractIntelligence(results[], type): string {
  const relevantResults = results.filter(result => 
    result.text && result.text.toLowerCase().includes(type.toLowerCase())
  )
  
  if (relevantResults.length === 0) return 'Information not available'
  
  // Extract relevant sentences
  const sentences = relevantResults
    .map(result => result.text)
    .join(' ')
    .split('.')
    .filter(sentence => sentence.toLowerCase().includes(type.toLowerCase()))
    .slice(0, 3)
    .join('. ')
  
  return sentences || 'Information not available'
}

// Helper function to extract competitors from search results
function extractCompetitors(results[], targetCompany): any[] {
  const competitors = []
  const seenDomains = new Set()
  
  results.forEach(result => {
    if (!result.text) return
    
    // Extract potential competitor names and domains from text
    const text = result.text.toLowerCase()
    const targetLower = targetCompany.toLowerCase()
    
    // Skip if this result is primarily about the target company
    if (text.includes(targetLower) && !text.includes('vs') && !text.includes('competitor')) {
      return
    }
    
    // Extract domain from URL
    let domain = ''
    try {
      const url = new URL(result.url)
      domain = url.hostname.replace('www.', '')
    } catch (error) {
      // Invalid URL, skip
      return
    }
    
    if (seenDomains.has(domain)) return
    seenDomains.add(domain)
    
    // Determine similarity reason
    let similarityReason = 'Found in competitor research'
    if (text.includes('similar')) similarityReason = 'Similar business model'
    if (text.includes('alternative')) similarityReason = 'Alternative solution'
    if (text.includes('competitor')) similarityReason = 'Direct competitor'
    
    competitors.push({
      name: result.title.split(' - ')[0].split(' | ')[0], // Clean up title
      domain: domain,
      similarity_reason: similarityReason,
      market_position: text.includes('direct') ? 'Direct competitor' : 'Indirect competitor',
      source_url: result.url,
      description: result.text.substring(0, 200) + '...'
    })
  })
  
  return competitors.slice(0, 10) // Limit to top 10 competitors
}
