# Publishing System Testing Guide

This guide provides comprehensive instructions for testing the publishing system integration with the orchestrator agent.

## Prerequisites

### 1. Database Setup
Ensure the publishing system database tables are created:

```bash
# Run the migration (if using Supabase locally)
supabase db reset

# Or apply the specific migration
supabase migration up --include-all

# Or run the SQL directly in your database
psql -d your_database -f supabase/migrations/20250126150000_add_publishing_system_tables.sql
```

**Note**: The migration script has been updated to remove sequence grants that don't apply to UUID primary keys.

### 2. Environment Variables
Ensure these environment variables are configured:

```bash
# Supabase Configuration
PUBLIC_SUPABASE_URL=your_supabase_url
PUBLIC_SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# Slack Webhook (for testing)
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/WEBHOOK/URL
```

## Testing Workflow

### Phase 1: Database Schema Verification

1. **Verify Tables Created**
   ```sql
   -- Check if tables exist
   SELECT table_name FROM information_schema.tables 
   WHERE table_schema = 'public' 
   AND table_name IN ('publishing_destinations', 'publishing_analytics');
   ```

2. **Check Default Data**
   ```sql
   -- Verify default Slack destination was created
   SELECT * FROM publishing_destinations WHERE type = 'slack';
   ```

### Phase 2: Slack Webhook Configuration

1. **Create Slack Webhook**
   - Go to your Slack workspace
   - Navigate to Apps → Incoming Webhooks
   - Create a new webhook for your desired channel
   - Copy the webhook URL

2. **Configure Destination via API**
   ```bash
   curl -X POST http://localhost:5173/api/publishing/destinations \
     -H "Content-Type: application/json" \
     -d '{
       "type": "slack",
       "name": "Test Slack Channel",
       "description": "Test channel for research publishing",
       "enabled": true,
       "config": {
         "webhookUrl": "YOUR_WEBHOOK_URL",
         "channel": "#research",
         "username": "Research Bot",
         "iconEmoji": ":mag:",
         "retryAttempts": 3,
         "timeout": 10000
       }
     }'
   ```

   **Note**: Use camelCase for config fields (`webhookUrl`, `iconEmoji`) and include required numeric fields (`retryAttempts`, `timeout`).

3. **Verify Destination Created**
   ```bash
   curl http://localhost:5173/api/publishing/destinations?enabled=true
   ```

### Phase 3: End-to-End Research → Publish Workflow

1. **Start the Development Server**
   ```bash
   pnpm run dev
   ```

2. **Navigate to Campaign Orchestrator**
   - Open browser to `http://localhost:5173`
   - Login/authenticate if required
   - Navigate to Campaign Orchestrator page

3. **Execute Research Query**
   - Enter a company name or domain (e.g., "OpenAI" or "openai.com")
   - Submit the research request
   - Monitor the 7-step progress:
     1. Processing input and extracting company information
     2. Enriching target company data with Apollo API
     3. Gathering competitive intelligence with Exa search
     4. Discovering similar companies and competitors
     5. Retrieving contact information for all companies
     6. Formatting comprehensive research results
     7. Publishing results to configured destinations (optional)

4. **Verify Publishing Step**
   - Step 7 should show one of these statuses:
     - "Publishing destinations available - ready for manual publishing" (if destinations configured)
     - "No publishing destinations configured - skipping publishing step" (if no destinations)
     - "Publishing check failed - continuing without publishing" (if error occurred)

5. **Test Manual Publishing**
   - After research completes, look for the "Publish" button in the action buttons
   - Click the "Publish" button to open the publishing modal
   - Verify the modal shows:
     - Available destinations (Slack should be enabled)
     - Placeholder destinations with "Coming Soon" labels (HubSpot, Salesforce, Generic Webhook)
     - Publishing options (Include Metadata, Include Contacts, etc.)

6. **Execute Publishing**
   - Select the Slack destination
   - Configure publishing options as desired
   - Click "Preview" to see formatted message
   - Click "Publish" to send to Slack
   - Verify success message and check Slack channel for the message

### Phase 4: Error Scenario Testing

1. **Test Invalid Webhook URL**
   - Configure a destination with invalid webhook URL
   - Attempt to publish
   - Verify error handling and user feedback

2. **Test Network Failures**
   - Temporarily disable internet connection
   - Attempt to publish
   - Verify graceful error handling

3. **Test Database Failures**
   - Temporarily stop Supabase (if local)
   - Verify fallback to in-memory storage
   - Check console logs for appropriate warnings

### Phase 5: UI Component Testing

1. **Publishing Modal Functionality**
   - Test modal open/close behavior
   - Verify destination selection (checkboxes)
   - Test publishing options toggles
   - Verify preview functionality
   - Test form validation

2. **Responsive Design**
   - Test modal on different screen sizes
   - Verify mobile responsiveness
   - Check accessibility features

3. **Integration with Research Display**
   - Verify publish button appears after research completion
   - Test button styling and positioning
   - Verify modal receives correct research data

### Phase 6: Analytics Verification

1. **Check Publishing Analytics**
   ```sql
   -- View publishing attempts
   SELECT 
     pa.*,
     pd.name as destination_name,
     pd.type as destination_type
   FROM publishing_analytics pa
   JOIN publishing_destinations pd ON pa.destination_id = pd.id
   ORDER BY pa.published_at DESC;
   ```

2. **Verify Data Integrity**
   - Check that research data hashes are consistent
   - Verify status tracking (success/failed/pending)
   - Confirm response data is stored properly

## Expected Results

### Successful Test Completion Should Show:

1. **Database Tables**
   - `publishing_destinations` table with at least one Slack destination
   - `publishing_analytics` table ready to receive data

2. **UI Integration**
   - Publish button visible in research results
   - Publishing modal opens and functions correctly
   - All destination types shown (enabled and disabled)

3. **Workflow Integration**
   - 7-step orchestrator process completes successfully
   - Publishing step shows appropriate status
   - Manual publishing works end-to-end

4. **Slack Integration**
   - Messages delivered to configured Slack channel
   - Message formatting includes research data
   - Error handling works for invalid configurations

5. **Analytics Tracking**
   - Publishing attempts logged to database
   - Success/failure status tracked correctly
   - Response data captured for debugging

## Troubleshooting

### Common Issues:

1. **"No publishing destinations configured"**
   - Verify database migration ran successfully
   - Check if default destination was created
   - Manually create destination via API

2. **"Publishing check failed"**
   - Check Supabase connection
   - Verify environment variables
   - Check console logs for specific errors

3. **Slack webhook failures**
   - Verify webhook URL is correct and active
   - Check Slack app permissions
   - Test webhook URL directly with curl

4. **Modal not opening**
   - Check browser console for JavaScript errors
   - Verify research data is properly formatted
   - Check component imports and dependencies

### Debug Commands:

```bash
# Check build for errors
pnpm run build

# Check TypeScript compilation
pnpm run check

# View server logs
tail -f .svelte-kit/output/server/index.js

# Test API endpoints directly
curl http://localhost:5173/api/publishing/destinations
curl http://localhost:5173/api/publishing/publish -X POST -H "Content-Type: application/json" -d '{...}'
```

## Success Criteria

The publishing system integration is considered successful when:

- [ ] Database migration completes without errors
- [ ] Publishing destinations can be created and managed
- [ ] Orchestrator workflow includes publishing as 7th step
- [ ] Publishing modal integrates seamlessly with research display
- [ ] Slack publishing works end-to-end
- [ ] Error scenarios are handled gracefully
- [ ] Analytics data is captured correctly
- [ ] Build completes without compilation errors
- [ ] All UI components are responsive and accessible

This completes the comprehensive testing workflow for the publishing system integration.
