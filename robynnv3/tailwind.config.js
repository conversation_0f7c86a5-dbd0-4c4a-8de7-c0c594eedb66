import daisyui from "daisyui"

/** @type {import('tailwindcss').Config} */
export default {
  content: ["./src/**/*.{html,js,svelte,ts}"],
  theme: {
    extend: {
      fontFamily: {
        sans: [
          '"Plus Jakarta Sans"',
          "system-ui",
          "-apple-system",
          "BlinkMacSystemFont",
          '"Segoe UI"',
          "Roboto",
          '"Helvetica Neue"',
          "Arial",
          "sans-serif",
        ],
        serif: [
          "Merriweather",
          "ui-serif",
          "Georgia",
          "Cambria",
          '"Times New Roman"',
          "Times",
          "serif",
        ],
        mono: [
          "ui-monospace",
          "SFMono-Regular",
          "Menlo",
          "Monaco",
          "Consolas",
          '"Liberation Mono"',
          '"Courier New"',
          "monospace",
        ],
      },
    },
  },
  plugins: [daisyui],
  daisyui: {
    themes: [
      {
        professional: {
          primary: "hsl(var(--primary))",
          "primary-content": "hsl(var(--primary-foreground))",
          secondary: "hsl(var(--secondary))",
          "secondary-content": "hsl(var(--secondary-foreground))",
          accent: "hsl(var(--accent))",
          "accent-content": "hsl(var(--accent-foreground))",
          neutral: "hsl(var(--muted))",
          "neutral-content": "hsl(var(--muted-foreground))",
          "base-100": "hsl(var(--background))",
          "base-200": "hsl(var(--card))",
          "base-300": "hsl(var(--muted))",
          "base-content": "hsl(var(--foreground))",
          info: "hsl(220 85% 57%)",
          "info-content": "hsl(0 0% 100%)",
          success: "hsl(142 76% 36%)",
          "success-content": "hsl(0 0% 100%)",
          warning: "hsl(43 96% 56%)",
          "warning-content": "hsl(0 0% 0%)",
          error: "hsl(var(--destructive))",
          "error-content": "hsl(var(--destructive-foreground))",
          "--rounded-box": "var(--radius)",
          "--rounded-btn": "var(--radius)",
          "--rounded-badge": "var(--radius)",
          "--animation-btn": "0.25s",
          "--animation-input": "0.2s",
          "--btn-focus-scale": "0.95",
          "--border-btn": "1px",
          "--tab-border": "1px",
          "--tab-radius": "var(--radius)",
        },
        neo: {
          primary: "hsl(var(--primary))",
          "primary-content": "hsl(var(--primary-foreground))",
          secondary: "hsl(var(--secondary))",
          "secondary-content": "hsl(var(--secondary-foreground))",
          accent: "hsl(var(--accent))",
          "accent-content": "hsl(var(--accent-foreground))",
          neutral: "hsl(var(--muted))",
          "neutral-content": "hsl(var(--muted-foreground))",
          "base-100": "hsl(var(--background))",
          "base-200": "hsl(var(--card))",
          "base-300": "hsl(var(--muted))",
          "base-content": "hsl(var(--foreground))",
          info: "hsl(216 100% 50%)",
          "info-content": "hsl(0 0% 100%)",
          success: "hsl(120 100% 40%)",
          "success-content": "hsl(0 0% 100%)",
          warning: "hsl(60 100% 50%)",
          "warning-content": "hsl(0 0% 0%)",
          error: "hsl(var(--destructive))",
          "error-content": "hsl(var(--destructive-foreground))",
          "--rounded-box": "0px",
          "--rounded-btn": "0px",
          "--rounded-badge": "0px",
          "--animation-btn": "0.1s",
          "--animation-input": "0.1s",
          "--btn-focus-scale": "0.98",
          "--border-btn": "2px",
          "--tab-border": "2px",
          "--tab-radius": "0px",
        },
      },
    ],
    darkTheme: false, // We handle dark mode through CSS variables
    base: true,
    styled: true,
    utils: true,
    prefix: "",
  },
}
