#!/usr/bin/env node

/**
 * Tool Testing Script
 * 
 * This script tests each Mastra tool individually to identify which ones
 * have syntax errors and which ones work correctly.
 */

import fs from 'fs/promises'
import path from 'path'
import { fileURLToPath } from 'url'
import { exec } from 'child_process'
import { promisify } from 'util'

const execAsync = promisify(exec)

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)
const projectRoot = path.join(__dirname, '../..')

// Paths
const MASTRA_TOOLS_DIR = path.join(projectRoot, 'src/mastra/tools')
const WORKING_TOOLS_DIR = path.join(projectRoot, 'src/mastra/tools/working')
const BROKEN_TOOLS_DIR = path.join(projectRoot, 'src/mastra/tools/broken')

/**
 * Test a JavaScript file for syntax errors using Node.js
 */
async function testToolSyntax(filePath) {
  try {
    // Use node --check to validate syntax without executing
    await execAsync(`node --check "${filePath}"`)
    return { success: true, error: null }
  } catch (error) {
    return { success: false, error: error.message }
  }
}

/**
 * Get all tool files
 */
async function getToolFiles() {
  const files = await fs.readdir(MASTRA_TOOLS_DIR)
  return files.filter(f => 
    f.endsWith('.js') && 
    f !== 'index.js' && 
    !f.includes('simple-web-search') // Keep the manual one
  )
}

/**
 * Test all tools and categorize them
 */
async function testAllTools() {
  console.log('🧪 Testing all Mastra tools for syntax errors...\n')
  
  const toolFiles = await getToolFiles()
  const workingTools = []
  const brokenTools = []
  
  // Ensure directories exist
  await fs.mkdir(WORKING_TOOLS_DIR, { recursive: true })
  await fs.mkdir(BROKEN_TOOLS_DIR, { recursive: true })
  
  for (const filename of toolFiles) {
    const filePath = path.join(MASTRA_TOOLS_DIR, filename)
    
    console.log(`Testing ${filename}...`)
    const result = await testToolSyntax(filePath)
    
    if (result.success) {
      console.log(`  ✅ ${filename} - WORKING`)
      workingTools.push(filename)
      
      // Move to working directory
      const workingPath = path.join(WORKING_TOOLS_DIR, filename)
      await fs.copyFile(filePath, workingPath)
      
    } else {
      console.log(`  ❌ ${filename} - BROKEN`)
      console.log(`     Error: ${result.error.split('\n')[0]}`)
      brokenTools.push({ filename, error: result.error })
      
      // Move to broken directory
      const brokenPath = path.join(BROKEN_TOOLS_DIR, filename)
      await fs.copyFile(filePath, brokenPath)
    }
  }
  
  return { workingTools, brokenTools }
}

/**
 * Generate an index file with only working tools
 */
async function generateWorkingToolsIndex(workingTools) {
  const imports = []
  const exports = []
  
  // Always include the manual simple web search tool
  imports.push(`export { simpleWebSearchTool } from '../simple-web-search.js'`)
  
  for (const filename of workingTools) {
    try {
      const filePath = path.join(WORKING_TOOLS_DIR, filename)
      const content = await fs.readFile(filePath, 'utf-8')
      
      // Extract tool exports
      const exportMatches = content.match(/export\s+const\s+(\w+Tool)\s*=/g) || []
      
      for (const exportMatch of exportMatches) {
        const toolName = exportMatch.replace(/export\s+const\s+(\w+Tool)\s*=/, '$1')
        const importName = filename.replace('.js', '')
        
        imports.push(`export { ${toolName} } from './working/${filename}'`)
      }
    } catch (error) {
      console.warn(`Warning: Could not process ${filename}: ${error.message}`)
    }
  }
  
  const indexContent = `// Auto-generated working tools index
// This file includes only tools that pass syntax validation

${imports.join('\n')}
`
  
  const indexPath = path.join(MASTRA_TOOLS_DIR, 'index.js')
  await fs.writeFile(indexPath, indexContent, 'utf-8')
  
  console.log(`\n✅ Generated working tools index with ${imports.length} tools`)
}

/**
 * Generate a summary report
 */
function generateReport(workingTools, brokenTools) {
  console.log(`\n📊 Tool Testing Summary:`)
  console.log(`   Working tools: ${workingTools.length}`)
  console.log(`   Broken tools: ${brokenTools.length}`)
  console.log(`   Success rate: ${((workingTools.length / (workingTools.length + brokenTools.length)) * 100).toFixed(1)}%`)
  
  if (workingTools.length > 0) {
    console.log(`\n✅ Working tools:`)
    workingTools.forEach(tool => console.log(`   - ${tool}`))
  }
  
  if (brokenTools.length > 0) {
    console.log(`\n❌ Broken tools:`)
    brokenTools.forEach(({ filename, error }) => {
      console.log(`   - ${filename}: ${error.split('\n')[0]}`)
    })
  }
  
  console.log(`\n📂 Files organized:`)
  console.log(`   Working: ${WORKING_TOOLS_DIR}`)
  console.log(`   Broken: ${BROKEN_TOOLS_DIR}`)
}

/**
 * Main function
 */
async function main() {
  try {
    const { workingTools, brokenTools } = await testAllTools()
    await generateWorkingToolsIndex(workingTools)
    generateReport(workingTools, brokenTools)
    
    console.log(`\n🎯 Next steps:`)
    console.log(`   1. Run "pnpm run mastra:dev" to test with working tools only`)
    console.log(`   2. Fix broken tools in ${BROKEN_TOOLS_DIR}`)
    console.log(`   3. Move fixed tools back to working directory`)
    
  } catch (error) {
    console.error('❌ Tool testing failed:', error.message)
    process.exit(1)
  }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main()
}

export { testAllTools, generateWorkingToolsIndex }
