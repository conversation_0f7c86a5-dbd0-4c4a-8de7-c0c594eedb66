#!/usr/bin/env node

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// Path configurations
const SVELTE_TOOLS_PATH = path.join(__dirname, '../lib/agents/tools')
const MASTRA_TOOLS_PATH = path.join(__dirname, '../mastra/tools')
const REGISTRY_PATH = path.join(__dirname, '../mastra/registry.ts')

console.log('🔍 Validating Mastra sync...')

let hasErrors = false

// Check if sync has been run
function validateSyncExists() {
  console.log('\n📁 Checking if sync has been run...')
  
  if (!fs.existsSync(MASTRA_TOOLS_PATH)) {
    console.error('❌ Mastra tools directory does not exist. Run "pnpm run sync:mastra" first.')
    hasErrors = true
    return false
  }
  
  const mastraFiles = fs.readdirSync(MASTRA_TOOLS_PATH).filter(f => f.endsWith('.js'))
  if (mastraFiles.length === 0) {
    console.error('❌ No Mastra tools found. Run "pnpm run sync:mastra" first.')
    hasErrors = true
    return false
  }
  
  console.log(`✅ Found ${mastraFiles.length} Mastra tool(s)`)
  return true
}

// Validate that Mastra tools don't contain TypeScript syntax
function validateJavaScriptSyntax() {
  console.log('\n🔧 Checking for TypeScript syntax in Mastra tools...')
  
  const mastraFiles = fs.readdirSync(MASTRA_TOOLS_PATH)
    .filter(f => f.endsWith('.js'))
    .map(f => path.join(MASTRA_TOOLS_PATH, f))
  
  for (const filePath of mastraFiles) {
    const content = fs.readFileSync(filePath, 'utf8')
    const fileName = path.basename(filePath)
    
    // Check for TypeScript syntax patterns
    const tsPatterns = [
      /function\s+\w+<[^>]+>/,  // Generic functions
      /async\s+function\s+\w+<[^>]+>/,  // Async generic functions
      /:\s*Promise<[^>]+>/,  // Promise type annotations
      /:\s*\w+<[^>]+>/,  // Generic type annotations
      /<[^>]+>\s*\(/,  // Generic function calls
      /interface\s+\w+/,  // Interface declarations
      /type\s+\w+\s*=/,  // Type aliases
      /\s+as\s+\w+/,  // Type assertions
      /\w+!/,  // Non-null assertion operator
      /\w+\?:/,  // Optional parameters
    ]
    
    for (const pattern of tsPatterns) {
      if (pattern.test(content)) {
        console.error(`❌ ${fileName} contains TypeScript syntax: ${pattern}`)
        hasErrors = true
      }
    }
  }
  
  if (!hasErrors) {
    console.log('✅ All Mastra tools use valid JavaScript syntax')
  }
}

// Validate that required dependencies are available
function validateDependencies() {
  console.log('\n📦 Checking for SvelteKit dependencies in Mastra tools...')
  
  const mastraFiles = fs.readdirSync(MASTRA_TOOLS_PATH)
    .filter(f => f.endsWith('.js'))
    .map(f => path.join(MASTRA_TOOLS_PATH, f))
  
  const forbiddenImports = [
    '$env/',
    '$lib/',
    '$app/',
    'svelte',
    '@sveltejs/',
  ]
  
  for (const filePath of mastraFiles) {
    const content = fs.readFileSync(filePath, 'utf8')
    const fileName = path.basename(filePath)
    
    for (const forbidden of forbiddenImports) {
      if (content.includes(forbidden)) {
        console.error(`❌ ${fileName} contains SvelteKit import: ${forbidden}`)
        hasErrors = true
      }
    }
  }
  
  if (!hasErrors) {
    console.log('✅ No SvelteKit dependencies found in Mastra tools')
  }
}

// Validate registry exists and is up to date
function validateRegistry() {
  console.log('\n📋 Checking registry...')
  
  if (!fs.existsSync(REGISTRY_PATH)) {
    console.warn('⚠️ Registry file does not exist. Run "pnpm run sync:registry" to generate it.')
    return
  }
  
  const registryContent = fs.readFileSync(REGISTRY_PATH, 'utf8')
  const mastraFiles = fs.readdirSync(MASTRA_TOOLS_PATH)
    .filter(f => f.endsWith('.js'))
    .map(f => f.replace('.js', ''))
  
  for (const toolName of mastraFiles) {
    if (!registryContent.includes(toolName)) {
      console.warn(`⚠️ Tool ${toolName} not found in registry. Run "pnpm run sync:registry" to update.`)
    }
  }
  
  console.log('✅ Registry validation complete')
}

// Main validation
async function main() {
  try {
    if (!validateSyncExists()) {
      process.exit(1)
    }
    
    validateJavaScriptSyntax()
    validateDependencies()
    validateRegistry()
    
    if (hasErrors) {
      console.error('\n❌ Validation failed with errors. Please fix the issues above.')
      process.exit(1)
    } else {
      console.log('\n✅ All validations passed! Mastra sync is working correctly.')
    }
  } catch (error) {
    console.error('💥 Validation error:', error)
    process.exit(1)
  }
}

main()
