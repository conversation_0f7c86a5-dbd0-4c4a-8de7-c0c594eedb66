#!/usr/bin/env node

/**
 * Registry Generation Script
 * 
 * This script automatically generates the Mastra registry file from tools and agents
 * by scanning the generated Mastra tools and creating appropriate imports and exports.
 * 
 * Usage: node src/scripts/update-registry.js
 */

import fs from 'fs/promises'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)
const projectRoot = path.join(__dirname, '../..')

// Paths
const MASTRA_TOOLS_DIR = path.join(projectRoot, 'src/mastra/tools')
const MASTRA_AGENTS_DIR = path.join(projectRoot, 'src/mastra/agents')
const REGISTRY_PATH = path.join(projectRoot, 'src/mastra/registry.ts')

/**
 * Extract tool metadata from JavaScript files
 */
async function extractToolMetadata(toolsDir) {
  const tools = []
  
  try {
    const files = await fs.readdir(toolsDir)
    const toolFiles = files.filter(f => f.endsWith('.js') && f !== 'index.js')
    
    for (const filename of toolFiles) {
      const filePath = path.join(toolsDir, filename)
      const content = await fs.readFile(filePath, 'utf-8')
      
      // Extract tool exports and metadata
      const exportMatches = content.match(/export\s+const\s+(\w+Tool)\s*=/g) || []
      const descriptionMatch = content.match(/description:\s*['"`]([^'"`]+)['"`]/)
      const idMatch = content.match(/id:\s*['"`]([^'"`]+)['"`]/)
      
      for (const exportMatch of exportMatches) {
        const toolName = exportMatch.replace(/export\s+const\s+(\w+Tool)\s*=/, '$1')
        const toolId = idMatch ? idMatch[1] : filename.replace('.js', '').replace('-tool', '')
        const description = descriptionMatch ? descriptionMatch[1] : `Tool from ${filename}`
        
        tools.push({
          name: toolName,
          id: toolId,
          filename: filename.replace('.js', ''),
          description,
          category: inferCategory(filename, description)
        })
      }
    }
  } catch (error) {
    console.warn(`Warning: Could not read tools directory: ${error.message}`)
  }
  
  return tools
}

/**
 * Infer tool category from filename and description
 */
function inferCategory(filename, description) {
  const name = filename.toLowerCase()
  const desc = description.toLowerCase()
  
  if (name.includes('apollo') || desc.includes('contact') || desc.includes('lead')) {
    return 'Lead Generation'
  }
  if (name.includes('search') || name.includes('exa') || name.includes('brave') || desc.includes('search')) {
    return 'Search & Discovery'
  }
  if (name.includes('firecrawl') || name.includes('scrape') || desc.includes('crawl') || desc.includes('scrape')) {
    return 'Web Scraping'
  }
  if (name.includes('content') || name.includes('grammar') || name.includes('text') || desc.includes('content') || desc.includes('text')) {
    return 'Content & Writing'
  }
  if (name.includes('keyword') || name.includes('seo') || desc.includes('keyword') || desc.includes('seo')) {
    return 'SEO & Keywords'
  }
  if (name.includes('serp') || desc.includes('serp') || desc.includes('ranking')) {
    return 'SERP Analysis'
  }
  
  return 'Utility'
}

/**
 * Extract agent metadata from agent files
 */
async function extractAgentMetadata(agentsDir) {
  const agents = []
  
  try {
    await fs.access(agentsDir)
    const files = await fs.readdir(agentsDir)
    const agentFiles = files.filter(f => f.endsWith('.js') && f !== 'index.js')
    
    for (const filename of agentFiles) {
      const filePath = path.join(agentsDir, filename)
      const content = await fs.readFile(filePath, 'utf-8')
      
      const exportMatches = content.match(/export\s+const\s+(\w+Agent)\s*=/g) || []
      const nameMatch = content.match(/name:\s*['"`]([^'"`]+)['"`]/)
      const descriptionMatch = content.match(/description:\s*['"`]([^'"`]+)['"`]/)
      
      for (const exportMatch of exportMatches) {
        const agentName = exportMatch.replace(/export\s+const\s+(\w+Agent)\s*=/, '$1')
        const name = nameMatch ? nameMatch[1] : agentName.replace('Agent', '')
        const description = descriptionMatch ? descriptionMatch[1] : `Agent from ${filename}`
        
        agents.push({
          name: agentName,
          displayName: name,
          filename: filename.replace('.js', ''),
          description
        })
      }
    }
  } catch (error) {
    console.warn(`Warning: Could not read agents directory: ${error.message}`)
  }
  
  return agents
}

/**
 * Generate the registry TypeScript file
 */
function generateRegistryContent(tools, agents) {
  const toolImports = tools.map(tool => 
    `import { ${tool.name} } from './tools/${tool.filename}.js'`
  ).join('\n')
  
  const agentImports = agents.length > 0 ? agents.map(agent => 
    `import { ${agent.name} } from './agents/${agent.filename}.js'`
  ).join('\n') : ''
  
  const toolExports = tools.map(tool => `  ${tool.name}`).join(',\n')
  const agentExports = agents.length > 0 ? agents.map(agent => `  ${agent.name}`).join(',\n') : ''
  
  const toolCategories = {}
  tools.forEach(tool => {
    if (!toolCategories[tool.category]) {
      toolCategories[tool.category] = []
    }
    toolCategories[tool.category].push(tool)
  })
  
  const categoriesContent = Object.entries(toolCategories)
    .map(([category, categoryTools]) => 
      `  '${category}': [\n${categoryTools.map(tool => `    ${tool.name}`).join(',\n')}\n  ]`
    ).join(',\n')
  
  return `// Auto-generated Mastra registry
// This file is automatically generated by src/scripts/update-registry.js
// Do not edit manually - changes will be overwritten

${toolImports}
${agentImports ? '\n' + agentImports : ''}

// Tool exports
export const tools = [
${toolExports}
]

${agents.length > 0 ? `// Agent exports
export const agents = [
${agentExports}
]

` : ''}// Tools organized by category
export const toolsByCategory = {
${categoriesContent}
}

// Tool metadata for dashboard
export const toolMetadata = {
${tools.map(tool => 
  `  '${tool.id}': {
    name: '${tool.name}',
    id: '${tool.id}',
    description: '${tool.description}',
    category: '${tool.category}'
  }`
).join(',\n')}
}

${agents.length > 0 ? `// Agent metadata for dashboard
export const agentMetadata = {
${agents.map(agent => 
  `  '${agent.name}': {
    name: '${agent.name}',
    displayName: '${agent.displayName}',
    description: '${agent.description}'
  }`
).join(',\n')}
}

` : ''}// All available tools and agents
export default {
  tools,
${agents.length > 0 ? '  agents,' : ''}
  toolsByCategory,
  toolMetadata${agents.length > 0 ? ',\n  agentMetadata' : ''}
}
`
}

/**
 * Main registry generation function
 */
async function generateRegistry() {
  console.log('🔄 Generating Mastra registry...\n')
  
  try {
    // Extract tool and agent metadata
    console.log('📄 Scanning tools...')
    const tools = await extractToolMetadata(MASTRA_TOOLS_DIR)
    console.log(`✅ Found ${tools.length} tool(s)`)
    
    console.log('📄 Scanning agents...')
    const agents = await extractAgentMetadata(MASTRA_AGENTS_DIR)
    console.log(`✅ Found ${agents.length} agent(s)`)
    
    // Generate registry content
    console.log('\n📝 Generating registry content...')
    const registryContent = generateRegistryContent(tools, agents)
    
    // Write registry file
    await fs.writeFile(REGISTRY_PATH, registryContent, 'utf-8')
    
    console.log(`✅ Registry generated successfully!`)
    console.log(`📂 Registry file: ${REGISTRY_PATH}`)
    
    // Summary
    console.log(`\n📊 Registry Summary:`)
    console.log(`   Tools: ${tools.length}`)
    console.log(`   Agents: ${agents.length}`)
    
    const categories = [...new Set(tools.map(t => t.category))]
    console.log(`   Categories: ${categories.join(', ')}`)
    
  } catch (error) {
    console.error('❌ Registry generation failed:', error.message)
    process.exit(1)
  }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  generateRegistry()
}

export { generateRegistry }
