#!/usr/bin/env node

/**
 * Mastra Sync Script
 * 
 * This script automatically generates pure Mastra tools from SvelteKit tools
 * by replacing SvelteKit-specific imports with Node.js equivalents.
 * 
 * Usage: node src/scripts/sync-mastra.js
 */

import fs from 'fs/promises'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)
const projectRoot = path.join(__dirname, '../..')

// Paths
const SVELTEKIT_TOOLS_DIR = path.join(projectRoot, 'src/lib/agents/tools')
const SVELTEKIT_AGENTS_DIR = path.join(projectRoot, 'src/lib/agents')
const MASTRA_TOOLS_DIR = path.join(projectRoot, 'src/mastra/tools')
const MASTRA_AGENTS_DIR = path.join(projectRoot, 'src/mastra/agents')
const MASTRA_SHARED_DIR = path.join(projectRoot, 'src/mastra/shared')

// Transform patterns for SvelteKit -> Mastra conversion
const TRANSFORM_PATTERNS = [
  // Special case for llm-providers.ts function declaration
  {
    pattern: /export function createLLMClient\(config:\s*LLMConfig\)\s*\{/g,
    replacement: 'export function createLLMClient(config) {'
  },
  // Agent-specific imports: fix llm-providers import
  {
    pattern: /import\s+\{([^}]*),\s*type\s+LLMConfig\s*\}\s+from\s+['"](\.\/llm-providers|llm-providers)['"]/g,
    replacement: 'import { $1 } from "../shared/llm-providers.js"'
  },
  {
    pattern: /import\s+\{\s*createLLMClient\s*,\s*type\s+LLMConfig\s*\}\s+from\s+['"](\.\/llm-providers|llm-providers)['"]/g,
    replacement: 'import { createLLMClient } from "../shared/llm-providers.js"'
  },
  {
    pattern: /import\s+\{\s*createLLMClient\s*\}\s+from\s+['"](\.\/llm-providers|llm-providers)['"]/g,
    replacement: 'import { createLLMClient } from "../shared/llm-providers.js"'
  },
  // Agent-specific imports: fix tool imports
  {
    pattern: /import\s+\{([^}]+)\}\s+from\s+['"]\.\/tools\/([^'"]+)['"]/g,
    replacement: 'import { $1 } from "../tools/$2.js"'
  },
  // Environment imports
  {
    pattern: /import\s+\{\s*env\s*\}\s+from\s+['"]\$env\/dynamic\/private['"]/g,
    replacement: "import { ENV_VARS } from '../shared/env.js'"
  },
  // Environment usage
  {
    pattern: /env\.([A-Z_]+)/g,
    replacement: 'ENV_VARS.$1()'
  },
  // Lib imports - schemas
  {
    pattern: /import\s+\{([^}]+)\}\s+from\s+['"]\$lib\/schemas\/([^'"]+)['"]/g,
    replacement: "import {$1} from '../shared/schemas.js'"
  },
  // Lib imports - types (remove, types not needed in JS)
  {
    pattern: /import\s+\{([^}]+)\}\s+from\s+['"]\$lib\/types\/([^'"]+)['"]/g,
    replacement: '// Types removed for JavaScript'
  },
  // Remove type-only imports
  {
    pattern: /import\s+type\s+\{[^}]+\}\s+from\s+['"][^'"]+['"]/g,
    replacement: '// Type imports removed for JavaScript'
  },
  // Lib imports - utils (inline for Firecrawl)
  {
    pattern: /import\s+\{([^}]+)\}\s+from\s+['"]\$lib\/utils\/firecrawl-utils['"]/g,
    replacement: '// Inlined utils - see generated code below'
  },
  // Remove SvelteKit-specific imports that aren't needed
  {
    pattern: /import[^;]+\$lib\/utils\/[^;]+;?\s*/g,
    replacement: ''
  },
  // Remove any remaining $lib imports
  {
    pattern: /import[^;]+\$lib\/[^;]+;?\s*/g,
    replacement: ''
  },
  // Remove generic type parameters from function definitions
  {
    pattern: /async\s+function\s+(\w+)<[^>]+>\s*\(/g,
    replacement: 'async function $1('
  },
  {
    pattern: /function\s+(\w+)<[^>]+>\s*\(/g,
    replacement: 'function $1('
  },
  // Remove TypeScript type annotations in variable declarations (but not object properties)
  {
    pattern: /^(\s*const\s+\w+)\s*:\s*[A-Za-z_][A-Za-z0-9_]*(<[^>]+>)?\s*=/gm,
    replacement: '$1 ='
  },
  {
    pattern: /^(\s*let\s+\w+)\s*:\s*[A-Za-z_][A-Za-z0-9_]*(<[^>]+>)?\s*=/gm,
    replacement: '$1 ='
  },
  {
    pattern: /^(\s*var\s+\w+)\s*:\s*[A-Za-z_][A-Za-z0-9_]*(<[^>]+>)?\s*=/gm,
    replacement: '$1 ='
  },
  // Agent-specific: fix function parameter with optional type annotation
  {
    pattern: /export\s+function\s+(\w+)\s*\(\s*(\w+)\s*\?\s*:\s*[^)]+\s*\)/g,
    replacement: 'export function $1($2)'
  },
  {
    pattern: /function\s+(\w+)\s*\(\s*(\w+)\s*\?\s*:\s*[^)]+\s*\)/g,
    replacement: 'function $1($2)'
  },
  // Remove function parameter type annotations
  {
    pattern: /\(\s*\{([^}]+)\}\s*:\s*[^)]+\)\s*=>/g,
    replacement: '({ $1 }) =>'
  },
  // Remove optional parameter markers (?)
  {
    pattern: /(\w+)\?\s*:/g,
    replacement: '$1:'
  },
  {
    pattern: /(\w+)\?\s*\)/g,
    replacement: '$1)'
  },
  {
    pattern: /(\w+)\?\s*,/g,
    replacement: '$1,'
  },
  // Remove function parameter types (like operation: () => Promise<T>)
  {
    pattern: /(\w+):\s*\(\s*\)\s*=>\s*Promise<[^>]+>/g,
    replacement: '$1'
  },
  // Remove function parameter types (like context: string)
  {
    pattern: /(\w+):\s*(string|number|boolean|object|any|unknown)/g,
    replacement: '$1'
  },
  // Remove function parameter types with custom type names (like provider: LLMProvider)
  {
    pattern: /(\w+):\s*[A-Z][A-Za-z0-9_]*\s*\)/g,
    replacement: '$1)'
  },
  {
    pattern: /(\w+):\s*[A-Z][A-Za-z0-9_]*\s*,/g,
    replacement: '$1,'
  },
  // Fix function parameter array type annotations (like results[]: type)
  {
    pattern: /(\w+)\[\]\s*:\s*(string|number|boolean|object|any|unknown)/g,
    replacement: '$1'
  },
  // Fix function parameter type annotations (like results[], type): return
  {
    pattern: /(\w+)\[\],\s*(\w+)\)\s*:\s*(string|number|boolean|object|any|unknown)/g,
    replacement: '$1, $2)'
  },
  // Fix function parameter with array type followed by return type annotation
  {
    pattern: /(\w+):\s*any\[\],\s*(\w+):\s*(string|number|boolean|object|any|unknown)\)\s*:\s*any\[\]/g,
    replacement: '$1, $2)'
  },
  // Fix malformed bracket expressions with type names
  {
    pattern: /result\[key\s+[A-Za-z_][A-Za-z0-9_]*\]/g,
    replacement: 'result[key]'
  },
  // Fix specific firecrawl malformed syntax
  {
    pattern: /result\[key\s+FirecrawlScrapeResult\]/g,
    replacement: 'result[key]'
  },
  // Fix TypeScript 'as keyof Type' syntax
  {
    pattern: /(\w+)\s+as\s+keyof\s+[A-Za-z_][A-Za-z0-9_]*/g,
    replacement: '$1'
  },
  // Fix TypeScript 'as "literal"' syntax
  {
    pattern: /(\w+\(\))\s+as\s+["'][^"']+["']/g,
    replacement: '$1'
  },
  // Fix TypeScript 'as Type' syntax (general) - complex union types
  {
    pattern: /(\w+\(\))\s+as\s+"[^"]*"\s*\|\s*"[^"]*"\s*\|\s*"[^"]*"/g,
    replacement: '$1'
  },
  // Fix malformed pipe syntax from failed transformation
  {
    pattern: /(\w+\(\))\s+\|\s*"[^"]*"\s*\|\s*"[^"]*"/g,
    replacement: '$1'
  },
  // Remove complex generic type annotations like Record<A, B>
  {
    pattern: /:\s*Record<[^>]+>\s*=/g,
    replacement: ' ='
  },
  // Remove function parameter type annotations - be more specific
  {
    pattern: /^(\s*export\s+function\s+\w+\s*\(\s*\w+)\s*:\s*[A-Za-z_][A-Za-z0-9_]*(\s*\)\s*\{)/gm,
    replacement: '$1$2'
  },
  // Handle export function with custom type parameter (like config: LLMConfig)
  {
    pattern: /^(\s*export\s+function\s+\w+\s*\(\s*\w+)\s*:\s*[A-Z][A-Za-z0-9_]*(\s*\)\s*\{)/gm,
    replacement: '$1$2'
  },
  // Remove explicit type declarations but preserve array brackets
  {
    pattern: /:\s*(string|number|boolean|object|any|unknown)\[\]\s*=/g,
    replacement: ' = []'
  },
  {
    pattern: /:\s*(string|number|boolean|object|any|unknown)\s*=/g,
    replacement: ' ='
  },
  {
    pattern: /:\s*(string|number|boolean|object|any|unknown)\s*[;,}\]]/g,
    replacement: '$1'
  },
  // Remove interface/type definitions (only at start of lines, not in comments)
  {
    pattern: /^\s*interface\s+\w+\s*\{[^}]*\}\s*/gm,
    replacement: ''
  },
  {
    pattern: /^\s*export\s+interface\s+\w+\s*\{[^}]*\}\s*/gm,
    replacement: ''
  },
  {
    pattern: /^\s*type\s+\w+\s*=\s*\{[^}]*\}\s*/gm,
    replacement: ''
  },
  {
    pattern: /^\s*export\s+type\s+\w+\s*=\s*[^;]+;?\s*/gm,
    replacement: ''
  },
  // Remove z.infer type annotations
  {
    pattern: /z\.infer<typeof\s+\w+>/g,
    replacement: 'any'
  },
  // Remove inline type annotations (like in map callbacks)
  {
    pattern: /\(\s*(\w+)\s*:\s*any\s*\)/g,
    replacement: '($1)'
  },
  // Remove return type annotations from functions (including Promise<T>)
  {
    pattern: /\)\s*:\s*Promise<[^>]+>\s*\{/g,
    replacement: ') {'
  },
  {
    pattern: /\)\s*:\s*[A-Za-z_][A-Za-z0-9_<>|,\s]*\s*=>/g,
    replacement: ') =>'
  },
  // Fix function signature with array return type (like ): type[] { becomes ) { )
  {
    pattern: /\)\s*:\s*[A-Za-z_][A-Za-z0-9_<>|,\s]*\[\]\s*\{/g,
    replacement: ') {'
  },
  // Fix multiline function signatures with return type annotations
  {
    pattern: /\)\s*:\s*[A-Za-z_][A-Za-z0-9_<>|,\s]*\s*\{/gm,
    replacement: ') {'
  },
  // Fix function parameters ending with return type on next line
  {
    pattern: /\)\s*\n\s*:\s*[A-Za-z_][A-Za-z0-9_<>|,\s]*\s*\{/g,
    replacement: ') {'
  },
  // Remove type annotations after variable names
  {
    pattern: /(\w+):\s*Error\s*$/gm,
    replacement: '$1'
  },
  // Remove Promise<T> return type annotations specifically
  {
    pattern: /:\s*Promise<[^>]+>\s*\{/g,
    replacement: ' {'
  },
  // Remove generic type parameters from Set/Map/etc.
  {
    pattern: /new\s+(Set|Map|Array)<[^>]+>\(/g,
    replacement: 'new $1('
  },
  // Remove type parameters in variable declarations
  {
    pattern: /=\s*new\s+(Set|Map|Array)<[^>]+>\(/g,
    replacement: '= new $1('
  },
  // Remove TypeScript 'as' type assertions
  {
    pattern: /\s+as\s+\w+/g,
    replacement: ''
  },
  {
    pattern: /\s+as\s+\w+\[\]/g,
    replacement: ''
  },
  // Remove TypeScript non-null assertion operator (but not logical NOT)
  {
    pattern: /(\w+)!/g,
    replacement: '$1'
  },
  // Fix common transformation issues
  {
    pattern: /let\s+(\w+)\[\]\s*=/g,
    replacement: 'let $1 ='
  },
  {
    pattern: /const\s+(\w+)\[\]\s*=/g,
    replacement: 'const $1 ='
  },
  // Fix broken if statements from type removal
  {
    pattern: /if\s+\(\s*search_\s+===/g,
    replacement: 'if (search_type ==='
  },
  // Fix function signature with trailing array type annotation (after parameters)
  {
    pattern: /\)\[\]\s*\{/g,
    replacement: ') {'
  },
  // Remove generic type annotations on object literals
  {
    pattern: /\{\}<[^>]+>/g,
    replacement: '{}'
  }
]

// Tool metadata extraction patterns
const METADATA_PATTERNS = {
  toolId: /export\s+const\s+(\w+Tool)\s*=/,
  createTool: /createTool\s*\(\s*\{([^}]+)\}/s,
  schema: /schema:\s*(\w+Schema)/,
  name: /name:\s*['"`]([^'"`]+)['"`]/,
  description: /description:\s*['"`]([^'"`]+)['"`]/
}

/**
 * Transform SvelteKit tool code to Mastra-compatible code
 */
function transformToolCode(code, filename) {
  let transformed = code

  // Apply transformation patterns
  TRANSFORM_PATTERNS.forEach(({ pattern, replacement }) => {
    transformed = transformed.replace(pattern, replacement)
  })

  // Special post-processing for llm-providers.ts
  if (filename === 'llm-providers.ts') {
    // Fix the missing function declaration issue
    transformed = transformed.replace(
      /console\.log\('Provider:', provider\);/,
      'export function createLLMClient(config) {\n  const { provider, model } = config\n  \n  console.log(\'=== LLM CLIENT CONFIGURATION ===\');\n  console.log(\'Provider:\', provider);'
    )
  }

  // Special post-processing for content-agent.ts
  if (filename === 'content-agent.ts') {
    // Fix the broken instructions property
    transformed = transformed.replace(
      /instructions,/g,
      'instructions: CONTENT_AGENT_SYSTEM_PROMPT,'
    )
  }

  // Special post-processing for orchestrator-agent.ts
  if (filename === 'orchestrator-agent.ts') {
    // Fix the broken instructions property
    transformed = transformed.replace(
      /instructions,/g,
      'instructions: ORCHESTRATOR_SYSTEM_PROMPT,'
    )
  }

  // Special post-processing for seo-strategist.ts
  if (filename === 'seo-strategist.ts') {
    // Fix the broken instructions property
    transformed = transformed.replace(
      /instructions,/g,
      'instructions: SEO_SYSTEM_PROMPT,'
    )
  }

  // Add header comment
  const header = `// Auto-generated Mastra tool from ${filename}
// This file is automatically generated by src/scripts/sync-mastra.js
// Do not edit manually - changes will be overwritten

`

  return header + transformed
}

/**
 * Extract tool metadata from code
 */
function extractToolMetadata(code, filename) {
  const metadata = {
    id: filename.replace('.ts', '').replace('-tool', ''),
    filename,
    exports: []
  }

  // Extract tool exports
  const exportMatches = code.match(/export\s+const\s+(\w+)\s*=/g)
  if (exportMatches) {
    metadata.exports = exportMatches.map(match => 
      match.replace(/export\s+const\s+(\w+)\s*=/, '$1')
    )
  }

  return metadata
}

/**
 * Generate utility functions inline for tools that need them
 */
function generateInlineUtils(code) {
  if (code.includes('firecrawl-utils')) {
    // Add inline Firecrawl utilities
    return `
// Inline Firecrawl utilities (generated)
const FIRECRAWL_API_URL = ENV_VARS.FIRECRAWL_API_URL()
const FIRECRAWL_API_KEY = ENV_VARS.FIRECRAWL_API_KEY()

async function makeFirecrawlRequest(endpoint, data) {
  const response = await fetch(\`\${FIRECRAWL_API_URL}\${endpoint}\`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': \`Bearer \${FIRECRAWL_API_KEY}\`
    },
    body: JSON.stringify(data)
  })
  
  if (!response.ok) {
    throw new Error(\`Firecrawl API error: \${response.status} \${response.statusText}\`)
  }
  
  return response.json()
}

` + code
  }
  
  return code
}

/**
 * Process a single tool file
 */
async function processToolFile(filename) {
  const sourcePath = path.join(SVELTEKIT_TOOLS_DIR, filename)
  const targetPath = path.join(MASTRA_TOOLS_DIR, filename.replace('.ts', '.js'))
  
  try {
    console.log(`📄 Processing ${filename}...`)
    
    // Read source file
    const sourceCode = await fs.readFile(sourcePath, 'utf-8')
    
    // Transform code
    let transformedCode = transformToolCode(sourceCode, filename)
    
    // Add inline utilities if needed
    transformedCode = generateInlineUtils(transformedCode)
    
    // Extract metadata
    const metadata = extractToolMetadata(sourceCode, filename)
    
    // Write transformed file
    await fs.writeFile(targetPath, transformedCode, 'utf-8')
    
    console.log(`✅ Generated ${filename} -> ${path.basename(targetPath)}`)
    return metadata
    
  } catch (error) {
    console.error(`❌ Error processing ${filename}:`, error.message)
    return null
  }
}

/**
 * Process a single agent file
 */
async function processAgentFile(filename) {
  const sourcePath = path.join(SVELTEKIT_AGENTS_DIR, filename)
  const targetPath = path.join(MASTRA_AGENTS_DIR, filename.replace('.ts', '.js'))
  
  try {
    console.log(`🤖 Processing agent ${filename}...`)
    
    // Read source file
    const sourceCode = await fs.readFile(sourcePath, 'utf-8')
    
    // Transform code (same patterns as tools)
    let transformedCode = transformToolCode(sourceCode, filename)
    
    // Extract metadata
    const metadata = extractAgentMetadata(sourceCode, filename)
    
    // Write transformed file
    await fs.writeFile(targetPath, transformedCode, 'utf-8')
    
    console.log(`✅ Generated agent ${filename} -> ${path.basename(targetPath)}`)
    return metadata
    
  } catch (error) {
    console.error(`❌ Error processing agent ${filename}:`, error.message)
    return null
  }
}

/**
 * Extract agent metadata from code
 */
function extractAgentMetadata(code, filename) {
  const metadata = {
    id: filename.replace('.ts', '').replace('-agent', ''),
    filename,
    exports: []
  }

  // Extract agent exports
  const exportMatches = code.match(/export\s+const\s+(\w+Agent)\s*=/g)
  if (exportMatches) {
    metadata.exports = exportMatches.map(match => 
      match.replace(/export\s+const\s+(\w+)\s*=/, '$1')
    )
  }

  return metadata
}

/**
 * Update Mastra tools index file
 */
async function updateToolsIndex(toolMetadata) {
  const indexPath = path.join(MASTRA_TOOLS_DIR, 'index.js')
  
  const exports = toolMetadata
    .filter(meta => meta && meta.exports.length > 0)
    .map(meta => 
      meta.exports.map(exportName => 
        `export { ${exportName} } from './${meta.filename.replace('.ts', '.js')}'`
      ).join('\n')
    )
    .join('\n')
  
  const indexContent = `// Auto-generated Mastra tools index
// This file is automatically generated by src/scripts/sync-mastra.js

${exports}
`
  
  await fs.writeFile(indexPath, indexContent, 'utf-8')
  console.log('✅ Updated tools index')
}

/**
 * Update Mastra agents index file
 */
async function updateAgentsIndex(agentMetadata) {
  const indexPath = path.join(MASTRA_AGENTS_DIR, 'index.js')
  
  const exports = agentMetadata
    .filter(meta => meta && meta.exports.length > 0)
    .map(meta => 
      meta.exports.map(exportName => 
        `export { ${exportName} } from './${meta.filename.replace('.ts', '.js')}'`
      ).join('\n')
    )
    .join('\n')
  
  const indexContent = `// Auto-generated Mastra agents index
// This file is automatically generated by src/scripts/sync-mastra.js

${exports}
`
  
  await fs.writeFile(indexPath, indexContent, 'utf-8')
  console.log('✅ Updated agents index')
}

/**
 * Main sync function
 */
/**
 * Copy and transform llm-providers file to shared directory
 */
async function processLLMProviders() {
  const sourcePath = path.join(SVELTEKIT_AGENTS_DIR, 'llm-providers.ts')
  const targetPath = path.join(MASTRA_SHARED_DIR, 'llm-providers.js')
  
  try {
    const content = await fs.readFile(sourcePath, 'utf-8')
    const transformed = transformToolCode(content, 'llm-providers.ts')
    await fs.writeFile(targetPath, transformed)
    console.log('📄 Copied and transformed llm-providers.ts -> llm-providers.js')
  } catch (error) {
    console.warn('⚠️  Could not process llm-providers.ts:', error.message)
  }
}

async function syncMastraTools() {
  console.log('🔄 Starting Mastra tools and agents sync...\n')
  
  try {
    // Ensure target directories exist
    await fs.mkdir(MASTRA_TOOLS_DIR, { recursive: true })
    await fs.mkdir(MASTRA_AGENTS_DIR, { recursive: true })
    await fs.mkdir(MASTRA_SHARED_DIR, { recursive: true })
    
    // Process llm-providers first
    await processLLMProviders()
    
    // Get all tool files
    const toolFiles = await fs.readdir(SVELTEKIT_TOOLS_DIR)
    const validToolFiles = toolFiles.filter(file => 
      file.endsWith('.ts') && 
      file.includes('tool') && 
      !file.includes('test') &&
      !file.includes('.d.ts')
    )
    
    // Get all agent files
    const agentFiles = await fs.readdir(SVELTEKIT_AGENTS_DIR)
    const validAgentFiles = agentFiles.filter(file => 
      file.endsWith('.ts') && 
      (file.includes('agent') || file === 'content-agent.ts' || file === 'seo-strategist.ts' || file === 'company-researcher.ts') &&
      !file.includes('test') &&
      !file.includes('.d.ts') &&
      !file.includes('index') &&
      !file.includes('llm-providers') &&
      !file.includes('backup')
    )
    
    console.log(`📁 Found ${validToolFiles.length} tool files and ${validAgentFiles.length} agent files to process\n`)
    
    // Process each tool file
    const toolMetadata = []
    for (const filename of validToolFiles) {
      const metadata = await processToolFile(filename)
      if (metadata) {
        toolMetadata.push(metadata)
      }
    }
    
    // Process each agent file  
    const agentMetadata = []
    for (const filename of validAgentFiles) {
      const metadata = await processAgentFile(filename)
      if (metadata) {
        agentMetadata.push(metadata)
      }
    }
    
    // Update indexes
    await updateToolsIndex(toolMetadata)
    await updateAgentsIndex(agentMetadata)
    
    console.log(`\n✅ Sync completed! Generated ${toolMetadata.length} Mastra tools and ${agentMetadata.length} agents`)
    console.log('📂 Tools available in:', MASTRA_TOOLS_DIR)
    console.log('🤖 Agents available in:', MASTRA_AGENTS_DIR)
    
  } catch (error) {
    console.error('❌ Sync failed:', error.message)
    process.exit(1)
  }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  syncMastraTools()
}

export { syncMastraTools }
