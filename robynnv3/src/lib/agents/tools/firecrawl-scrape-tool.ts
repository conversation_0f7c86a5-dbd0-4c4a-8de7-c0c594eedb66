import { z } from 'zod'
import { createTool } from '@mastra/core'
import { firecrawlScrapeSchema } from '$lib/schemas/firecrawl-schemas'
import {
  getFirecrawlContext,
  isFirecrawlConfigured,
  createFirecrawlHeaders,
  createTimeoutController,
  parseFirecrawlError,
  handleFirecrawlError,
  generateMockScrapeResult,
  validateFirecrawlUrl,
  normalizeUrl,
  extractDomain,
  calculateFirecrawlConfidence
} from '$lib/utils/firecrawl-utils'
import type { 
  FirecrawlScrapeRequest, 
  FirecrawlScrapeResponse,
  FirecrawlScrapeResult 
} from '$lib/types/firecrawl'

export const firecrawlScrapeTool = createTool({
  id: 'firecrawl-scrape',
  description: 'Deep single-page analysis with AI-powered content extraction, JavaScript rendering, and user interaction simulation. Ideal for competitor landing pages, pricing intelligence, and real-time content monitoring.',
  inputSchema: firecrawlScrapeSchema,
  execute: async (context) => {
    // Handle both direct calls and agent-wrapped calls
    // Agent calls: { context: { url, formats, ... } }
    // Direct calls: { url, formats, ... }
    const inputData = context.context || context

    // Enhanced parameter validation with helpful error messages
    if (!inputData || typeof inputData !== 'object') {
      return {
        success: false,
        error: 'Parameter validation failed: Expected object with parameters, received: ' + typeof inputData,
        help: 'Please check the system prompt for correct JSON parameter examples',
        source: 'firecrawl-scrape',
        confidence_score: 0.0,
        processing_time: '0s'
      }
    }

    // Validate extractOptions if provided
    if (inputData.extractOptions && typeof inputData.extractOptions === 'string') {
      return {
        success: false,
        error: 'Parameter validation failed: Invalid extractOptions: Expected JSON object like {"onlyMainContent": true}, received string: ' + inputData.extractOptions,
        help: 'Please check the system prompt for correct JSON parameter examples',
        source: 'firecrawl-scrape',
        confidence_score: 0.0,
        processing_time: '0s'
      }
    }

    // Validate actions if provided
    if (inputData.actions && typeof inputData.actions === 'string') {
      return {
        success: false,
        error: 'Parameter validation failed: Invalid actions: Expected JSON array of action objects, received string: ' + inputData.actions,
        help: 'Please check the system prompt for correct JSON parameter examples',
        source: 'firecrawl-scrape',
        confidence_score: 0.0,
        processing_time: '0s'
      }
    }

    // Validate location if provided
    if (inputData.location && typeof inputData.location === 'string') {
      return {
        success: false,
        error: 'Parameter validation failed: Invalid location: Expected JSON object like {"country": "US"}, received string: ' + inputData.location,
        help: 'Please check the system prompt for correct JSON parameter examples',
        source: 'firecrawl-scrape',
        confidence_score: 0.0,
        processing_time: '0s'
      }
    }

    const {
      url,
      formats,
      extractOptions,
      actions,
      location,
      jsonOptions
    } = inputData

    // Validate URL format
    if (!validateFirecrawlUrl(url)) {
      return {
        success: false,
        error: 'Parameter validation failed: Invalid URL format. Please provide a valid HTTP or HTTPS URL.',
        help: 'Please check the system prompt for correct JSON parameter examples',
        source: 'firecrawl-scrape',
        confidence_score: 0.0,
        processing_time: '0s'
      }
    }

    // Validate format conflicts - don't allow both formats and jsonOptions
    if (jsonOptions && formats && formats.length > 1) {
      return {
        success: false,
        error: 'Parameter validation failed: Cannot use multiple formats with jsonOptions. When using jsonOptions for structured data extraction, omit the formats parameter or use only ["markdown"].',
        help: 'For structured data extraction, use: {"url": "...", "jsonOptions": {...}} without formats parameter',
        source: 'firecrawl-scrape',
        confidence_score: 0.0,
        processing_time: '0s'
      }
    }

    const normalizedUrl = normalizeUrl(url)
    const domain = extractDomain(normalizedUrl)

    // Check if Firecrawl API is configured
    if (!isFirecrawlConfigured()) {
      console.error('Firecrawl API credentials missing - returning mock data for testing')
      
      const mockResult = generateMockScrapeResult(normalizedUrl)
      return {
        success: true,
        data: {
          ...mockResult,
          metadata: {
            ...mockResult.metadata,
            note: 'Mock data returned - Firecrawl API credentials not configured'
          }
        },
        source: 'firecrawl-scrape',
        confidence_score: 0.5, // Lower confidence for mock data
        domain,
        processing_time: '0.5s',
        note: 'Mock data returned - Firecrawl API credentials not configured'
      }
    }

    const firecrawlContext = getFirecrawlContext()
    
    try {
      // Build request payload - flatten extractOptions to match Firecrawl API format
      const requestPayload: any = {
        url: normalizedUrl,
        // Only include formats if not using jsonOptions (to avoid conflicts)
        ...(jsonOptions ? {} : { formats: formats || ['markdown'] }),
        // Flatten extractOptions to top-level fields as expected by Firecrawl API
        ...(extractOptions?.onlyMainContent !== undefined && { onlyMainContent: extractOptions.onlyMainContent }),
        ...(extractOptions?.includeTags && { includeTags: extractOptions.includeTags }),
        ...(extractOptions?.excludeTags && { excludeTags: extractOptions.excludeTags }),
        ...(extractOptions?.onlyIncludeTags && { onlyIncludeTags: extractOptions.onlyIncludeTags }),
        ...(actions && { actions }),
        ...(location && {
          location: {
            country: location.country,
            // Convert language string to languages array as expected by Firecrawl
            ...(location.language && { languages: [location.language] })
          }
        }),
        ...(jsonOptions && { jsonOptions })
      }

      console.log('Making Firecrawl scrape request for:', normalizedUrl)
      console.log('Request payload:', JSON.stringify(requestPayload, null, 2))

      const startTime = Date.now()
      const { controller, timeoutId } = createTimeoutController(firecrawlContext.timeout)

      const response = await fetch(`${firecrawlContext.baseUrl}/v1/scrape`, {
        method: 'POST',
        headers: createFirecrawlHeaders(firecrawlContext.apiKey),
        body: JSON.stringify(requestPayload),
        signal: controller.signal
      })

      clearTimeout(timeoutId)
      const processingTime = `${((Date.now() - startTime) / 1000).toFixed(1)}s`

      if (!response.ok) {
        let errorData = {}
        let responseText = ''

        try {
          responseText = await response.text()
          errorData = JSON.parse(responseText)
        } catch (parseError) {
          console.error('Failed to parse Firecrawl error response:', parseError)
          errorData = { message: responseText || 'Unknown error' }
        }

        // Enhanced error logging with full details
        console.error('Firecrawl API Error Details:', {
          status: response.status,
          statusText: response.statusText,
          url: normalizedUrl,
          requestPayload: JSON.stringify(requestPayload, null, 2),
          responseHeaders: Object.fromEntries(response.headers.entries()),
          responseBody: responseText,
          errorData
        })

        const firecrawlError = parseFirecrawlError(errorData, response.status)
        const errorResponse = handleFirecrawlError(firecrawlError)

        // Add additional context to error response
        errorResponse.debug_info = {
          request_url: normalizedUrl,
          request_payload: requestPayload,
          response_status: response.status,
          response_headers: Object.fromEntries(response.headers.entries()),
          raw_response: responseText.substring(0, 500) // First 500 chars
        }

        console.error('Firecrawl scrape API error:', errorResponse)
        return errorResponse
      }

      const data: FirecrawlScrapeResponse = await response.json()

      if (!data.success || !data.data) {
        console.error('Firecrawl scrape data error:', data.error)
        return {
          success: false,
          error: `Firecrawl scrape failed: ${data.error || 'Unknown error'}`,
          source: 'firecrawl-scrape',
          confidence_score: 0.0,
          processing_time: processingTime,
          type: 'data_error',
          note: 'Firecrawl tool failed - research continued with other available tools'
        }
      }

      const result = data.data
      
      // Calculate confidence score based on data quality
      const confidence = calculateFirecrawlConfidence(
        !!(result.markdown || result.html),
        !!result.metadata,
        !!result.extractedData,
        result.metadata.statusCode
      )

      // Enhanced result with additional metadata
      const enhancedResult = {
        success: true,
        data: {
          ...result,
          metadata: {
            ...result.metadata,
            domain,
            processingTime,
            confidence_score: confidence,
            formats_returned: Object.keys(result).filter(key => 
              ['markdown', 'html', 'rawHtml', 'screenshot'].includes(key) && result[key as keyof FirecrawlScrapeResult]
            ),
            has_extracted_data: !!result.extractedData,
            extraction_prompt: extractSchema?.prompt
          }
        },
        source: 'firecrawl-scrape',
        confidence_score: confidence,
        domain,
        processing_time: processingTime,
        url: normalizedUrl
      }

      console.log('Firecrawl scrape completed successfully:', {
        url: normalizedUrl,
        domain,
        confidence,
        processingTime,
        hasContent: !!(result.markdown || result.html),
        hasExtractedData: !!result.extractedData
      })

      return enhancedResult

    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        const timeoutError = parseFirecrawlError(error)
        const errorResponse = handleFirecrawlError(timeoutError)
        console.error('Firecrawl scrape timeout error:', errorResponse)
        return errorResponse
      }

      console.error('Firecrawl scrape error:', error)

      // For network or API errors, provide fallback response
      if (error instanceof Error && (
        error.message.includes('fetch') ||
        error.message.includes('network') ||
        error.message.includes('timeout')
      )) {
        console.log('Network error detected, providing fallback response')

        const fallbackUrl = normalizedUrl || 'unknown-url'
        const mockResult = generateMockScrapeResult(fallbackUrl)
        return {
          success: false,
          error: error.message,
          fallback_data: {
            ...mockResult,
            metadata: {
              ...mockResult.metadata,
              note: 'Fallback data due to network error'
            }
          },
          source: 'firecrawl-scrape',
          confidence_score: 0.3,
          domain: domain || 'unknown-domain',
          processing_time: '0s',
          url: fallbackUrl
        }
      }

      // Return error response instead of throwing for agent resilience
      return {
        success: false,
        error: `Firecrawl scrape error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        source: 'firecrawl-scrape',
        confidence_score: 0.0,
        processing_time: '0s',
        type: 'unexpected_error',
        note: 'Firecrawl tool failed - research continued with other available tools'
      }
    }
  }
})
