import { z } from "zod"
import { env } from "$env/dynamic/private"

const braveSearchSchema = z.object({
  query: z.string().describe("Search query to execute")
})

export const braveSearchTool = {
  name: "braveSearch",
  description: "Fallback web search tool using Brave Search API for company research and competitive analysis",
  schema: braveSearchSchema,
  execute: async ({ query }: z.infer<typeof braveSearchSchema>) => {
    console.log(`🔍 Brave Search (Fallback): ${query}`)
    
    try {
      const apiKey = env.BRAVE_SEARCH_API_KEY
      if (!apiKey) {
        throw new Error("Brave Search API key not configured")
      }

      const response = await fetch(`https://api.search.brave.com/res/v1/web/search?q=${encodeURIComponent(query)}&count=10`, {
        headers: {
          'Accept': 'application/json',
          'Accept-Encoding': 'gzip',
          'X-Subscription-Token': apiKey
        }
      })

      if (!response.ok) {
        throw new Error(`Brave Search API error: ${response.status}`)
      }

      const data = await response.json()
      
      // Format results similar to web search tool
      const results = data.web?.results?.slice(0, 10).map((result: any) => ({
        title: result.title,
        url: result.url,
        snippet: result.description,
        publishedDate: result.age || null
      })) || []

      console.log(`✅ Brave Search found ${results.length} results`)
      
      return {
        results,
        query,
        totalResults: data.web?.results?.length || 0,
        source: "brave_search_fallback"
      }
    } catch (error) {
      console.error("❌ Brave Search failed:", error)
      
      // Return basic fallback data
      return {
        results: [
          {
            title: `${query} - Industry Overview`,
            url: `https://example.com/search?q=${encodeURIComponent(query)}`,
            snippet: `General information about ${query}. For comprehensive results, please try refining your search query.`,
            publishedDate: new Date().toISOString()
          }
        ],
        query,
        totalResults: 1,
        source: "brave_search_fallback",
        error: error instanceof Error ? error.message : "Unknown error"
      }
    }
  }
}
