import { createTool } from "@mastra/core"
import { slackPublishingSchema } from "$lib/schemas/publishing-schemas"
import type { 
  SlackWebhookConfig, 
  SlackMessage, 
  PublishingResult,
  OrchestratorResearchData 
} from "$lib/types/publishing"
import { SlackMessageFormatter } from "./slack-message-formatter"
import {
  getSlackContext,
  validateSlackWebhookUrl,
  createSlackHeaders,
  createTimeoutController,
  parseSlackError,
  handleSlackError,
  retryWithBackoff,
  generateMockSlackResponse
} from "./slack-utils"

/**
 * Slack Webhook Publishing Tool
 * 
 * Publishes orchestrator agent research results to Slack channels via webhooks.
 * Follows the established Mastra createTool pattern with comprehensive error handling.
 */
export const slackWebhookTool = createTool({
  id: "slack_webhook_publish",
  description: "Publish research results to Slack channels via webhook integration",
  inputSchema: slackPublishingSchema,
  
  execute: async (context) => {
    const startTime = Date.now()
    const { researchData, config, formatting, preview } = context.context
    
    try {
      console.log('=== SLACK WEBHOOK TOOL EXECUTION ===')
      console.log('Config:', JSON.stringify(config, null, 2))
      console.log('Formatting options:', JSON.stringify(formatting, null, 2))
      console.log('Preview mode:', preview)
      
      // Validate webhook URL
      if (!validateSlackWebhookUrl(config.webhookUrl)) {
        throw new Error('Invalid Slack webhook URL format')
      }
      
      // Get Slack context
      const slackContext = getSlackContext(config)
      
      // Format message using the research data
      const slackMessage = await SlackMessageFormatter.format(researchData, formatting)
      
      // Apply webhook-specific configuration
      if (config.channel) slackMessage.channel = config.channel
      if (config.username) slackMessage.username = config.username
      if (config.iconEmoji) slackMessage.icon_emoji = config.iconEmoji
      if (config.threadTs) slackMessage.thread_ts = config.threadTs
      
      // If preview mode, return formatted message without sending
      if (preview) {
        const processingTime = Date.now() - startTime
        return {
          success: true,
          destination: 'slack',
          timestamp: new Date().toISOString(),
          deliveryTime: processingTime,
          retryCount: 0,
          previewData: {
            message: slackMessage,
            estimatedSize: JSON.stringify(slackMessage).length,
            blockCount: slackMessage.blocks?.length || 0,
            note: 'Preview mode - message not sent to Slack'
          }
        }
      }
      
      // Check if Slack webhook is configured (for production)
      if (!config.webhookUrl.includes('hooks.slack.com')) {
        console.log('Slack webhook not configured, returning mock response')
        const processingTime = Date.now() - startTime
        
        return {
          success: true,
          destination: 'slack',
          messageId: `mock_${Date.now()}`,
          timestamp: new Date().toISOString(),
          deliveryTime: processingTime,
          retryCount: 0,
          previewData: {
            message: slackMessage,
            mockResponse: generateMockSlackResponse(true),
            note: 'Mock response - Slack webhook not configured'
          }
        }
      }
      
      // Send message to Slack with retry logic
      const result = await retryWithBackoff(
        () => sendSlackMessage(slackMessage, slackContext),
        slackContext.retryAttempts
      )
      
      const processingTime = Date.now() - startTime
      
      return {
        success: true,
        destination: 'slack',
        messageId: result.ts || `slack_${Date.now()}`,
        timestamp: new Date().toISOString(),
        deliveryTime: processingTime,
        retryCount: 0, // TODO: Track actual retry count
        previewData: {
          message: slackMessage,
          slackResponse: result
        }
      }
      
    } catch (error) {
      console.error('Slack webhook tool error:', error)
      
      const processingTime = Date.now() - startTime
      const slackError = parseSlackError(error)
      const errorResult = handleSlackError(slackError)
      
      // Return error result with processing time
      return {
        ...errorResult,
        deliveryTime: processingTime,
        previewData: {
          error: slackError,
          note: 'Publishing failed - check error details'
        }
      }
    }
  }
})

/**
 * Send message to Slack webhook
 */
async function sendSlackMessage(
  message: SlackMessage, 
  context: { webhookUrl: string; timeout: number }
): Promise<any> {
  const { controller, timeoutId } = createTimeoutController(context.timeout)
  
  try {
    const response = await fetch(context.webhookUrl, {
      method: 'POST',
      headers: createSlackHeaders(),
      body: JSON.stringify(message),
      signal: controller.signal
    })
    
    clearTimeout(timeoutId)
    
    if (!response.ok) {
      const errorText = await response.text()
      console.error('Slack webhook error response:', errorText)
      throw new Error(`Slack webhook failed: ${response.status} ${response.statusText}`)
    }
    
    // Slack webhooks return different response formats
    const contentType = response.headers.get('content-type')
    if (contentType && contentType.includes('application/json')) {
      return await response.json()
    } else {
      const text = await response.text()
      return { ok: text === 'ok', response: text }
    }
    
  } catch (error) {
    clearTimeout(timeoutId)
    throw error
  }
}

/**
 * Test Slack webhook configuration
 */
export async function testSlackWebhook(config: SlackWebhookConfig): Promise<boolean> {
  try {
    if (!validateSlackWebhookUrl(config.webhookUrl)) {
      return false
    }
    
    const testMessage: SlackMessage = {
      text: "🧪 Test message from Robynn Publishing System",
      blocks: [
        {
          type: "section",
          text: {
            type: "mrkdwn",
            text: "This is a test message to verify your Slack webhook configuration. If you see this message, your webhook is working correctly! 🎉"
          }
        }
      ]
    }
    
    const context = getSlackContext(config)
    await sendSlackMessage(testMessage, context)
    
    return true
  } catch (error) {
    console.error('Slack webhook test failed:', error)
    return false
  }
}

/**
 * Validate Slack webhook configuration
 */
export function validateSlackConfig(config: any): config is SlackWebhookConfig {
  try {
    // Basic validation
    if (!config || typeof config !== 'object') return false
    if (!config.webhookUrl || typeof config.webhookUrl !== 'string') return false
    if (!validateSlackWebhookUrl(config.webhookUrl)) return false
    
    // Optional fields validation
    if (config.channel && typeof config.channel !== 'string') return false
    if (config.username && typeof config.username !== 'string') return false
    if (config.iconEmoji && typeof config.iconEmoji !== 'string') return false
    if (config.threadTs && typeof config.threadTs !== 'string') return false
    
    // Numeric fields validation
    if (config.retryAttempts !== undefined) {
      if (typeof config.retryAttempts !== 'number' || config.retryAttempts < 0 || config.retryAttempts > 5) {
        return false
      }
    }
    
    if (config.timeout !== undefined) {
      if (typeof config.timeout !== 'number' || config.timeout < 1000 || config.timeout > 30000) {
        return false
      }
    }
    
    return true
  } catch (error) {
    return false
  }
}

/**
 * Generate example Slack configuration for documentation
 */
export function getSlackConfigExample(): SlackWebhookConfig {
  return {
    webhookUrl: "*****************************************************************************",
    channel: "#research-results",
    username: "Robynn Research Bot",
    iconEmoji: ":robot_face:",
    retryAttempts: 3,
    timeout: 10000
  }
}

/**
 * Generate example research data for testing
 */
export function getExampleResearchData(): OrchestratorResearchData {
  return {
    targetCompany: {
      name: "Example Corp",
      domain: "example.com",
      industry: "Technology",
      employees: "100-500",
      revenue: "$10M-$50M",
      description: "A leading technology company specializing in innovative solutions.",
      location: "San Francisco, CA, USA",
      founded_year: 2015,
      headquarters: {
        city: "San Francisco",
        state: "CA",
        country: "USA"
      },
      website_url: "https://example.com",
      linkedin_url: "https://linkedin.com/company/example-corp",
      technologies: ["React", "Node.js", "AWS", "PostgreSQL"],
      confidence_score: 0.92,
      data_sources: ["Apollo API", "Exa Search"],
      last_updated: new Date().toISOString(),
      contacts: [
        {
          name: "John Smith",
          title: "CEO",
          email: "<EMAIL>",
          linkedin_url: "https://linkedin.com/in/johnsmith",
          seniority: "senior",
          department: "executive",
          confidence_score: 0.95,
          source: "Apollo API"
        }
      ]
    },
    competitors: [
      {
        name: "Competitor Inc",
        domain: "competitor.com",
        similarity: "Similar technology stack and target market",
        industry: "Technology",
        employees: "200-1000",
        revenue: "$20M-$100M",
        description: "A competitive technology company in the same space.",
        location: "Austin, TX, USA",
        website_url: "https://competitor.com",
        linkedin_url: "https://linkedin.com/company/competitor-inc",
        technologies: ["Vue.js", "Python", "GCP"],
        confidence_score: 0.85,
        data_sources: ["Exa Search"],
        contacts: []
      }
    ],
    intelligence: {
      market_insights: {
        content: "The technology market is experiencing rapid growth with increasing demand for innovative solutions.",
        confidence_score: 0.80,
        sources: ["Exa Search", "Web Intelligence"],
        last_updated: new Date().toISOString()
      }
    },
    actionable_insights: {
      marketing_opportunities: [
        {
          insight: "Strong developer community engagement opportunity",
          priority: "High" as const,
          confidence_score: 0.85,
          recommended_action: "Create developer-focused content and documentation"
        }
      ],
      competitive_advantages: [
        {
          advantage: "Superior API design and developer experience",
          impact: "High" as const,
          confidence_score: 0.90
        }
      ],
      contact_strategy: {
        primary_contacts: ["John Smith (CEO)"],
        approach_recommendations: "Focus on technical leadership and innovation messaging",
        confidence_score: 0.88
      }
    },
    metadata: {
      totalCompanies: 5,
      totalContacts: 8,
      processingTime: "45 seconds",
      dataQuality: "High" as const,
      confidence_score: 0.89,
      data_freshness: new Date().toISOString(),
      sources_used: ["Apollo API", "Exa Search"],
      api_calls_made: {
        apollo_search_company: 1,
        apollo_find_companies: 1,
        apollo_find_contacts: 1,
        exa_search: 1
      },
      tool_status: {
        apollo_tools: "success",
        exa_search: "success"
      }
    }
  }
}
