import type { 
  OrchestratorResearchData, 
  SlackMessage, 
  SlackMessageBlock, 
  SlackFormattingOptions 
} from '$lib/types/publishing'
import {
  createHeaderBlock,
  createSectionBlock,
  createFieldsBlock,
  createDividerBlock,
  formatConfidenceScore,
  formatDataSources,
  escapeSlackMarkdown,
  truncateText,
  validateSlackMessage
} from './slack-utils'

/**
 * Slack Message Formatter for Orchestrator Research Data
 * 
 * Transforms structured research data into Slack-optimized message blocks
 * following Slack's Block Kit format and message limits.
 */
export class SlackMessageFormatter {
  
  /**
   * Format research data into a complete Slack message
   */
  static async format(
    data: OrchestratorResearchData,
    options: SlackFormattingOptions = {
      includeMetadata: true,
      includeContacts: true,
      includeCompetitors: true,
      includeInsights: true,
      messageStyle: 'detailed',
      useThreads: false
    }
  ): Promise<SlackMessage> {
    const blocks: SlackMessageBlock[] = []

    // Validate and normalize data structure
    const normalizedData = this.normalizeResearchData(data)

    // Header section
    const companyName = normalizedData.targetCompany?.name || 'Unknown Company'
    blocks.push(createHeaderBlock(`🔍 Research Results: ${companyName}`))
    
    // Company overview
    if (normalizedData.targetCompany) {
      blocks.push(...this.formatCompanyOverview(normalizedData.targetCompany, options.messageStyle))
    }

    // Competitors section
    if (options.includeCompetitors && normalizedData.competitors && normalizedData.competitors.length > 0) {
      blocks.push(createDividerBlock())
      blocks.push(...this.formatCompetitors(normalizedData.competitors, options.messageStyle))
    }

    // Intelligence section
    if (normalizedData.intelligence) {
      blocks.push(createDividerBlock())
      blocks.push(...this.formatIntelligence(normalizedData.intelligence, options.messageStyle))
    }

    // Actionable insights
    if (options.includeInsights && normalizedData.actionable_insights) {
      blocks.push(createDividerBlock())
      blocks.push(...this.formatActionableInsights(normalizedData.actionable_insights))
    }

    // Contacts section
    if (options.includeContacts && normalizedData.targetCompany?.contacts && normalizedData.targetCompany.contacts.length > 0) {
      blocks.push(createDividerBlock())
      blocks.push(...this.formatContacts(normalizedData.targetCompany.contacts, options.messageStyle))
    }

    // Metadata section
    if (options.includeMetadata && normalizedData.metadata) {
      blocks.push(createDividerBlock())
      blocks.push(...this.formatMetadata(normalizedData.metadata))
    }
    
    const message: SlackMessage = {
      blocks,
      unfurl_links: false,
      unfurl_media: false
    }
    
    // Add mentions if specified
    if (options.mentionUsers && options.mentionUsers.length > 0) {
      const mentions = options.mentionUsers.map(userId => `<@${userId}>`).join(' ')
      message.text = `${mentions} New research results available`
    }
    
    // Validate message before returning
    if (!validateSlackMessage(message)) {
      throw new Error('Generated Slack message exceeds platform limits')
    }
    
    return message
  }

  /**
   * Normalize research data to handle different formats
   */
  private static normalizeResearchData(data: any): OrchestratorResearchData {
    // If data is already in the correct format, return as-is
    if (data && data.targetCompany && typeof data.targetCompany === 'object') {
      return data as OrchestratorResearchData
    }

    // Handle case where data might be wrapped in a 'data' property
    if (data && data.data && data.data.targetCompany) {
      return data.data as OrchestratorResearchData
    }

    // Handle case where the structure is different (e.g., from different agent versions)
    const normalized: any = {
      targetCompany: null,
      competitors: [],
      intelligence: {},
      actionable_insights: {},
      metadata: {}
    }

    // Try to extract target company from various possible structures
    if (data.target_company) {
      normalized.targetCompany = this.normalizeCompanyData(data.target_company)
    } else if (data.company) {
      normalized.targetCompany = this.normalizeCompanyData(data.company)
    } else if (typeof data === 'string') {
      // Handle case where data is a JSON string
      try {
        const parsed = JSON.parse(data)
        return this.normalizeResearchData(parsed)
      } catch {
        // If parsing fails, create a minimal structure
        normalized.targetCompany = { name: 'Research Results', domain: '', industry: '' }
      }
    } else {
      // Create a minimal target company structure
      normalized.targetCompany = {
        name: 'Research Results',
        domain: '',
        industry: '',
        description: 'Research data available',
        technologies: [],
        data_sources: []
      }
    }

    // Extract other fields
    normalized.competitors = data.competitors || data.competitor_companies || []
    normalized.intelligence = data.intelligence || {}
    normalized.actionable_insights = data.actionable_insights || {}
    normalized.metadata = data.metadata || {}

    return normalized as OrchestratorResearchData
  }

  /**
   * Normalize company data to ensure arrays are properly initialized
   */
  private static normalizeCompanyData(company: any): any {
    if (!company) return null

    return {
      ...company,
      technologies: Array.isArray(company.technologies) ? company.technologies : [],
      data_sources: Array.isArray(company.data_sources) ? company.data_sources : []
    }
  }

  /**
   * Format company overview section
   */
  private static formatCompanyOverview(company: any, style: string): SlackMessageBlock[] {
    const blocks: SlackMessageBlock[] = []
    
    // Company basic info
    const fields = [
      { title: 'Industry', value: company.industry || 'Unknown' },
      { title: 'Employees', value: company.employees || 'Unknown' },
      { title: 'Revenue', value: company.revenue || 'Unknown' },
      { title: 'Location', value: company.location || 'Unknown' }
    ]
    
    if (company.founded_year) {
      fields.push({ title: 'Founded', value: company.founded_year.toString() })
    }
    
    blocks.push(createFieldsBlock(fields))
    
    // Company description
    if (company.description && style !== 'summary') {
      blocks.push(createSectionBlock(`*Description:*\n${company.description}`))
    }
    
    // Technologies
    if (company.technologies && Array.isArray(company.technologies) && company.technologies.length > 0) {
      const techList = company.technologies.slice(0, 10).join(', ')
      blocks.push(createSectionBlock(`*Technologies:* ${techList}`))
    }
    
    // Confidence and sources
    const confidenceText = `*Confidence:* ${formatConfidenceScore(company.confidence_score)}`
    const sourcesText = `*Sources:* ${Array.isArray(company.data_sources) ? company.data_sources.join(', ') : 'Unknown'}`
    blocks.push(createSectionBlock(`${confidenceText}\n${sourcesText}`))
    
    return blocks
  }
  
  /**
   * Format competitors section
   */
  private static formatCompetitors(competitors: any[], style: string): SlackMessageBlock[] {
    const blocks: SlackMessageBlock[] = []
    
    blocks.push(createHeaderBlock('🏢 Key Competitors'))
    
    const maxCompetitors = style === 'summary' ? 3 : 5
    const topCompetitors = competitors.slice(0, maxCompetitors)
    
    for (const competitor of topCompetitors) {
      const fields = [
        { title: 'Company', value: competitor.name },
        { title: 'Industry', value: competitor.industry || 'Unknown' },
        { title: 'Employees', value: competitor.employees || 'Unknown' },
        { title: 'Similarity', value: competitor.similarity || 'Unknown' }
      ]
      
      blocks.push(createFieldsBlock(fields))
      
      if (style === 'detailed' && competitor.description) {
        blocks.push(createSectionBlock(truncateText(competitor.description, 500)))
      }
    }
    
    if (competitors.length > maxCompetitors) {
      blocks.push(createSectionBlock(`_...and ${competitors.length - maxCompetitors} more competitors_`))
    }
    
    return blocks
  }
  
  /**
   * Format intelligence section
   */
  private static formatIntelligence(intelligence: any, style: string): SlackMessageBlock[] {
    const blocks: SlackMessageBlock[] = []
    
    blocks.push(createHeaderBlock('🧠 Market Intelligence'))
    
    const sections = [
      { key: 'market_insights', title: 'Market Insights', emoji: '📊' },
      { key: 'competitive_landscape', title: 'Competitive Landscape', emoji: '🏟️' },
      { key: 'key_differentiators', title: 'Key Differentiators', emoji: '⭐' },
      { key: 'business_model', title: 'Business Model', emoji: '💼' },
      { key: 'target_market', title: 'Target Market', emoji: '🎯' }
    ]
    
    for (const section of sections) {
      const data = intelligence[section.key]
      if (data && data.content) {
        const maxLength = style === 'summary' ? 200 : style === 'executive' ? 300 : 500
        const content = truncateText(data.content, maxLength)
        const confidence = formatConfidenceScore(data.confidence_score)
        
        blocks.push(createSectionBlock(
          `*${section.emoji} ${section.title}*\n${content}\n_Confidence: ${confidence}_`
        ))
      }
    }
    
    return blocks
  }
  
  /**
   * Format actionable insights section
   */
  private static formatActionableInsights(insights: any): SlackMessageBlock[] {
    const blocks: SlackMessageBlock[] = []
    
    blocks.push(createHeaderBlock('💡 Actionable Insights'))
    
    // Marketing opportunities
    if (insights.marketing_opportunities && insights.marketing_opportunities.length > 0) {
      blocks.push(createSectionBlock('*🎯 Marketing Opportunities*'))
      
      for (const opportunity of insights.marketing_opportunities.slice(0, 3)) {
        const priority = opportunity.priority === 'High' ? '🔴' : 
                        opportunity.priority === 'Medium' ? '🟡' : '🟢'
        blocks.push(createSectionBlock(
          `${priority} *${opportunity.insight}*\n_Action:_ ${opportunity.recommended_action}`
        ))
      }
    }
    
    // Competitive advantages
    if (insights.competitive_advantages && insights.competitive_advantages.length > 0) {
      blocks.push(createSectionBlock('*⚡ Competitive Advantages*'))
      
      const advantages = insights.competitive_advantages.slice(0, 3)
        .map(adv => `• ${adv.advantage} (${adv.impact} impact)`)
        .join('\n')
      
      blocks.push(createSectionBlock(advantages))
    }
    
    // Contact strategy
    if (insights.contact_strategy) {
      blocks.push(createSectionBlock(
        `*📞 Contact Strategy*\n${insights.contact_strategy.approach_recommendations}`
      ))
    }
    
    return blocks
  }
  
  /**
   * Format contacts section
   */
  private static formatContacts(contacts: any[], style: string): SlackMessageBlock[] {
    const blocks: SlackMessageBlock[] = []
    
    blocks.push(createHeaderBlock('👥 Key Contacts'))
    
    const maxContacts = style === 'summary' ? 2 : 5
    const topContacts = contacts.slice(0, maxContacts)
    
    for (const contact of topContacts) {
      const fields = [
        { title: 'Name', value: contact.name },
        { title: 'Title', value: contact.title },
        { title: 'Department', value: contact.department || 'Unknown' },
        { title: 'Seniority', value: contact.seniority || 'Unknown' }
      ]
      
      if (contact.email) {
        fields.push({ title: 'Email', value: contact.email })
      }
      
      blocks.push(createFieldsBlock(fields))
    }
    
    if (contacts.length > maxContacts) {
      blocks.push(createSectionBlock(`_...and ${contacts.length - maxContacts} more contacts_`))
    }
    
    return blocks
  }
  
  /**
   * Format metadata section
   */
  private static formatMetadata(metadata: any): SlackMessageBlock[] {
    const blocks: SlackMessageBlock[] = []
    
    const fields = [
      { title: 'Companies Found', value: metadata.totalCompanies?.toString() || '0' },
      { title: 'Contacts Found', value: metadata.totalContacts?.toString() || '0' },
      { title: 'Processing Time', value: metadata.processingTime || 'Unknown' },
      { title: 'Data Quality', value: metadata.dataQuality || 'Unknown' }
    ]
    
    blocks.push(createFieldsBlock(fields))
    
    if (metadata.sources_used && metadata.sources_used.length > 0) {
      blocks.push(createSectionBlock(`*Data Sources:*\n${formatDataSources(metadata.sources_used)}`))
    }
    
    return blocks
  }
  
  /**
   * Generate preview of formatted message
   */
  static async preview(
    data: OrchestratorResearchData, 
    options?: SlackFormattingOptions
  ): Promise<{ blocks: SlackMessageBlock[]; estimatedLength: number }> {
    const message = await this.format(data, options)
    const estimatedLength = JSON.stringify(message.blocks).length
    
    return {
      blocks: message.blocks || [],
      estimatedLength
    }
  }
}
