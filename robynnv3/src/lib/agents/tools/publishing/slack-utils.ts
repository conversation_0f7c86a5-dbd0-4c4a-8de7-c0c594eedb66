import { env } from '$env/dynamic/private'
import type { 
  SlackWebhookConfig, 
  SlackMessage, 
  SlackMessageBlock,
  PublishingError,
  PublishingResult 
} from '$lib/types/publishing'

// Configuration constants
export const SLACK_CONFIG = {
  maxMessageLength: 3000, // S<PERSON>ck's text limit
  maxBlocksPerMessage: 50, // Slack's block limit
  timeout: 10000, // 10 seconds default timeout
  maxRetries: 3,
  retryDelay: 1000 // 1 second
} as const

// Slack webhook context interface
export interface SlackContext {
  webhookUrl: string
  timeout: number
  retryAttempts: number
}

// Get Slack configuration from webhook URL
export function getSlackContext(config: SlackWebhookConfig): SlackContext {
  if (!config.webhookUrl) {
    throw new Error('Slack webhook URL is required')
  }

  return {
    webhookUrl: config.webhookUrl,
    timeout: config.timeout || SLACK_CONFIG.timeout,
    retryAttempts: config.retryAttempts || SLACK_CONFIG.maxRetries
  }
}

// Validate Slack webhook URL format
export function validateSlackWebhookUrl(url: string): boolean {
  try {
    const parsedUrl = new URL(url)
    return parsedUrl.hostname === 'hooks.slack.com' && 
           parsedUrl.pathname.startsWith('/services/')
  } catch {
    return false
  }
}

// Create headers for Slack webhook requests
export function createSlackHeaders(): Record<string, string> {
  return {
    'Content-Type': 'application/json',
    'User-Agent': 'Robynn-Publishing-System/1.0'
  }
}

// Create timeout controller for requests
export function createTimeoutController(timeoutMs: number) {
  const controller = new AbortController()
  const timeoutId = setTimeout(() => {
    controller.abort()
  }, timeoutMs)

  return { controller, timeoutId }
}

// Parse Slack API errors
export function parseSlackError(error: any, statusCode?: number): PublishingError {
  if (error instanceof Error && error.name === 'AbortError') {
    return {
      type: 'network',
      message: 'Request timed out',
      statusCode: 408,
      retryable: true,
      destination: 'slack',
      timestamp: new Date().toISOString()
    }
  }

  if (statusCode) {
    switch (statusCode) {
      case 400:
        return {
          type: 'validation',
          message: 'Invalid webhook URL or message format',
          statusCode: 400,
          retryable: false,
          destination: 'slack',
          timestamp: new Date().toISOString()
        }
      case 403:
        return {
          type: 'authentication',
          message: 'Webhook URL is invalid or expired',
          statusCode: 403,
          retryable: false,
          destination: 'slack',
          timestamp: new Date().toISOString()
        }
      case 429:
        return {
          type: 'rate_limit',
          message: 'Slack rate limit exceeded. Please try again later.',
          statusCode: 429,
          retryable: true,
          destination: 'slack',
          timestamp: new Date().toISOString()
        }
      case 500:
        return {
          type: 'server_error',
          message: 'Slack server error. Please try again.',
          statusCode: 500,
          retryable: true,
          destination: 'slack',
          timestamp: new Date().toISOString()
        }
      default:
        return {
          type: 'unknown',
          message: error.message || `Slack API error (${statusCode})`,
          statusCode,
          retryable: statusCode >= 500,
          destination: 'slack',
          timestamp: new Date().toISOString()
        }
    }
  }

  return {
    type: 'unknown',
    message: error.message || 'Unknown Slack error occurred',
    statusCode: 0,
    retryable: false,
    destination: 'slack',
    timestamp: new Date().toISOString()
  }
}

// Handle Slack errors with structured responses
export function handleSlackError(error: PublishingError): PublishingResult {
  const baseErrorResponse: PublishingResult = {
    success: false,
    destination: 'slack',
    timestamp: new Date().toISOString(),
    deliveryTime: 0,
    retryCount: 0
  }

  switch (error.type) {
    case 'authentication':
      return {
        ...baseErrorResponse,
        error: 'Slack webhook authentication failed. Please check your webhook URL.'
      }
    case 'rate_limit':
      return {
        ...baseErrorResponse,
        error: 'Slack rate limit exceeded. Please try again in a few minutes.'
      }
    case 'network':
      return {
        ...baseErrorResponse,
        error: 'Network error connecting to Slack. Please check your connection.'
      }
    case 'validation':
      return {
        ...baseErrorResponse,
        error: 'Invalid message format or webhook URL. Please check your configuration.'
      }
    default:
      return {
        ...baseErrorResponse,
        error: `Slack publishing error: ${error.message}`
      }
  }
}

// Truncate text to fit Slack limits
export function truncateText(text: string, maxLength: number = SLACK_CONFIG.maxMessageLength): string {
  if (text.length <= maxLength) {
    return text
  }
  
  return text.substring(0, maxLength - 3) + '...'
}

// Escape Slack markdown characters
export function escapeSlackMarkdown(text: string): string {
  return text
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/\*/g, '\\*')
    .replace(/_/g, '\\_')
    .replace(/~/g, '\\~')
    .replace(/`/g, '\\`')
}

// Format confidence score for display
export function formatConfidenceScore(score: number): string {
  const percentage = Math.round(score * 100)
  if (percentage >= 90) return `🟢 ${percentage}% (High)`
  if (percentage >= 70) return `🟡 ${percentage}% (Medium)`
  return `🔴 ${percentage}% (Low)`
}

// Format data sources for display
export function formatDataSources(sources: string[]): string {
  return sources.map(source => `• ${source}`).join('\n')
}

// Create divider block
export function createDividerBlock(): SlackMessageBlock {
  return {
    type: 'divider'
  }
}

// Create section block with text
export function createSectionBlock(text: string, markdown: boolean = true): SlackMessageBlock {
  return {
    type: 'section',
    text: {
      type: markdown ? 'mrkdwn' : 'plain_text',
      text: truncateText(text)
    }
  }
}

// Create header block
export function createHeaderBlock(text: string): SlackMessageBlock {
  return {
    type: 'header',
    text: {
      type: 'plain_text',
      text: truncateText(text, 150) // Headers have shorter limits
    }
  }
}

// Create fields block for key-value pairs
export function createFieldsBlock(fields: Array<{ title: string; value: string }>): SlackMessageBlock {
  return {
    type: 'section',
    fields: fields.map(field => ({
      type: 'mrkdwn',
      text: `*${escapeSlackMarkdown(field.title)}*\n${escapeSlackMarkdown(field.value)}`
    }))
  }
}

// Validate Slack message structure
export function validateSlackMessage(message: SlackMessage): boolean {
  // Must have either text or blocks
  if (!message.text && (!message.blocks || message.blocks.length === 0)) {
    return false
  }

  // Check blocks limit
  if (message.blocks && message.blocks.length > SLACK_CONFIG.maxBlocksPerMessage) {
    return false
  }

  // Check text length
  if (message.text && message.text.length > SLACK_CONFIG.maxMessageLength) {
    return false
  }

  return true
}

// Sleep utility for retry delays
export async function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms))
}

// Retry logic with exponential backoff
export async function retryWithBackoff<T>(
  fn: () => Promise<T>,
  maxRetries: number,
  baseDelay: number = 1000
): Promise<T> {
  let lastError: Error

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await fn()
    } catch (error) {
      lastError = error as Error

      if (attempt === maxRetries) {
        throw lastError
      }

      // Exponential backoff: 1s, 2s, 4s, 8s...
      const delay = baseDelay * Math.pow(2, attempt)
      await sleep(delay)
    }
  }

  throw lastError!
}

// Generate mock Slack response for testing
export function generateMockSlackResponse(success: boolean = true): any {
  if (success) {
    return {
      ok: true,
      ts: Date.now().toString(),
      channel: 'C1234567890',
      message: {
        type: 'message',
        subtype: 'bot_message',
        text: 'Research results published successfully',
        ts: Date.now().toString(),
        username: 'Robynn Research Bot',
        bot_id: 'B1234567890'
      }
    }
  } else {
    return {
      ok: false,
      error: 'invalid_webhook',
      warning: 'Mock error for testing purposes'
    }
  }
}
