// SvelteKit environment abstraction for main application
// This file provides environment variables using SvelteKit's env module

import { env } from '$env/dynamic/private'

export const getEnvVar = (key: string): string => {
  return env[key] || ''
}

export const getRequiredEnvVar = (key: string): string => {
  const value = env[key]
  if (!value) {
    throw new Error(`Required environment variable ${key} is not set`)
  }
  return value
}

// Common environment variables used across tools
export const ENV_VARS = {
  // API Keys
  OPENAI_API_KEY: () => getEnvVar('OPENAI_API_KEY'),
  ANTHROPIC_API_KEY: () => getEnvVar('ANTHROPIC_API_KEY'),
  GOOGLE_API_KEY: () => getEnvVar('GOOGLE_API_KEY'),
  EXA_API_KEY: () => getEnvVar('EXA_API_KEY'),
  APOLLO_API_KEY: () => getEnvVar('APOLLO_API_KEY'),
  DATAFORSEO_LOGIN: () => getEnvVar('DATAFORSEO_LOGIN'),
  DATAFORSEO_PASSWORD: () => getEnvVar('DATAFORSEO_PASSWORD'),
  SERP_API_KEY: () => getEnvVar('SERP_API_KEY'),
  BRAVE_API_KEY: () => getEnvVar('BRAVE_API_KEY'),
  FIRECRAWL_API_KEY: () => getEnvVar('FIRECRAWL_API_KEY'),
  
  // Service URLs
  APOLLO_API_URL: () => getEnvVar('APOLLO_API_URL') || 'https://api.apollo.io/v1',
  FIRECRAWL_API_URL: () => getEnvVar('FIRECRAWL_API_URL') || 'https://api.firecrawl.dev',
  
  // Configuration
  DEFAULT_LLM_PROVIDER: () => getEnvVar('DEFAULT_LLM_PROVIDER') || 'openai',
  DEFAULT_LLM_MODEL: () => getEnvVar('DEFAULT_LLM_MODEL') || 'gpt-4o-mini',
  
  // AG-UI Feature Flags
  AGUI_ENABLED: () => getEnvVar('AGUI_ENABLED') === 'true',
  AGUI_STREAMING_ENABLED: () => getEnvVar('AGUI_STREAMING_ENABLED') === 'true'
} as const

export type EnvVarKey = keyof typeof ENV_VARS
