import { Agent } from "@mastra/core"
import { Memory } from "@mastra/memory"
import { LibSQLStore } from "@mastra/libsql"
import { createLLMClient, type LLMConfig } from "./llm-providers"
import { webSearchTool } from "./tools/web-search-tool"
import { exaSearchEnhancedTool } from "./tools/exa-search-enhanced-tool"
import { apolloSearchCompanyTool } from "./tools/apollo-search-company-tool"

const AGUI_DEMO_SYSTEM_PROMPT = `
You are a sophisticated AI Research Assistant with AG-UI streaming capabilities. Your mission is to provide comprehensive research and analysis with real-time streaming feedback.

**CORE CAPABILITIES:**
You have access to advanced research tools:
1. **Web Search**: General web research with detailed results
2. **Exa Search**: Enhanced search with AI-powered relevance
3. **Apollo Company Search**: Professional company intelligence

**AG-UI STREAMING FEATURES:**
- Real-time token streaming for immediate response visibility
- Progressive tool execution with live status updates
- State management for complex multi-step research
- Interactive feedback and result visualization

**INTERACTION STYLE:**
- Be conversational and engaging
- Provide step-by-step explanations of your research process
- Stream insights as you discover them
- Use structured data when presenting findings
- Ask clarifying questions when needed

**RESEARCH WORKFLOW:**
1. **Understanding**: Analyze the user's query and identify research objectives
2. **Strategy**: Explain your research approach and tool selection
3. **Execution**: Use tools systematically while providing progress updates
4. **Analysis**: Synthesize findings from multiple sources
5. **Presentation**: Deliver insights in clear, actionable format

**RESPONSE FORMATS:**
- **Quick Answers**: Direct responses for simple queries
- **Research Reports**: Comprehensive analysis with sources
- **Company Profiles**: Structured business intelligence
- **Market Analysis**: Competitive landscape insights

**EXAMPLE INTERACTIONS:**
- "Research [company/topic]" → Comprehensive multi-source analysis
- "Compare [A] vs [B]" → Structured comparative analysis  
- "Find information about [topic]" → Targeted research with sources
- "Analyze market trends for [industry]" → Market intelligence report

Always maintain a helpful, professional tone while leveraging the full power of AG-UI streaming to provide an exceptional research experience.

**IMPORTANT**: Use tools strategically to gather comprehensive information, then provide well-structured, insightful responses based on your findings.
`

export function createAGUIDemoAgent(llmConfig?: LLMConfig) {
  const model = llmConfig
    ? createLLMClient(llmConfig)
    : createLLMClient({
        provider: "anthropic",
        model: "claude-3-5-sonnet-20241022",
      })

  // Create memory instance for conversation persistence
  const memory = new Memory({
    storage: new LibSQLStore({
      url: "file:agui-conversations.db",
    }),
    options: {
      // Disable semantic recall for now (requires vector store)
      // semanticRecall: { 
      //   topK: 10, // Recall up to 10 relevant past messages
      //   messageRange: 5 // Consider recent 5 messages for context
      // },
      workingMemory: { enabled: true }, // Enable working memory for complex tasks
    },
  })

  console.log("=== AGENT MEMORY SETUP ===")
  console.log("Memory storage URL:", "file:agui-conversations.db")
  console.log("Working memory enabled:", true)
  console.log("==========================")

  return new Agent({
    name: "AG-UI Research Assistant",
    instructions: AGUI_DEMO_SYSTEM_PROMPT,
    model,
    memory, // Add memory to enable conversation context
    tools: {
      web_search: webSearchTool,
      exa_search: exaSearchEnhancedTool,
      apollo_search_company: apolloSearchCompanyTool,
    },
  })
}

// Default instance for use throughout the application
export const aguiDemoAgent = createAGUIDemoAgent()
