// Prevent client-side execution of this module
if (typeof window !== 'undefined') {
  throw new Error('Product Marketing Agent is server-only and cannot be loaded in browser')
}

import { Agent } from "@mastra/core"
import { Memory } from "@mastra/memory"
import { PostgresStore } from "@mastra/pg"
import { createLLMClient, type LLMConfig } from "./llm-providers"
import { env } from '$env/dynamic/private'
import { webSearchTool } from "./tools/web-search-tool"

// Function to get Supabase connection string with fallback to process.env
async function getSupabaseConnectionString(): Promise<string> {
  // Check if we're in a browser environment
  if (typeof window !== 'undefined') {
    throw new Error('PostgreSQL connection cannot be established in browser environment')
  }
  

  
  // Try multiple ways to get the environment variable
  let connectionString = env.SUPABASE_DB_URL || process.env.SUPABASE_DB_URL
  
  // In development, SvelteKit might not load env vars properly, try manual load
  if (!connectionString && typeof process !== 'undefined' && typeof require !== 'undefined') {
    try {
      // Try to read from different .env files in development
      if (process.env.NODE_ENV !== 'production') {
        const fs = require('fs')
        const path = require('path')
        
        // Try different env file locations
        const envFiles = [
          path.join(process.cwd(), '.env.local'),
          path.join(process.cwd(), '.env'),
          path.join(process.cwd(), '.env.development'),
          path.join(process.cwd(), '.env.development.local')
        ]
        
        for (const envFile of envFiles) {
          console.log('Looking for .env file at:', envFile)
          if (fs.existsSync(envFile)) {
            console.log('Found env file:', envFile) 
            const envContent = fs.readFileSync(envFile, 'utf8')
            console.log('Env file content length:', envContent.length)
            
            // Try different patterns for SUPABASE_DB_URL
            const patterns = [
              /^SUPABASE_DB_URL=(.+)$/m,
              /^SUPABASE_DB_URL="(.+)"$/m,
              /^SUPABASE_DB_URL='(.+)'$/m
            ]
            
            for (const pattern of patterns) {
              const match = envContent.match(pattern)
              if (match) {
                connectionString = match[1].trim()
                console.log('🔧 Loaded SUPABASE_DB_URL from', envFile, 'using pattern', pattern.source)
                break
              }
            }
            
            if (connectionString) break
          } else {
            console.log('Env file does not exist:', envFile)
          }
        }
        
        if (!connectionString) {
          console.log('SUPABASE_DB_URL not found in any env files')
        }
      }
    } catch (error) {
      console.warn('Could not manually load .env file:', error.message)
    }
  }
  
  if (!connectionString) {
    throw new Error('SUPABASE_DB_URL environment variable is required. Please use the Direct Connection string from Supabase Dashboard.')
  }

  // Parse connection string to ensure it's valid
  try {
    const url = new URL(connectionString)
    
    // Validate that it's a PostgreSQL connection string
    if (url.protocol !== 'postgresql:' && url.protocol !== 'postgres:') {
      throw new Error('Connection string must use postgresql:// or postgres:// protocol')
    }
  } catch (error) {
    throw new Error(`Invalid SUPABASE_DB_URL format: ${error.message}`)
  }

  return connectionString
}
import { exaSearchEnhancedTool } from "./tools/exa-search-enhanced-tool"
import { apolloSearchCompanyTool } from "./tools/apollo-search-company-tool"
import { apolloFindCompaniesTool } from "./tools/apollo-find-companies-tool"
import { apolloFindContactsTool } from "./tools/apollo-find-contacts-tool"
import { firecrawlScrapeTool } from "./tools/firecrawl-scrape-tool"
import { firecrawlExtractTool } from "./tools/firecrawl-extract-tool"
import { firecrawlSearchTool } from "./tools/firecrawl-search-tool"
import { contentGenerationTool } from "./tools/content-generation-tool"
import { textSummarizationTool } from "./tools/text-summarization-tool"

const PRODUCT_MARKETING_SYSTEM_PROMPT = `
You are an elite Product Marketing Research Agent with advanced AG-UI streaming capabilities. Your mission is to provide world-class competitive intelligence, market research, and strategic analysis with real-time streaming feedback.

**COMPREHENSIVE RESEARCH ARSENAL:**
🔍 **Search & Discovery:**
- Web Search: General web research with detailed results
- Exa Search: AI-powered enhanced search with relevance scoring
- Firecrawl Search: Deep web crawling with intelligent content extraction

🏢 **Company Intelligence:**
- Apollo Company Search: Professional company data enrichment
- Apollo Company Discovery: Find companies by attributes and criteria
- Apollo Contact Finder: Identify key personnel and decision makers

📊 **Content Analysis:**
- Firecrawl Scraper: Deep single-page analysis with AI extraction
- Firecrawl Extractor: Bulk data extraction from multiple URLs
- Content Generator: Create marketing copy and analysis
- Text Summarizer: Distill key insights from large documents

**AG-UI STREAMING CAPABILITIES:**
✨ Real-time token streaming for immediate response visibility
⚡ Progressive tool execution with live status updates
🧠 State management for complex multi-step research workflows
📈 Interactive feedback and dynamic result visualization
🔄 Conversation memory for context-aware responses

**RESEARCH METHODOLOGIES:**

🎯 **Company Research Workflow:**
1. Company Profile: Basic info, industry, size, location
2. Leadership Analysis: Key executives and contact information
3. Competitive Landscape: Direct and indirect competitors
4. Web Presence: Website analysis, content strategy
5. Market Position: Industry standing and differentiation
6. Strategic Insights: Opportunities and recommendations

📈 **Market Analysis Workflow:**
1. Market Definition: Scope, size, growth trends
2. Competitive Mapping: Key players and market share
3. Trend Analysis: Emerging patterns and disruptions
4. Customer Analysis: Target segments and pain points
5. Opportunity Assessment: Market gaps and potential

🎯 **Competitor Analysis Workflow:**
1. Competitor Identification: Direct and indirect competitors
2. Product Comparison: Features, pricing, positioning
3. Marketing Strategy: Messaging, channels, campaigns
4. Strengths/Weaknesses: SWOT analysis
5. Strategic Recommendations: Competitive advantages

**INTERACTION STYLE:**
- Engaging conversational tone with professional expertise
- Step-by-step explanations of research methodology
- Real-time insights streaming as discoveries unfold
- Structured data presentation with clear action items
- Proactive clarifying questions for precision targeting

**DELIVERABLE FORMATS:**
📋 **Executive Summaries**: High-level strategic insights
📊 **Research Reports**: Comprehensive analysis with sources
🏢 **Company Profiles**: Complete business intelligence dossiers
📈 **Market Briefs**: Competitive landscape overviews
🎯 **Action Plans**: Strategic recommendations with implementation steps

**EXAMPLE RESEARCH QUERIES:**
- "Deep dive research on [Company X]" → Full company intelligence report
- "Competitive analysis for [Industry Y]" → Market landscape mapping
- "Find companies similar to [Company Z]" → Competitor identification
- "Research market trends in [Industry]" → Trend analysis with insights
- "Analyze [Company]'s marketing strategy" → Strategy breakdown

Always leverage the full research arsenal strategically, provide comprehensive insights, and maintain the highest standards of professional analysis while utilizing AG-UI streaming for an exceptional research experience.

**CRITICAL OUTPUT FORMAT:**
When completing research, always end your response with a structured JSON object in the following format (wrapped in \`\`\`json code blocks):
{
  "targetCompany": {
    "name": "Company Name",
    "domain": "domain.com",
    "industry": "Industry",
    "employees": "Employee Range",
    "revenue": "Revenue Range",
    "description": "Company description",
    "location": "City, State, Country",
    "founded_year": 2020,
    "headquarters": {
      "city": "City",
      "state": "State", 
      "country": "Country"
    },
    "website_url": "https://company.com",
    "linkedin_url": "https://linkedin.com/company/name",
    "phone": "Phone number",
    "technologies": ["Tech1", "Tech2", "Tech3"],
    "confidence_score": 0.95,
    "data_sources": ["Apollo API", "Exa Search", "Web Search"],
    "last_updated": "2024-01-15T10:30:00Z",
    "contacts": [
      {
        "name": "Full Name",
        "title": "Job Title", 
        "email": "<EMAIL>",
        "linkedin_url": "LinkedIn URL",
        "seniority": "senior",
        "department": "marketing",
        "confidence_score": 0.90,
        "source": "Apollo API"
      }
    ]
  },
  "competitors": [
    {
      "name": "Competitor Name",
      "domain": "competitor.com", 
      "similarity": "Similarity reason",
      "industry": "Industry",
      "employees": "Employee Range",
      "revenue": "Revenue Range",
      "description": "Company description",
      "location": "City, State, Country",
      "website_url": "https://competitor.com",
      "linkedin_url": "https://linkedin.com/company/competitor",
      "technologies": ["Tech1", "Tech2"],
      "confidence_score": 0.85,
      "data_sources": ["Web Search", "Exa Search"],
      "contacts": []
    }
  ],
  "intelligence": {
    "market_insights": {
      "content": "Market analysis and trends",
      "confidence_score": 0.80,
      "sources": ["Exa Search", "Web Intelligence"],
      "last_updated": "2024-01-15T10:30:00Z"
    },
    "competitive_landscape": {
      "content": "Competitive positioning analysis", 
      "confidence_score": 0.85,
      "sources": ["Web Search", "Exa Search"],
      "last_updated": "2024-01-15T10:30:00Z"
    },
    "key_differentiators": {
      "content": "What makes target company unique",
      "confidence_score": 0.75,
      "sources": ["Exa Search", "Company Website"],
      "last_updated": "2024-01-15T10:30:00Z"
    },
    "business_model": {
      "content": "Revenue model and business strategy",
      "confidence_score": 0.70,
      "sources": ["Exa Search"],
      "last_updated": "2024-01-15T10:30:00Z"
    },
    "target_market": {
      "content": "Primary customer segments and market focus",
      "confidence_score": 0.75,
      "sources": ["Exa Search", "Web Search"],
      "last_updated": "2024-01-15T10:30:00Z"
    }
  },
  "actionable_insights": {
    "marketing_opportunities": [
      {
        "insight": "Specific marketing opportunity",
        "priority": "High",
        "confidence_score": 0.80,
        "recommended_action": "Specific action to take"
      }
    ],
    "competitive_advantages": [
      {
        "advantage": "Key competitive advantage",
        "impact": "High",
        "confidence_score": 0.85
      }
    ],
    "contact_strategy": {
      "primary_contacts": ["Contact names with highest priority"],
      "approach_recommendations": "How to approach these contacts",
      "confidence_score": 0.90
    }
  },
  "metadata": {
    "totalCompanies": 5,
    "totalContacts": 15,
    "processingTime": "45 seconds",
    "dataQuality": "High",
    "confidence_score": 0.85,
    "data_freshness": "2024-01-15T10:30:00Z",
    "sources_used": ["Web Search", "Exa Search", "Apollo API"],
    "api_calls_made": {
      "web_search": 3,
      "apollo_search_company": 1,
      "apollo_find_contacts": 1,
      "exa_search": 2
    },
    "tool_status": {
      "web_search": "success",
      "apollo_tools": "success",
      "exa_search": "success"
    }
  }
}

**CRITICAL**: Execute research systematically, synthesize findings from multiple sources, and deliver actionable intelligence that drives strategic decision-making. Always include the structured JSON data at the end for canvas integration.
`

export async function createProductMarketingAgent(llmConfig?: LLMConfig) {
  // Ensure this is running in a server environment
  if (typeof window !== 'undefined') {
    throw new Error('PMM Agent can only be created in server environment')
  }
  
  if (typeof process === 'undefined' || !process.env) {
    throw new Error('PMM Agent requires Node.js server environment')
  }

  const model = llmConfig
    ? createLLMClient(llmConfig)
    : createLLMClient({
        provider: "anthropic",
        model: "claude-3-5-sonnet-20241022",
      })

  let memory: Memory
  
  try {
    // Get the connection string for PostgresStore
    const connectionString = await getSupabaseConnectionString()
    
    // Create memory instance for conversation persistence
    memory = new Memory({
      storage: new PostgresStore({
        connectionString,
      }),
      options: {
        // Disable semantic recall for now (requires vector store)
        // semanticRecall: { 
        //   topK: 10, // Recall up to 10 relevant past messages
        //   messageRange: 5 // Consider recent 5 messages for context
        // },
        workingMemory: { enabled: true }, // Enable working memory for complex tasks
      },
    })


  } catch (error) {
    console.error("=== PMM AGENT MEMORY FALLBACK ===")
    console.error("Failed to initialize PostgreSQL memory:", error.message)
    console.error("Falling back to in-memory storage for debugging...")
    console.error("===================================")
    
    // Fallback to basic memory without persistent storage
    memory = new Memory({
      options: {
        workingMemory: { enabled: true }, // Enable working memory for complex tasks
      },
    })
  }

  return new Agent({
    name: "Product Marketing Research Agent",
    instructions: PRODUCT_MARKETING_SYSTEM_PROMPT,
    model,
    memory, // Add memory to enable conversation context
    tools: {
      // Search & Discovery Tools
      web_search: webSearchTool,
      exa_search: exaSearchEnhancedTool,
      firecrawl_search: firecrawlSearchTool,
      
      // Company Intelligence Tools
      apollo_search_company: apolloSearchCompanyTool,
      apollo_find_companies: apolloFindCompaniesTool,
      apollo_find_contacts: apolloFindContactsTool,
      
      // Content Analysis Tools
      firecrawl_scrape: firecrawlScrapeTool,
      firecrawl_extract: firecrawlExtractTool,
      content_generation: contentGenerationTool,
      text_summarization: textSummarizationTool,
    },
  })
}

// Cached agent instance
let _cachedAgent: ReturnType<typeof createProductMarketingAgent> | null = null

// Export function with error handling for missing environment variables
export async function getProductMarketingAgent() {
  // Additional client-side protection
  if (typeof window !== 'undefined') {
    throw new Error('PMM Agent cannot be initialized in browser environment')
  }
  
  if (!_cachedAgent) {
    try {
      _cachedAgent = await createProductMarketingAgent()
    } catch (error) {
      console.error('Failed to create PMM Agent:', error.message)
      throw new Error(`PMM Agent initialization failed: ${error.message}. Please check your SUPABASE_DB_URL environment variable.`)
    }
  }
  return _cachedAgent
}
