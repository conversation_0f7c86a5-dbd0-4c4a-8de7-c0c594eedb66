// AG-UI Conversation Store
// Manages conversation state with AG-UI event stream integration

import { writable, derived, type Writable, type Readable } from 'svelte/store'
import { EventType, type AGUIEventUnion, type ConversationState } from './event-types'
import { AGUIStreamClient, type StreamConfig } from './stream-client'
import { replaceResearchData, addToolResult, tryParseResearchData, addNote, clearCanvas } from './canvas-store'

// Initial conversation state
const initialState: ConversationState = {
  messages: [],
  toolCalls: [],
  state: {},
  isLoading: false,
  runId: undefined,
  conversationId: undefined
}

// Create the main conversation store
export const conversationStore: Writable<ConversationState> = writable(initialState)

// Derived stores for specific data
export const messages: Readable<ConversationState['messages']> = derived(
  conversationStore,
  ($store) => $store.messages
)

export const toolCalls: Readable<ConversationState['toolCalls']> = derived(
  conversationStore,
  ($store) => $store.toolCalls
)

export const isLoading: Readable<boolean> = derived(
  conversationStore,
  ($store) => $store.isLoading
)

export const currentState: Readable<Record<string, any>> = derived(
  conversationStore,
  ($store) => $store.state
)

// AG-UI Stream Manager Class
export class AGUIConversationManager {
  private streamClient?: AGUIStreamClient
  private currentMessageId?: string

  constructor(private store: Writable<ConversationState> = conversationStore) {}

  // Send a message and start streaming
  async sendMessage(endpoint: string, message: string, context?: Record<string, any>): Promise<void> {
    // Get or generate conversation ID
    let conversationId: string
    let isFirstMessage = false
    
    this.store.update(state => {
      conversationId = state.conversationId || this.generateId()
      isFirstMessage = state.messages.length === 0
      
      console.log("=== CONVERSATION STORE DEBUG ===")
      console.log("Existing conversation ID:", state.conversationId)
      console.log("Using conversation ID:", conversationId)
      console.log("Message:", message)
      console.log("Context:", context)
      console.log("Is first message:", isFirstMessage)
      console.log("================================")
      
      return {
        ...state,
        conversationId,
        messages: [...state.messages, {
          id: this.generateId(),
          role: 'user' as const,
          content: message,
          timestamp: new Date()
        }],
        isLoading: true
      }
    })
    
    // Clear canvas when starting a new conversation
    if (isFirstMessage) {
      console.log('=== CLEARING CANVAS FOR NEW CONVERSATION ===')
      clearCanvas()
    }

    // Prepare stream configuration
    const streamConfig: StreamConfig = {
      endpoint,
      body: {
        message, // Send message directly as expected by server
        conversationId: conversationId!, // Include conversation ID for memory
        context
      },
      onEvent: (event) => this.handleEvent(event),
      onError: (error) => this.handleError(error),
      onComplete: () => this.handleComplete()
    }

    console.log("=== STREAM CONFIG DEBUG ===")
    console.log("Endpoint:", endpoint)
    console.log("Stream body:", JSON.stringify(streamConfig.body, null, 2))
    console.log("=============================")

    try {
      // Disconnect existing stream if any
      this.disconnect()

      // Create new stream connection
      this.streamClient = new AGUIStreamClient()
      await this.streamClient.connect(streamConfig)
    } catch (error) {
      this.handleError(error instanceof Error ? error : new Error('Connection failed'))
    }
  }

  // Handle incoming AG-UI events
  private handleEvent(event: AGUIEventUnion): void {
    console.log('AG-UI Event:', event)

    switch (event.type) {
      case EventType.RUN_STARTED:
        this.store.update(state => ({
          ...state,
          runId: event.runId,
          isLoading: true
        }))
        break

      case EventType.TEXT_MESSAGE_START:
        this.currentMessageId = event.messageId
        // Add placeholder assistant message
        this.store.update(state => ({
          ...state,
          messages: [...state.messages, {
            id: event.messageId,
            role: 'assistant' as const,
            content: '',
            timestamp: new Date()
          }]
        }))
        break

      case EventType.TEXT_MESSAGE_CONTENT:
        if (event.messageId === this.currentMessageId) {
          this.store.update(state => ({
            ...state,
            messages: state.messages.map(msg => 
              msg.id === event.messageId 
                ? { ...msg, content: msg.content + event.delta }
                : msg
            )
          }))
        }
        break

      case EventType.TEXT_MESSAGE_END:
        this.currentMessageId = undefined
        break

      case EventType.TOOL_CALL_START:
        this.store.update(state => ({
          ...state,
          toolCalls: [...state.toolCalls, {
            id: event.toolCallId,
            name: event.toolName,
            args: {},
            status: 'pending' as const
          }]
        }))
        break

      case EventType.TOOL_CALL_ARGS:
        this.store.update(state => ({
          ...state,
          toolCalls: state.toolCalls.map(tool =>
            tool.id === event.toolCallId
              ? { ...tool, args: event.args }
              : tool
          )
        }))
        break

      case EventType.TOOL_CALL_END:
        this.store.update(state => ({
          ...state,
          toolCalls: state.toolCalls.map(tool =>
            tool.id === event.toolCallId
              ? { ...tool, result: event.result, status: 'completed' as const }
              : tool
          )
        }))
        
        // Auto-add completed tool results to canvas
        if (event.result) {
          // Get current state to find tool name
          let toolName = 'unknown'
          this.store.subscribe(state => {
            const tool = state.toolCalls.find(t => t.id === event.toolCallId)
            if (tool) toolName = tool.name
          })()
          
          console.log('=== AUTO-ADDING TOOL RESULT TO CANVAS ===')
          console.log(`Tool: ${toolName}`, event.result)
          addToolResult(toolName, event.result)
        }
        break

      case EventType.TOOL_CALL_ERROR:
        this.store.update(state => ({
          ...state,
          toolCalls: state.toolCalls.map(tool =>
            tool.id === event.toolCallId
              ? { ...tool, error: event.error, status: 'error' as const }
              : tool
          )
        }))
        break

      case EventType.STATE_SNAPSHOT:
        this.store.update(state => ({
          ...state,
          state: { ...event.snapshot }
        }))
        
        // Auto-update canvas with research data if available
        if (event.snapshot && typeof event.snapshot === 'object') {
          const researchData = tryParseResearchData(JSON.stringify(event.snapshot))
          if (researchData) {
            console.log('=== AUTO-ADDING RESEARCH TO CANVAS ===')
            console.log('Research data found in state snapshot:', researchData)
            replaceResearchData(researchData)
          }
        }
        break

      case EventType.STATE_DELTA:
        this.store.update(state => {
          const newState = { ...state.state }
          
          // Apply JSON Patch operations
          for (const delta of event.delta) {
            const pathParts = delta.path.split('/').filter(p => p !== '')
            
            if (delta.op === 'add' || delta.op === 'replace') {
              this.setNestedValue(newState, pathParts, delta.value)
            } else if (delta.op === 'remove') {
              this.removeNestedValue(newState, pathParts)
            }
          }
          
          return { ...state, state: newState }
        })
        break

      case EventType.RUN_FINISHED:
        this.store.update(state => ({
          ...state,
          isLoading: false
        }))
        break

      case EventType.ERROR:
        console.error('AG-UI Error:', event.error)
        this.store.update(state => ({
          ...state,
          isLoading: false
        }))
        break
    }
  }

  private handleError(error: Error): void {
    console.error('AG-UI Stream Error:', error)
    this.store.update(state => ({
      ...state,
      isLoading: false
    }))
  }

  private handleComplete(): void {
    console.log('AG-UI Stream Complete')
    this.store.update(state => ({
      ...state,
      isLoading: false
    }))
  }

  // Helper methods for state manipulation
  private setNestedValue(obj: any, path: string[], value: any): void {
    let current = obj
    for (let i = 0; i < path.length - 1; i++) {
      const key = path[i]
      if (!(key in current) || typeof current[key] !== 'object') {
        current[key] = {}
      }
      current = current[key]
    }
    current[path[path.length - 1]] = value
  }

  private removeNestedValue(obj: any, path: string[]): void {
    let current = obj
    for (let i = 0; i < path.length - 1; i++) {
      const key = path[i]
      if (!(key in current)) return
      current = current[key]
    }
    delete current[path[path.length - 1]]
  }

  // Disconnect stream
  disconnect(): void {
    if (this.streamClient) {
      this.streamClient.disconnect()
      this.streamClient = undefined
    }
  }

  // Clear conversation
  clear(): void {
    this.disconnect()
    this.store.set(initialState)
  }

  // Start new conversation (clear but keep same conversation ID)
  newConversation(): void {
    this.disconnect()
    this.store.update(state => ({
      ...initialState,
      conversationId: this.generateId() // Generate new conversation ID
    }))
  }

  private generateId(): string {
    return `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }
}

// Create default conversation manager instance
export const conversationManager = new AGUIConversationManager()

// Export helper functions for common operations
export const sendMessage = (endpoint: string, message: string, context?: Record<string, any>) => 
  conversationManager.sendMessage(endpoint, message, context)

export const clearConversation = () => conversationManager.clear()

export const disconnectStream = () => conversationManager.disconnect()

// Manual canvas operations
export const addMessageToCanvas = (messageContent: string, title?: string) => {
  console.log('=== ADD MESSAGE TO CANVAS CALLED ===')
  console.log('Message content length:', messageContent.length)
  console.log('Message preview:', messageContent.substring(0, 200))
  
  // Try to parse as research data first
  const researchData = tryParseResearchData(messageContent)
  if (researchData) {
    console.log('=== MANUALLY ADDING RESEARCH TO CANVAS ===')
    console.log('Research data found:', researchData)
    replaceResearchData(researchData)
    return 'research'
  }
  
  // Otherwise add as note
  console.log('=== MANUALLY ADDING NOTE TO CANVAS ===')
  console.log('Adding as note with title:', title)
  addNote(messageContent, title || 'Research Insight')
  return 'note'
}

// Clear canvas when new conversation starts
export const clearCanvasOnNewConversation = () => {
  console.log('=== CLEARING CANVAS FOR NEW CONVERSATION ===')
  import('./canvas-store').then(({ clearCanvas }) => {
    clearCanvas()
  })
}
