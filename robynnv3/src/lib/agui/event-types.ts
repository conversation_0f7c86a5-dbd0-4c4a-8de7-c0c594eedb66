// AG-UI Event Types
// Based on AG-UI protocol specification

export enum EventType {
  // Runtime events
  RUN_STARTED = 'RUN_STARTED',
  RUN_FINISHED = 'RUN_FINISHED',
  
  // Message events
  TEXT_MESSAGE_START = 'TEXT_MESSAGE_START',
  TEXT_MESSAGE_CONTENT = 'TEXT_MESSAGE_CONTENT',
  TEXT_MESSAGE_END = 'TEXT_MESSAGE_END',
  
  // Tool call events
  TOOL_CALL_START = 'TOOL_CALL_START',
  TOOL_CALL_ARGS = 'TOOL_CALL_ARGS',
  TOOL_CALL_END = 'TOOL_CALL_END',
  TOOL_CALL_ERROR = 'TOOL_CALL_ERROR',
  
  // State events
  STATE_SNAPSHOT = 'STATE_SNAPSHOT',
  STATE_DELTA = 'STATE_DELTA',
  
  // Error events
  ERROR = 'ERROR'
}

// Base event interface
export interface AGUIEvent {
  type: EventType
  timestamp?: string
  runId?: string
}

// Text message events
export interface TextMessageStartEvent extends AGUIEvent {
  type: EventType.TEXT_MESSAGE_START
  messageId: string
}

export interface TextMessageContentEvent extends AGUIEvent {
  type: EventType.TEXT_MESSAGE_CONTENT
  messageId: string
  delta: string
}

export interface TextMessageEndEvent extends AGUIEvent {
  type: EventType.TEXT_MESSAGE_END
  messageId: string
}

// Tool call events
export interface ToolCallStartEvent extends AGUIEvent {
  type: EventType.TOOL_CALL_START
  toolCallId: string
  toolName: string
}

export interface ToolCallArgsEvent extends AGUIEvent {
  type: EventType.TOOL_CALL_ARGS
  toolCallId: string
  args: Record<string, any>
}

export interface ToolCallEndEvent extends AGUIEvent {
  type: EventType.TOOL_CALL_END
  toolCallId: string
  result: any
}

export interface ToolCallErrorEvent extends AGUIEvent {
  type: EventType.TOOL_CALL_ERROR
  toolCallId: string
  error: string
}

// State events
export interface StateSnapshotEvent extends AGUIEvent {
  type: EventType.STATE_SNAPSHOT
  snapshot: Record<string, any>
}

export interface StateDeltaEvent extends AGUIEvent {
  type: EventType.STATE_DELTA
  delta: Array<{
    op: 'add' | 'remove' | 'replace'
    path: string
    value?: any
  }>
}

// Runtime events
export interface RunStartedEvent extends AGUIEvent {
  type: EventType.RUN_STARTED
  runId: string
}

export interface RunFinishedEvent extends AGUIEvent {
  type: EventType.RUN_FINISHED
  runId: string
  status: 'success' | 'error' | 'cancelled'
}

// Error events
export interface ErrorEvent extends AGUIEvent {
  type: EventType.ERROR
  error: string
  code?: string
}

// Union type for all events
export type AGUIEventUnion = 
  | TextMessageStartEvent
  | TextMessageContentEvent  
  | TextMessageEndEvent
  | ToolCallStartEvent
  | ToolCallArgsEvent
  | ToolCallEndEvent
  | ToolCallErrorEvent
  | StateSnapshotEvent
  | StateDeltaEvent
  | RunStartedEvent
  | RunFinishedEvent
  | ErrorEvent

// Helper types
export interface ConversationState {
  messages: Array<{
    id: string
    role: 'user' | 'assistant'
    content: string
    timestamp: Date
  }>
  toolCalls: Array<{
    id: string
    name: string
    args: Record<string, any>
    result?: any
    error?: string
    status: 'pending' | 'completed' | 'error'
  }>
  state: Record<string, any>
  isLoading: boolean
  runId?: string
  conversationId?: string // Add conversation ID for memory tracking
}
