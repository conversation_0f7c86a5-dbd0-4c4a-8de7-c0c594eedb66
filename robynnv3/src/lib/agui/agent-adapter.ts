// AG-UI Agent Adapter
// Wraps Mastra agents to provide AG-UI compatible streaming

import type { Agent } from '@mastra/core'
import { EventType, type AGUIEventUnion, type RunStartedEvent, type RunFinishedEvent, type TextMessageStartEvent, type TextMessageContentEvent, type TextMessageEndEvent, type ToolCallStartEvent, type ToolCallArgsEvent, type ToolCallEndEvent, type StateSnapshotEvent } from './event-types'

export interface AGUIMessage {
  role: 'user' | 'assistant' | 'system'
  content: string
}

export interface AGUIAgentConfig {
  runId?: string
  initialState?: Record<string, any>
  enableToolEmission?: boolean
}

export class AGUIAgentAdapter {
  private agent: Agent
  private config: AGUIAgentConfig

  constructor(agent: Agent, config: AGUIAgentConfig = {}) {
    this.agent = agent
    this.config = {
      enableToolEmission: true,
      ...config
    }
  }

  async* generateStream(messages: AGUIMessage[], memoryOptions?: any): AsyncGenerator<AGUIEventUnion> {
    const runId = this.config.runId || this.generateRunId()
    const messageId = this.generateMessageId()

    try {
      // Start run
      yield {
        type: EventType.RUN_STARTED,
        runId,
        timestamp: new Date().toISOString()
      } as RunStartedEvent

      // Initialize state snapshot
      if (this.config.initialState) {
        yield {
          type: EventType.STATE_SNAPSHOT,
          snapshot: this.config.initialState,
          timestamp: new Date().toISOString(),
          runId
        } as StateSnapshotEvent
      }

      // Start text message
      yield {
        type: EventType.TEXT_MESSAGE_START,
        messageId,
        timestamp: new Date().toISOString(),
        runId
      } as TextMessageStartEvent

      // Convert messages to Mastra format
      const mastraMessages = messages.map(msg => ({
        role: msg.role,
        content: msg.content
      }))

      // Execute agent with tool interception if enabled
      if (this.config.enableToolEmission) {
        // Add debug logging for memory
        console.log("=== AG-UI ADAPTER DEBUG ===")
        console.log("Memory options received:", JSON.stringify(memoryOptions, null, 2))
        console.log("Mastra messages:", mastraMessages)
        console.log("===========================")
        
        // For now, we'll use the standard agent execution
        // In a full implementation, we'd need to intercept tool calls
        const response = await this.agent.generate(mastraMessages, memoryOptions)
        
        // Stream the response text
        const text = response.text || ''
        
        // Simulate token streaming by chunking the response
        const chunks = this.chunkText(text, 50)
        for (const chunk of chunks) {
          yield {
            type: EventType.TEXT_MESSAGE_CONTENT,
            messageId,
            delta: chunk,
            timestamp: new Date().toISOString(),
            runId
          } as TextMessageContentEvent
          
          // Add small delay to simulate streaming
          await this.delay(50)
        }
      } else {
        // Simple execution without tool emission
        console.log("=== AG-UI ADAPTER DEBUG (No Tool Emission) ===")
        console.log("Memory options received:", JSON.stringify(memoryOptions, null, 2))
        console.log("============================================")
        
        const response = await this.agent.generate(mastraMessages, memoryOptions)
        const text = response.text || ''
        
        yield {
          type: EventType.TEXT_MESSAGE_CONTENT,
          messageId,
          delta: text,
          timestamp: new Date().toISOString(),
          runId
        } as TextMessageContentEvent
      }

      // End text message
      yield {
        type: EventType.TEXT_MESSAGE_END,
        messageId,
        timestamp: new Date().toISOString(),
        runId
      } as TextMessageEndEvent

      // Finish run
      yield {
        type: EventType.RUN_FINISHED,
        runId,
        status: 'success',
        timestamp: new Date().toISOString()
      } as RunFinishedEvent

    } catch (error) {
      // Error handling
      yield {
        type: EventType.ERROR,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
        runId
      }

      yield {
        type: EventType.RUN_FINISHED,
        runId,
        status: 'error',
        timestamp: new Date().toISOString()
      } as RunFinishedEvent
    }
  }

  private generateRunId(): string {
    return `run_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private generateToolCallId(): string {
    return `tool_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private chunkText(text: string, chunkSize: number): string[] {
    const chunks: string[] = []
    for (let i = 0; i < text.length; i += chunkSize) {
      chunks.push(text.slice(i, i + chunkSize))
    }
    return chunks
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
}

// Factory function to create AG-UI compatible agent
export function createAGUIAgent(agent: Agent, config?: AGUIAgentConfig): AGUIAgentAdapter {
  return new AGUIAgentAdapter(agent, config)
}
