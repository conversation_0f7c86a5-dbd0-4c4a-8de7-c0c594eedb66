// Firecrawl API Types and Interfaces

// Common types
export interface FirecrawlMetadata {
  title?: string
  description?: string
  keywords?: string[]
  author?: string
  ogTitle?: string
  ogDescription?: string
  ogImage?: string
  statusCode: number
  error?: string | null
}

export interface FirecrawlError {
  type: 'authentication' | 'rate_limit' | 'timeout' | 'server_error' | 'api_error' | 'unknown'
  message: string
  statusCode: number
  retryable: boolean
}

// Scrape API Types
export interface FirecrawlScrapeRequest {
  url: string
  formats?: ('markdown' | 'html' | 'rawHtml' | 'content' | 'links' | 'screenshot')[]
  extractOptions?: {
    onlyMainContent?: boolean
    includeTags?: string[]
    excludeTags?: string[]
    onlyIncludeTags?: string[]
  }
  actions?: Array<{
    type: 'click' | 'wait' | 'scroll' | 'key' | 'screenshot'
    selector?: string
    text?: string
    milliseconds?: number
  }>
  location?: {
    country?: string
    language?: string
  }
  jsonOptions?: {
    schema?: Record<string, any>
    systemPrompt?: string
    userPrompt?: string
  }
  timeout?: number
}

export interface FirecrawlScrapeResult {
  success: boolean
  url: string
  markdown?: string
  html?: string
  rawHtml?: string
  content?: string
  links?: string[]
  screenshot?: string
  metadata: FirecrawlMetadata
  extractedData?: Record<string, any>
}

export interface FirecrawlScrapeResponse {
  success: boolean
  data?: FirecrawlScrapeResult
  error?: string
}

// Extract API Types
export interface FirecrawlExtractRequest {
  urls: string[]
  schema: Record<string, any>
  prompt: string
  allowExternalLinks?: boolean
  limit?: number
  extractOptions?: {
    onlyMainContent?: boolean
    includeTags?: string[]
    excludeTags?: string[]
    onlyIncludeTags?: string[]
  }
}

export interface FirecrawlExtractResult {
  url: string
  extractedData: Record<string, any>
  metadata: FirecrawlMetadata
}

export interface FirecrawlExtractResponse {
  success: boolean
  data?: FirecrawlExtractResult[]
  error?: string
}

// Search API Types
export interface FirecrawlSearchRequest {
  query: string
  limit?: number
  country?: string
  language?: string
  searchOptions?: {
    tbs?: string
    filter?: string
    safe?: 'active' | 'off'
  }
  extractOptions?: {
    onlyMainContent?: boolean
    includeTags?: string[]
    excludeTags?: string[]
    onlyIncludeTags?: string[]
  }
}

export interface FirecrawlSearchResult {
  url: string
  title: string
  markdown?: string
  html?: string
  metadata: FirecrawlMetadata
}

export interface FirecrawlSearchResponse {
  success: boolean
  data?: FirecrawlSearchResult[]
  error?: string
}

// Enhanced data structures for company intelligence
export interface CompanyWebPresence {
  landing_page_analysis?: {
    value_propositions: string[]
    call_to_actions: string[]
    pricing_strategy?: string
    target_messaging?: string
    confidence_score: number
    source: string
    last_updated: string
  }
  content_strategy?: {
    blog_topics: string[]
    seo_focus: string[]
    content_frequency?: string
    content_types: string[]
    confidence_score: number
    source: string
    last_updated: string
  }
  market_positioning?: {
    competitive_mentions: string[]
    industry_trends: string[]
    thought_leadership: string[]
    brand_messaging: string[]
    confidence_score: number
    source: string
    last_updated: string
  }
}

export interface TechnicalAnalysis {
  technology_stack?: {
    technologies: Array<{
      name: string
      version?: string
      category: string
    }>
    frameworks: string[]
    hosting_provider?: string
    confidence_score: number
    source: string
    last_updated: string
  }
  performance_metrics?: {
    page_load_time?: string
    performance_score?: number
    core_web_vitals?: {
      lcp?: string
      fid?: string
      cls?: string
    }
    optimization_suggestions: string[]
    confidence_score: number
    source: string
    last_updated: string
  }
  security_analysis?: {
    ssl_certificate?: {
      issuer: string
      expires: string
    }
    security_score?: number
    security_headers: string[]
    vulnerabilities: string[]
    confidence_score: number
    source: string
    last_updated: string
  }
  mobile_optimization?: {
    mobile_friendly: boolean
    responsive_design: boolean
    mobile_performance_score?: number
    mobile_issues: string[]
    confidence_score: number
    source: string
    last_updated: string
  }
}

// Mock data structures for testing
export interface MockFirecrawlData {
  scrape_results: Record<string, FirecrawlScrapeResult>
  extract_results: Record<string, FirecrawlExtractResult[]>
  search_results: Record<string, FirecrawlSearchResult[]>
  web_presence: CompanyWebPresence
  technical_analysis: TechnicalAnalysis
}

// Tool response enhancement types
export interface EnhancedFirecrawlResponse {
  success: boolean
  data: any
  source: string
  confidence_score: number
  processing_time: string
  metadata?: {
    total_urls?: number
    processed_urls?: number
    unique_domains?: number
    domains?: string[]
    api_calls_made?: Record<string, number>
  }
  error?: string
  fallback_data?: any
  note?: string
}
