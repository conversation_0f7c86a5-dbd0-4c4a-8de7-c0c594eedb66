/**
 * Publishing System Types and Interfaces
 * 
 * This file contains all TypeScript interfaces and types for the publishing system
 * following our established patterns for external API integrations.
 */

// Base publishing interfaces
export interface PublishingDestination {
  id: string
  type: 'slack' | 'webhook' | 'hubspot' | 'salesforce'
  name: string
  description?: string
  enabled: boolean
  config: Record<string, any>
  createdAt: string
  updatedAt: string
  isPlaceholder?: boolean
  comingSoon?: boolean
}

export interface PublishingResult {
  success: boolean
  destination: string
  messageId?: string
  timestamp: string
  deliveryTime: number
  error?: string
  retryCount: number
  previewData?: Record<string, any>
}

// Slack-specific interfaces
export interface SlackWebhookConfig {
  webhookUrl: string
  channel?: string
  username?: string
  iconEmoji?: string
  threadTs?: string
  retryAttempts: number
  timeout: number
}

export interface SlackMessageBlock {
  type: string
  text?: {
    type: string
    text: string
  }
  fields?: Array<{
    type: string
    text: string
  }>
  accessory?: Record<string, any>
}

export interface SlackMessage {
  text?: string
  blocks?: SlackMessageBlock[]
  channel?: string
  username?: string
  icon_emoji?: string
  thread_ts?: string
  unfurl_links?: boolean
  unfurl_media?: boolean
}

export interface SlackFormattingOptions {
  includeMetadata: boolean
  includeContacts: boolean
  includeCompetitors: boolean
  includeInsights: boolean
  messageStyle: 'detailed' | 'summary' | 'executive'
  useThreads: boolean
  mentionUsers?: string[]
}

// Generic webhook interfaces
export interface WebhookConfig {
  url: string
  method: 'POST' | 'PUT' | 'PATCH'
  headers?: Record<string, string>
  authentication?: {
    type: 'none' | 'bearer' | 'basic' | 'api_key'
    token?: string
    username?: string
    password?: string
    apiKeyHeader?: string
  }
  retryAttempts: number
  timeout: number
}

// Research data interfaces (from orchestrator agent)
export interface OrchestratorResearchData {
  targetCompany: {
    name: string
    domain: string
    industry: string
    employees: string
    revenue: string
    description: string
    location: string
    founded_year?: number
    headquarters?: {
      city: string
      state: string
      country: string
    }
    website_url?: string
    linkedin_url?: string
    phone?: string
    technologies?: string[]
    confidence_score: number
    data_sources: string[]
    last_updated: string
    contacts?: Array<{
      name: string
      title: string
      email?: string
      linkedin_url?: string
      seniority: string
      department: string
      confidence_score: number
      source: string
    }>
  }
  competitors?: Array<{
    name: string
    domain: string
    similarity: string
    industry: string
    employees: string
    revenue: string
    description: string
    location: string
    website_url?: string
    linkedin_url?: string
    technologies?: string[]
    confidence_score: number
    data_sources: string[]
    contacts?: Array<any>
  }>
  intelligence?: {
    market_insights?: {
      content: string
      confidence_score: number
      sources: string[]
      last_updated: string
    }
    competitive_landscape?: {
      content: string
      confidence_score: number
      sources: string[]
      last_updated: string
    }
    key_differentiators?: {
      content: string
      confidence_score: number
      sources: string[]
      last_updated: string
    }
    business_model?: {
      content: string
      confidence_score: number
      sources: string[]
      last_updated: string
    }
    target_market?: {
      content: string
      confidence_score: number
      sources: string[]
      last_updated: string
    }
  }
  actionable_insights?: {
    marketing_opportunities?: Array<{
      insight: string
      priority: 'High' | 'Medium' | 'Low'
      confidence_score: number
      recommended_action: string
    }>
    competitive_advantages?: Array<{
      advantage: string
      impact: 'High' | 'Medium' | 'Low'
      confidence_score: number
    }>
    contact_strategy?: {
      primary_contacts: string[]
      approach_recommendations: string
      confidence_score: number
    }
  }
  metadata?: {
    totalCompanies: number
    totalContacts: number
    processingTime: string
    dataQuality: 'High' | 'Medium' | 'Low'
    confidence_score: number
    data_freshness: string
    sources_used: string[]
    api_calls_made: Record<string, number>
    tool_status: Record<string, string>
  }
}

// Publishing analytics interfaces
export interface PublishingAnalytics {
  totalPublications: number
  successfulPublications: number
  failedPublications: number
  averageDeliveryTime: number
  destinationBreakdown: Record<string, {
    attempts: number
    successes: number
    failures: number
    averageTime: number
  }>
  lastPublished?: string
  errorSummary: Array<{
    error: string
    count: number
    lastOccurred: string
  }>
}

// Publishing error types
export interface PublishingError {
  type: 'network' | 'authentication' | 'validation' | 'rate_limit' | 'server_error' | 'unknown'
  message: string
  statusCode?: number
  retryable: boolean
  destination: string
  timestamp: string
}

// Publishing registry interfaces
export interface PublishingRegistry {
  destinations: Map<string, PublishingDestination>
  formatters: Map<string, MessageFormatter>
  publishers: Map<string, Publisher>
}

export interface MessageFormatter {
  format(data: OrchestratorResearchData, options?: Record<string, any>): Promise<any>
  preview(data: OrchestratorResearchData, options?: Record<string, any>): Promise<any>
  validate(data: OrchestratorResearchData): boolean
}

export interface Publisher {
  publish(message: any, config: Record<string, any>): Promise<PublishingResult>
  test(config: Record<string, any>): Promise<boolean>
  validateConfig(config: Record<string, any>): boolean
}

// Configuration management interfaces
export interface PublishingConfiguration {
  destinations: PublishingDestination[]
  defaultFormatting: Record<string, any>
  globalSettings: {
    maxRetries: number
    defaultTimeout: number
    enableAnalytics: boolean
    logLevel: 'debug' | 'info' | 'warn' | 'error'
  }
}

// Batch publishing interfaces
export interface BatchPublishingRequest {
  researchData: OrchestratorResearchData
  destinations: Array<{
    id: string
    type: string
    config: Record<string, any>
    formatting?: Record<string, any>
  }>
  parallel: boolean
}

export interface BatchPublishingResult {
  totalDestinations: number
  successfulPublications: number
  failedPublications: number
  results: PublishingResult[]
  overallSuccess: boolean
  processingTime: number
}
