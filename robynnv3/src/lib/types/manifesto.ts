import type { BaseContent } from './content';

/**
 * Manifesto content interface
 */
export interface ManifestoContent extends BaseContent {
  frontmatter: {
    title: string;
    subtitle: string;
    author: string;
    role: string;
    bio: string;
    publishedDate: string;
    lastModified: string;
    readingTime: string;
    category: string;
    tags: string[];
    seoTitle: string;
    seoDescription: string;
    featured: boolean;
  };
}

/**
 * Manifesto page data interface
 */
export interface ManifestoPageData {
  manifesto: ManifestoContent | null;
  meta: {
    title: string;
    description: string;
    author: string;
    publishedDate: string;
    lastModified: string;
    readingTime: string;
    category: string;
    tags: string[];
  };
}

/**
 * Manifesto section interface for structured content
 */
export interface ManifestoSection {
  id: string;
  title: string;
  content: string;
  order: number;
}

/**
 * Reading progress interface for enhanced UX
 */
export interface ReadingProgress {
  currentSection: string;
  percentComplete: number;
  estimatedTimeRemaining: string;
}
