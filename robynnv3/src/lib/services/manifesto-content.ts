import { 
  loadMarkdownContent, 
  getCached<PERSON>ontent, 
  setCachedContent,
  validateContent,
  getFrontmatterValue
} from '$lib/utils/content-loader';
import type { ManifestoContent, ManifestoPageData } from '$lib/types/manifesto';

/**
 * Manifesto content service following the same patterns as HomeContentService
 */
export class ManifestoContentService {
  private static instance: ManifestoContentService;
  
  static getInstance(): ManifestoContentService {
    if (!this.instance) {
      this.instance = new ManifestoContentService();
    }
    return this.instance;
  }

  /**
   * Load manifesto content from the markdown file
   */
  async getManifestoContent(): Promise<ManifestoContent | null> {
    const cacheKey = 'manifesto-content';
    const cached = getCachedContent<ManifestoContent>(cacheKey);
    if (cached) return cached;

    // Load from the content directory (content loader expects files in /src/content/)
    const result = await loadMarkdownContent<ManifestoContent>('manifesto-content');
    
    if (result.success && result.data) {
      // Validate required frontmatter fields
      const validation = validateContent(result.data, [
        'title', 'subtitle', 'author', 'publishedDate', 'seoTitle', 'seoDescription'
      ]);
      
      if (!validation.valid) {
        console.warn('Manifesto content missing required fields:', validation.missing);
      }
      
      setCachedContent(cacheKey, result.data);
      return result.data;
    }
    
    console.error('Failed to load manifesto content:', result.error);
    return null;
  }

  /**
   * Get all manifesto page data including metadata
   */
  async getManifestoPageData(): Promise<ManifestoPageData> {
    const manifesto = await this.getManifestoContent();
    
    if (!manifesto) {
      // Return fallback data structure
      return {
        manifesto: null,
        meta: {
          title: 'The Robynn Manifesto - Rethinking Marketing for Builders',
          description: 'A founder\'s vision for transforming marketing from complexity to conversation through intelligent agents.',
          author: 'Madhukar Kumar',
          publishedDate: new Date().toISOString(),
          lastModified: new Date().toISOString(),
          readingTime: '12 min read',
          category: 'Vision',
          tags: ['manifesto', 'marketing', 'agents', 'growth', 'philosophy']
        }
      };
    }

    return {
      manifesto,
      meta: {
        title: getFrontmatterValue(manifesto.frontmatter, 'seoTitle', manifesto.frontmatter.title),
        description: getFrontmatterValue(manifesto.frontmatter, 'seoDescription', manifesto.frontmatter.subtitle),
        author: manifesto.frontmatter.author,
        publishedDate: manifesto.frontmatter.publishedDate,
        lastModified: manifesto.frontmatter.lastModified,
        readingTime: manifesto.frontmatter.readingTime,
        category: manifesto.frontmatter.category,
        tags: manifesto.frontmatter.tags || []
      }
    };
  }

  /**
   * Extract sections from manifesto content for navigation
   */
  extractSections(content: string): Array<{ id: string; title: string; order: number }> {
    const sections: Array<{ id: string; title: string; order: number }> = [];
    const lines = content.split('\n');
    let order = 0;

    for (const line of lines) {
      // Look for section headers (lines that are standalone and look like titles)
      const trimmed = line.trim();
      if (trimmed && 
          !trimmed.startsWith('<') && 
          !trimmed.includes('.') && 
          trimmed.length > 10 && 
          trimmed.length < 100 &&
          /^[A-Z]/.test(trimmed) &&
          !trimmed.includes('—') &&
          !trimmed.includes('*')) {
        
        // Check if this looks like a section header
        const nextLineIndex = lines.indexOf(line) + 1;
        const nextLine = nextLineIndex < lines.length ? lines[nextLineIndex].trim() : '';
        
        // If next line is empty or starts a paragraph, this is likely a section header
        if (!nextLine || nextLine.length > 50) {
          const id = trimmed.toLowerCase()
            .replace(/[^a-z0-9\s]/g, '')
            .replace(/\s+/g, '-');
          
          sections.push({
            id,
            title: trimmed,
            order: order++
          });
        }
      }
    }

    return sections;
  }
}

/**
 * Convenience function to get the manifesto content service instance
 */
export function getManifestoContentService(): ManifestoContentService {
  return ManifestoContentService.getInstance();
}
