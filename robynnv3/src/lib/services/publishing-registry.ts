import type {
  PublishingDestination,
  PublishingRegistry,
  MessageFormatter,
  Publisher,
  OrchestratorResearchData,
  PublishingResult
} from '$lib/types/publishing'
import { slackWebhookTool, validateSlackConfig, testSlackWebhook } from '$lib/agents/tools/publishing/slack-webhook-tool'
import { SlackMessageFormatter } from '$lib/agents/tools/publishing/slack-message-formatter'
import { createClient } from '@supabase/supabase-js'
import { env } from '$env/dynamic/private'
import { PUBLIC_SUPABASE_URL } from '$env/static/public'
import { SUPABASE_SERVICE_ROLE_KEY } from '$env/static/private'

/**
 * Publishing Registry Service
 * 
 * Manages available publishing destinations, formatters, and publishers.
 * Similar to the existing tool-registry.ts pattern but focused on publishing.
 */
export class PublishingRegistryService {
  private static instance: PublishingRegistryService
  private registry: PublishingRegistry
  private supabase: any

  private constructor() {
    this.registry = {
      destinations: new Map(),
      formatters: new Map(),
      publishers: new Map()
    }

    this.initializeSupabase()
    this.initializeDefaultComponents()
  }

  private initializeSupabase() {
    const supabaseUrl = PUBLIC_SUPABASE_URL || env.PUBLIC_SUPABASE_URL
    const serviceRoleKey = SUPABASE_SERVICE_ROLE_KEY || env.SUPABASE_SERVICE_ROLE_KEY

    if (!supabaseUrl || !serviceRoleKey) {
      console.warn('Supabase credentials not found, publishing registry will use in-memory storage')
      return
    }

    this.supabase = createClient(supabaseUrl, serviceRoleKey, {
      auth: { persistSession: false }
    })
  }
  
  public static getInstance(): PublishingRegistryService {
    if (!PublishingRegistryService.instance) {
      PublishingRegistryService.instance = new PublishingRegistryService()
    }
    return PublishingRegistryService.instance
  }
  
  /**
   * Initialize default formatters and publishers
   */
  private initializeDefaultComponents(): void {
    // Register Slack formatter
    this.registry.formatters.set('slack', new SlackFormatter())
    
    // Register Slack publisher
    this.registry.publishers.set('slack', new SlackPublisher())
    
    // TODO: Add other formatters and publishers
    // this.registry.formatters.set('webhook', new WebhookFormatter())
    // this.registry.publishers.set('webhook', new WebhookPublisher())
  }
  
  /**
   * Register a new publishing destination
   */
  public registerDestination(destination: PublishingDestination): void {
    this.registry.destinations.set(destination.id, destination)
  }
  
  /**
   * Get all registered destinations
   */
  public getDestinations(): PublishingDestination[] {
    return Array.from(this.registry.destinations.values())
  }
  
  /**
   * Get destinations by type
   */
  public getDestinationsByType(type: string): PublishingDestination[] {
    return this.getDestinations().filter(dest => dest.type === type)
  }
  
  /**
   * Get enabled destinations
   */
  public getEnabledDestinations(): PublishingDestination[] {
    return this.getDestinations().filter(dest => dest.enabled)
  }
  
  /**
   * Get destination by ID
   */
  public getDestination(id: string): PublishingDestination | undefined {
    return this.registry.destinations.get(id)
  }
  
  /**
   * Update destination configuration
   */
  public updateDestination(id: string, updates: Partial<PublishingDestination>): boolean {
    const destination = this.registry.destinations.get(id)
    if (!destination) return false
    
    const updated = { ...destination, ...updates, updatedAt: new Date().toISOString() }
    this.registry.destinations.set(id, updated)
    return true
  }
  
  /**
   * Remove destination
   */
  public removeDestination(id: string): boolean {
    return this.registry.destinations.delete(id)
  }
  
  /**
   * Get formatter for destination type
   */
  public getFormatter(type: string): MessageFormatter | undefined {
    return this.registry.formatters.get(type)
  }
  
  /**
   * Get publisher for destination type
   */
  public getPublisher(type: string): Publisher | undefined {
    return this.registry.publishers.get(type)
  }
  
  /**
   * Test destination configuration
   */
  public async testDestination(id: string): Promise<boolean> {
    const destination = this.getDestination(id)
    if (!destination) return false
    
    const publisher = this.getPublisher(destination.type)
    if (!publisher) return false
    
    return await publisher.test(destination.config)
  }
  
  /**
   * Publish to a specific destination
   */
  public async publishToDestination(
    destinationId: string, 
    data: OrchestratorResearchData,
    options?: Record<string, any>
  ): Promise<PublishingResult> {
    const destination = this.getDestination(destinationId)
    if (!destination) {
      throw new Error(`Destination ${destinationId} not found`)
    }
    
    if (!destination.enabled) {
      throw new Error(`Destination ${destinationId} is disabled`)
    }
    
    const formatter = this.getFormatter(destination.type)
    const publisher = this.getPublisher(destination.type)
    
    if (!formatter || !publisher) {
      throw new Error(`No formatter or publisher available for type ${destination.type}`)
    }
    
    // Format the message
    const formattedMessage = await formatter.format(data, options)
    
    // Publish the message
    return await publisher.publish(formattedMessage, destination.config)
  }
  
  /**
   * Publish to multiple destinations
   */
  public async publishToMultiple(
    destinationIds: string[],
    data: OrchestratorResearchData,
    options?: Record<string, any>,
    parallel: boolean = true
  ): Promise<PublishingResult[]> {
    const publishTasks = destinationIds.map(id => 
      this.publishToDestination(id, data, options)
    )
    
    if (parallel) {
      return await Promise.allSettled(publishTasks).then(results =>
        results.map((result, index) => {
          if (result.status === 'fulfilled') {
            return result.value
          } else {
            return {
              success: false,
              destination: destinationIds[index],
              timestamp: new Date().toISOString(),
              deliveryTime: 0,
              retryCount: 0,
              error: result.reason.message || 'Unknown error'
            }
          }
        })
      )
    } else {
      const results: PublishingResult[] = []
      for (const task of publishTasks) {
        try {
          const result = await task
          results.push(result)
        } catch (error) {
          results.push({
            success: false,
            destination: 'unknown',
            timestamp: new Date().toISOString(),
            deliveryTime: 0,
            retryCount: 0,
            error: error instanceof Error ? error.message : 'Unknown error'
          })
        }
      }
      return results
    }
  }
  
  /**
   * Get supported destination types
   */
  public getSupportedTypes(): string[] {
    return Array.from(this.registry.formatters.keys())
  }
  
  /**
   * Validate destination configuration
   */
  public validateDestinationConfig(type: string, config: any): boolean {
    switch (type) {
      case 'slack':
        return validateSlackConfig(config)
      // TODO: Add other validation functions
      default:
        return false
    }
  }

  /**
   * Database Methods - Load destinations from database
   */
  public async loadDestinationsFromDatabase(): Promise<PublishingDestination[]> {
    if (!this.supabase) {
      console.warn('Supabase not initialized, returning empty destinations')
      return []
    }

    try {
      const { data, error } = await this.supabase
        .from('publishing_destinations')
        .select('*')
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Error loading destinations from database:', error)
        return []
      }

      // Convert database format to our interface format
      const destinations: PublishingDestination[] = data.map((row: any) => ({
        id: row.id,
        type: row.type,
        name: row.name,
        description: row.description,
        enabled: row.enabled,
        config: row.config,
        createdAt: row.created_at,
        updatedAt: row.updated_at
      }))

      // Update in-memory registry
      this.registry.destinations.clear()
      destinations.forEach(dest => {
        this.registry.destinations.set(dest.id, dest)
      })

      return destinations
    } catch (error) {
      console.error('Database error loading destinations:', error)
      return []
    }
  }

  /**
   * Save destination to database
   */
  public async saveDestinationToDatabase(destination: Omit<PublishingDestination, 'id' | 'createdAt' | 'updatedAt'>): Promise<PublishingDestination | null> {
    if (!this.supabase) {
      console.warn('Supabase not initialized, cannot save destination')
      return null
    }

    try {
      const { data, error } = await this.supabase
        .from('publishing_destinations')
        .insert({
          type: destination.type,
          name: destination.name,
          description: destination.description,
          enabled: destination.enabled,
          config: destination.config
        })
        .select()
        .single()

      if (error) {
        console.error('Error saving destination to database:', error)
        return null
      }

      const savedDestination: PublishingDestination = {
        id: data.id,
        type: data.type,
        name: data.name,
        description: data.description,
        enabled: data.enabled,
        config: data.config,
        createdAt: data.created_at,
        updatedAt: data.updated_at
      }

      // Update in-memory registry
      this.registry.destinations.set(savedDestination.id, savedDestination)

      return savedDestination
    } catch (error) {
      console.error('Database error saving destination:', error)
      return null
    }
  }

  /**
   * Update destination in database
   */
  public async updateDestinationInDatabase(id: string, updates: Partial<PublishingDestination>): Promise<PublishingDestination | null> {
    if (!this.supabase) {
      console.warn('Supabase not initialized, cannot update destination')
      return null
    }

    try {
      const { data, error } = await this.supabase
        .from('publishing_destinations')
        .update({
          ...(updates.type && { type: updates.type }),
          ...(updates.name && { name: updates.name }),
          ...(updates.description !== undefined && { description: updates.description }),
          ...(updates.enabled !== undefined && { enabled: updates.enabled }),
          ...(updates.config && { config: updates.config }),
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single()

      if (error) {
        console.error('Error updating destination in database:', error)
        return null
      }

      const updatedDestination: PublishingDestination = {
        id: data.id,
        type: data.type,
        name: data.name,
        description: data.description,
        enabled: data.enabled,
        config: data.config,
        createdAt: data.created_at,
        updatedAt: data.updated_at
      }

      // Update in-memory registry
      this.registry.destinations.set(updatedDestination.id, updatedDestination)

      return updatedDestination
    } catch (error) {
      console.error('Database error updating destination:', error)
      return null
    }
  }

  /**
   * Delete destination from database
   */
  public async deleteDestinationFromDatabase(id: string): Promise<boolean> {
    if (!this.supabase) {
      console.warn('Supabase not initialized, cannot delete destination')
      return false
    }

    try {
      const { error } = await this.supabase
        .from('publishing_destinations')
        .delete()
        .eq('id', id)

      if (error) {
        console.error('Error deleting destination from database:', error)
        return false
      }

      // Remove from in-memory registry
      this.registry.destinations.delete(id)

      return true
    } catch (error) {
      console.error('Database error deleting destination:', error)
      return false
    }
  }

  /**
   * Log publishing analytics to database
   */
  public async logPublishingAnalytics(
    destinationId: string,
    researchDataHash: string,
    status: 'success' | 'failed' | 'pending',
    messageCount: number = 1,
    responseData?: any,
    errorMessage?: string
  ): Promise<void> {
    if (!this.supabase) {
      console.warn('Supabase not initialized, cannot log analytics')
      return
    }

    try {
      const { error } = await this.supabase
        .from('publishing_analytics')
        .insert({
          destination_id: destinationId,
          research_data_hash: researchDataHash,
          status,
          message_count: messageCount,
          response_data: responseData,
          error_message: errorMessage
        })

      if (error) {
        console.error('Error logging publishing analytics:', error)
      }
    } catch (error) {
      console.error('Database error logging analytics:', error)
    }
  }
}

/**
 * Slack Message Formatter Implementation
 */
class SlackFormatter implements MessageFormatter {
  async format(data: OrchestratorResearchData, options?: Record<string, any>): Promise<any> {
    return await SlackMessageFormatter.format(data, options)
  }
  
  async preview(data: OrchestratorResearchData, options?: Record<string, any>): Promise<any> {
    return await SlackMessageFormatter.preview(data, options)
  }
  
  validate(data: OrchestratorResearchData): boolean {
    return !!(data && data.targetCompany && data.targetCompany.name)
  }
}

/**
 * Slack Publisher Implementation
 */
class SlackPublisher implements Publisher {
  async publish(message: any, config: Record<string, any>): Promise<PublishingResult> {
    // Send the already formatted message directly to Slack
    try {
      const response = await fetch(config.webhookUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(message)
      })

      if (!response.ok) {
        throw new Error(`Slack webhook failed: ${response.status} ${response.statusText}`)
      }

      return {
        success: true,
        destinationId: config.id || 'slack',
        timestamp: new Date().toISOString(),
        message: 'Message published to Slack successfully'
      }
    } catch (error) {
      return {
        success: false,
        destinationId: config.id || 'slack',
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : 'Unknown error',
        message: 'Failed to publish to Slack'
      }
    }
  }
  
  async test(config: Record<string, any>): Promise<boolean> {
    return await testSlackWebhook(config)
  }
  
  validateConfig(config: Record<string, any>): boolean {
    return validateSlackConfig(config)
  }
}

// Export singleton instance
export const publishingRegistry = PublishingRegistryService.getInstance()

// Export destination type mapping for agent tools
export const PUBLISHING_DESTINATION_MAPPING: Record<string, string[]> = {
  'orchestrator-agent': ['slack', 'webhook'],
  'content-agent': ['slack', 'webhook'],
  'seo-strategist': ['slack', 'webhook']
}

/**
 * Helper function to create a new destination
 */
export function createDestination(
  id: string,
  type: 'slack' | 'webhook' | 'hubspot' | 'salesforce',
  name: string,
  config: Record<string, any>,
  description?: string
): PublishingDestination {
  return {
    id,
    type,
    name,
    description,
    enabled: true,
    config,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
}

/**
 * Helper function to get default destinations for testing
 */
export function getDefaultDestinations(): PublishingDestination[] {
  return [
    createDestination(
      'slack-general',
      'slack',
      'General Slack Channel',
      {
        webhookUrl: 'https://hooks.slack.com/services/EXAMPLE/WEBHOOK/URL',
        channel: '#general',
        username: 'Robynn Research Bot',
        iconEmoji: ':robot_face:',
        retryAttempts: 3,
        timeout: 10000
      },
      'Default Slack channel for research results'
    )
  ]
}
