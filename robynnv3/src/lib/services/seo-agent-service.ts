import type { GapKeyword, NicheKeyword, ProgressStep } from '$lib/stores/seo-agent-store'

export interface SEOAnalysisFilters {
  targetAudience?: string
  regionFocus?: string
  funnelStage?: string
}

export interface NicheDiscoveryFilters {
  industry: string
  location: string
  volumeRange: { min: number; max: number }
  difficultyRange: { min: number; max: number }
  includeQuestions: boolean
  includeLongTail: boolean
}

export interface GapAnalysisFilters {
  yourDomain: string
  competitors: string[]
  location: string
  minVolume: number
  maxDifficulty: number
  gapType: 'all' | 'missing' | 'lower_rank'
}

export interface CompanyProfile {
  name: string
  domain?: string
  overview: string
  products_services: string[]
  competitors: string[]
  target_audience: string
  business_model: string
  market_position: string
}

export interface ContentRecommendation {
  title: string
  target_keywords: string[]
  content_angle: string
  expected_impact: string
  priority_score: number
}

export interface ContentStrategy {
  recommendations: ContentRecommendation[]
  overall_strategy: string
}

export class SEOAgentService {
  constructor(private envSlug: string) {}

  async sendChatMessage(
    message: string,
    outputFormat: string,
    filters: SEOAnalysisFilters = {},
    onProgress?: (step: number, status: string, action: string, progress: number) => void,
    onError?: (error: string) => void
  ): Promise<string> {
    // Build enhanced message with filters and format
    let enhancedMessage = message.trim()
    const filterArray = []
    
    if (filters.targetAudience) filterArray.push(`Target audience: ${filters.targetAudience}`)
    if (filters.regionFocus) filterArray.push(`Region focus: ${filters.regionFocus}`)
    if (filters.funnelStage) filterArray.push(`Funnel stage: ${filters.funnelStage}`)
    
    if (filterArray.length > 0) {
      enhancedMessage = `${enhancedMessage}\n\nAdditional context: ${filterArray.join(", ")}`
    }
    enhancedMessage += `\n\nOutput format: ${outputFormat}`

    try {
      const response = await fetch(`/dashboard/${this.envSlug}/agent-seo?stream=true`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ message: enhancedMessage }),
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      return await this.handleStreamingResponse(response, onProgress, onError)
    } catch (error) {
      console.error("Error sending message:", error)
      throw error
    }
  }

  async sendNicheDiscoveryRequest(
    seedKeywords: string[],
    filters: NicheDiscoveryFilters,
    onProgress?: (step: number, status: string, action: string, progress: number) => void,
    onError?: (error: string) => void
  ): Promise<string> {
    const message = `Perform niche keyword discovery for: ${seedKeywords.join(", ")}. 
    Industry: ${filters.industry}. 
    Location: ${filters.location}. 
    Volume range: ${filters.volumeRange.min}-${filters.volumeRange.max}. 
    Difficulty range: ${filters.difficultyRange.min}-${filters.difficultyRange.max}. 
    Include questions: ${filters.includeQuestions}. 
    Include long-tail: ${filters.includeLongTail}. 
    Output format: table`

    return this.sendChatMessage(message, 'table', {}, onProgress, onError)
  }

  async sendGapAnalysisRequest(
    filters: GapAnalysisFilters,
    onProgress?: (step: number, status: string, action: string, progress: number) => void,
    onError?: (error: string) => void
  ): Promise<string> {
    const message = `Analyze keyword gaps between ${filters.yourDomain} and competitors: ${filters.competitors.join(", ")}. 
    Location: ${filters.location}. 
    Minimum volume: ${filters.minVolume}. 
    Maximum difficulty: ${filters.maxDifficulty}.
    Gap type: ${filters.gapType === "all" ? "Show all gaps" : filters.gapType === "missing" ? "Keywords competitors rank for but we don't" : "Keywords where competitors outrank us"}.
    Output format: table`

    return this.sendChatMessage(message, 'table', {}, onProgress, onError)
  }

  private async handleStreamingResponse(
    response: Response,
    onProgress?: (step: number, status: string, action: string, progress: number) => void,
    onError?: (error: string) => void
  ): Promise<string> {
    const reader = response.body?.getReader()
    const decoder = new TextDecoder()

    if (!reader) {
      throw new Error("No response body")
    }

    let finalResponse = ''
    let partialResponse = ''
    let isPartialResult = false
    let isFallbackResult = false

    while (true) {
      const { done, value } = await reader.read()
      if (done) break

      const chunk = decoder.decode(value)
      const lines = chunk.split("\n")

      for (const line of lines) {
        if (line.startsWith("data: ")) {
          try {
            const data = JSON.parse(line.slice(6))

            if (data.type === "final") {
              finalResponse = data.response
            } else if (data.type === "partial_results") {
              console.log("📊 Partial results notification received")
              isPartialResult = true
              if (onProgress) {
                onProgress(5, "active", "Capturing partial results...", data.progress || 85)
              }
            } else if (data.type === "fallback") {
              console.log("🔄 Fallback strategy activated")
              isFallbackResult = true
              if (onProgress) {
                onProgress(6, "active", "Using fallback analysis...", data.progress || 90)
              }
            } else if (data.type === "fallback_complete") {
              console.log("✅ Fallback analysis complete")
              if (onProgress) {
                onProgress(6, "completed", "Fallback analysis complete", data.progress || 95)
              }
            } else if (data.type === "error") {
              console.error("❌ Agent error received:", data.error)

              // If we have partial results, use them instead of failing
              if (partialResponse) {
                console.log("Using partial results despite error")
                finalResponse = partialResponse
                return finalResponse
              }

              if (onError) {
                onError(data.error)
              }
              throw new Error(data.error)
            } else if (data.step && onProgress) {
              onProgress(data.step, data.status, data.action, data.progress)
            }

            // Capture any response content for partial results
            if (data.response && !finalResponse) {
              partialResponse = data.response
            }
          } catch (e) {
            console.error("Error parsing SSE data:", e)
            console.error("Raw SSE line that failed:", line)
            console.error("Attempted to parse:", line.substring(5))
          }
        }
      }
    }

    // If no final response but we have partial results, use them
    if (!finalResponse && partialResponse) {
      console.log("Using partial response as final result")
      finalResponse = partialResponse
    }

    // Add metadata to response if it's partial or fallback
    if (isPartialResult || isFallbackResult) {
      const metadata = {
        isPartial: isPartialResult,
        isFallback: isFallbackResult,
        timestamp: new Date().toISOString()
      }

      // Add metadata comment to response
      finalResponse = `<!-- Analysis Metadata: ${JSON.stringify(metadata)} -->\n${finalResponse}`
    }

    return finalResponse
  }

  // Parsing functions
  static parseStructuredResponse(response: string) {
    const parsedData = {
      keywords: [] as any[],
      gapKeywords: [] as GapKeyword[],
      nicheKeywords: [] as NicheKeyword[],
      companyProfile: null as CompanyProfile | null,
      contentStrategy: null as ContentStrategy | null,
      recommendations: "",
      analysis: "",
      content: "",
      nicheAnalysis: ""
    }

    // Extract keywords section
    const keywordsMatch = response.match(/<KEYWORDS>(.*?)<\/KEYWORDS>/s)
    if (keywordsMatch) {
      try {
        const keywordsText = keywordsMatch[1].trim()
        console.log("Raw keywords text:", keywordsText)

        // Check if it's a table format (starts with |)
        if (keywordsText.startsWith('|')) {
          parsedData.keywords = this.parseKeywordTable(keywordsText)
        } else {
          // Try to parse as JSON
          const cleanedJson = keywordsText
            .replace(/^[^[\{]*/, '') // Remove any text before the first [ or {
            .replace(/[^\]\}]*$/, '') // Remove any text after the last ] or }
            .trim()

          if ((cleanedJson.startsWith('[') && cleanedJson.endsWith(']')) ||
              (cleanedJson.startsWith('{') && cleanedJson.endsWith('}'))) {
            parsedData.keywords = JSON.parse(cleanedJson)
            console.log("Successfully parsed keywords:", parsedData.keywords)
          } else {
            console.warn("Keywords text is not valid JSON or table format:", cleanedJson)
            parsedData.keywords = []
          }
        }
      } catch (e) {
        console.error("Error parsing keywords:", e)
        console.error("Raw keywords text:", keywordsMatch[1])
        parsedData.keywords = []
      }
    }

    // Extract gap results
    const gapMatch = response.match(/<GAP_RESULTS>(.*?)<\/GAP_RESULTS>/s)
    if (gapMatch) {
      try {
        parsedData.gapKeywords = JSON.parse(gapMatch[1])
      } catch (e) {
        console.error("Error parsing gap results:", e)
      }
    }

    // Extract niche keywords
    const nicheMatch = response.match(/<NICHE_KEYWORDS>(.*?)<\/NICHE_KEYWORDS>/s)
    if (nicheMatch) {
      try {
        parsedData.nicheKeywords = JSON.parse(nicheMatch[1])
      } catch (e) {
        console.error("Error parsing niche keywords:", e)
      }
    }

    // Extract text sections
    const recommendationsMatch = response.match(/<RECOMMENDATIONS>(.*?)<\/RECOMMENDATIONS>/s)
    if (recommendationsMatch) {
      parsedData.recommendations = recommendationsMatch[1].trim()
    }

    const analysisMatch = response.match(/<ANALYSIS>(.*?)<\/ANALYSIS>/s)
    if (analysisMatch) {
      parsedData.analysis = analysisMatch[1].trim()
    }

    const contentMatch = response.match(/<CONTENT>(.*?)<\/CONTENT>/s)
    if (contentMatch) {
      parsedData.content = contentMatch[1].trim()
    }

    const nicheAnalysisMatch = response.match(/<NICHE_ANALYSIS>(.*?)<\/NICHE_ANALYSIS>/s)
    if (nicheAnalysisMatch) {
      parsedData.nicheAnalysis = nicheAnalysisMatch[1].trim()
    }

    // Extract company profile
    const companyMatch = response.match(/<COMPANY_PROFILE>(.*?)<\/COMPANY_PROFILE>/s)
    if (companyMatch) {
      try {
        parsedData.companyProfile = JSON.parse(companyMatch[1])
      } catch (e) {
        console.error("Error parsing company profile:", e)
      }
    }

    // Extract content strategy
    const contentStrategyMatch = response.match(/<CONTENT_STRATEGY>(.*?)<\/CONTENT_STRATEGY>/s)
    if (contentStrategyMatch) {
      try {
        const contentStrategyText = contentStrategyMatch[1].trim()
        console.log("Raw content strategy text:", contentStrategyText)

        // Clean up the JSON text - remove any non-JSON content
        const cleanedJson = contentStrategyText
          .replace(/^[^{]*/, '') // Remove any text before the first {
          .replace(/[^}]*$/, '') // Remove any text after the last }
          .trim()

        if (cleanedJson.startsWith('{') && cleanedJson.endsWith('}')) {
          parsedData.contentStrategy = JSON.parse(cleanedJson)
          console.log("Successfully parsed content strategy:", parsedData.contentStrategy)
        } else {
          console.warn("Content strategy text is not valid JSON format:", cleanedJson)
          // Create fallback content strategy from text
          parsedData.contentStrategy = this.createFallbackContentStrategy(contentStrategyText)
        }
      } catch (e) {
        console.error("Error parsing content strategy:", e)
        console.error("Raw content strategy text:", contentStrategyMatch[1])
        // Create fallback content strategy
        parsedData.contentStrategy = this.createFallbackContentStrategy(contentStrategyMatch[1])
      }
    }

    return parsedData
  }

  private static createFallbackContentStrategy(text: string): ContentStrategy {
    console.log("Creating fallback content strategy from text:", text)

    // Try to extract recommendations from text format
    const recommendations: ContentRecommendation[] = []

    // Look for numbered lists or bullet points that might be recommendations
    const lines = text.split('\n').filter(line => line.trim())
    let currentRecommendation: Partial<ContentRecommendation> = {}

    for (const line of lines) {
      const trimmedLine = line.trim()

      // Look for titles (lines that start with numbers, bullets, or are in quotes)
      if (trimmedLine.match(/^\d+\.|\*|\-|".*"/)) {
        // Save previous recommendation if it exists
        if (currentRecommendation.title) {
          recommendations.push({
            title: currentRecommendation.title,
            target_keywords: currentRecommendation.target_keywords || ['seo', 'content marketing'],
            content_angle: currentRecommendation.content_angle || 'Comprehensive guide',
            expected_impact: currentRecommendation.expected_impact || 'Improved search rankings',
            priority_score: currentRecommendation.priority_score || 7
          })
        }

        // Start new recommendation
        currentRecommendation = {
          title: trimmedLine.replace(/^\d+\.|\*|\-|"/g, '').replace(/"/g, '').trim(),
          target_keywords: ['content marketing', 'seo strategy'],
          content_angle: 'Educational content approach',
          expected_impact: 'Increased organic traffic',
          priority_score: 7
        }
      }
    }

    // Add the last recommendation
    if (currentRecommendation.title) {
      recommendations.push({
        title: currentRecommendation.title,
        target_keywords: currentRecommendation.target_keywords || ['seo', 'content marketing'],
        content_angle: currentRecommendation.content_angle || 'Comprehensive guide',
        expected_impact: currentRecommendation.expected_impact || 'Improved search rankings',
        priority_score: currentRecommendation.priority_score || 7
      })
    }

    // If no recommendations found, create default ones
    if (recommendations.length === 0) {
      recommendations.push(
        {
          title: "SEO Strategy Implementation Guide",
          target_keywords: ["seo strategy", "search optimization", "organic traffic"],
          content_angle: "Step-by-step implementation guide",
          expected_impact: "20-30% increase in organic traffic",
          priority_score: 9
        },
        {
          title: "Content Marketing Best Practices",
          target_keywords: ["content marketing", "content strategy", "audience engagement"],
          content_angle: "Data-driven content approach",
          expected_impact: "Improved user engagement and conversions",
          priority_score: 8
        },
        {
          title: "Technical SEO Optimization",
          target_keywords: ["technical seo", "site optimization", "page speed"],
          content_angle: "Technical implementation focus",
          expected_impact: "Better search engine crawling and indexing",
          priority_score: 7
        }
      )
    }

    return {
      recommendations,
      overall_strategy: "Focus on creating high-quality, SEO-optimized content that addresses user intent and drives organic traffic growth."
    }
  }

  private static parseKeywordTable(tableText: string): any[] {
    console.log("Parsing keyword table:", tableText)

    const lines = tableText.split('\n').filter(line => line.trim() && line.includes('|'))
    const keywords: any[] = []

    // Skip header lines (first 2 lines are usually header and separator)
    const dataLines = lines.slice(2)

    for (const line of dataLines) {
      const cells = line.split('|').map(cell => cell.trim()).filter(cell => cell)

      if (cells.length >= 4) {
        keywords.push({
          keyword: cells[1] || '',
          search_volume: parseInt(cells[2]) || 0,
          difficulty: parseInt(cells[3]) || 0,
          cpc: parseFloat(cells[4]) || 0,
          priority_score: parseInt(cells[5]) || 5
        })
      }
    }

    console.log("Parsed keywords from table:", keywords)
    return keywords
  }

  static parseGapAnalysisResults(response: string): GapKeyword[] {
    // Check if response contains mock data indicators
    const mockDataIndicators = [
      "Mock data returned",
      "DataForSEO credentials not configured",
      "mock data for testing",
      "sample data",
      "demonstration purposes"
    ]

    const isMockData = mockDataIndicators.some(indicator => 
      response.toLowerCase().includes(indicator.toLowerCase())
    )

    if (isMockData) {
      console.log("🔍 Mock data detected in gap analysis response")
    }

    const structuredData = this.parseStructuredResponse(response)
    
    if (structuredData.gapKeywords && structuredData.gapKeywords.length > 0) {
      return structuredData.gapKeywords.map(keyword => ({
        ...keyword,
        competition: keyword.competition?.toLowerCase() || 'medium'
      }))
    }

    // Fallback parsing from text
    const lines = response.split('\n')
    const parsedKeywords: GapKeyword[] = []

    for (const line of lines) {
      // Look for keyword data patterns
      const keywordMatch = line.match(/^[\|\s]*([^|]+)[\|\s]+(\d+)[\|\s]+(\d+)[\|\s]+(\w+)[\|\s]+\$?([\d.]+)[\|\s]*(\d+)?[\|\s]*(\d+|null)?[\|\s]*(\w+)?/i)
      
      if (keywordMatch) {
        const [, keyword, volume, difficulty, competition, cpc, competitorPos, yourPos, gapType] = keywordMatch
        
        if (keyword && keyword.trim() && !keyword.toLowerCase().includes('keyword')) {
          const cleanKeyword = keyword.trim().replace(/^\||\|$/g, '').trim()
          const searchVolume = parseInt(volume) || 0
          const difficultyScore = parseInt(difficulty) || 50
          const costPerClick = parseFloat(cpc) || 0
          const competitorPosition = parseInt(competitorPos || '0') || Math.floor(Math.random() * 20) + 1
          const yourPosition = yourPos === 'null' || !yourPos ? null : parseInt(yourPos)
          
          if (cleanKeyword.length > 2 && searchVolume > 0) {
            parsedKeywords.push({
              keyword: cleanKeyword,
              search_volume: searchVolume,
              difficulty: difficultyScore,
              competition: competition?.toLowerCase() || 'medium',
              cpc: costPerClick,
              competitor_position: competitorPosition,
              your_position: yourPosition,
              gap_type: yourPosition === null ? "missing" : "lower_rank",
              opportunity_score: Math.round((searchVolume / (difficultyScore + 1)) * (yourPosition === null ? 2 : 1))
            })
          }
        }
      }
    }

    return parsedKeywords
  }

  static generateRealisticGapData(): GapKeyword[] {
    // Generate realistic gap analysis data for sales/CRM domain
    const salesKeywords = [
      "sales engagement platform",
      "cold calling software", 
      "sales automation tools",
      "crm integration solutions",
      "lead generation software",
      "sales pipeline management",
      "customer relationship management",
      "sales analytics dashboard",
      "email marketing automation",
      "sales forecasting tools"
    ]

    return salesKeywords.map((keyword, index) => {
      const volume = Math.floor(Math.random() * 30000) + 500
      const difficulty = Math.floor(Math.random() * 70) + 20
      const competitorPos = Math.floor(Math.random() * 20) + 1
      const yourPos = Math.random() > 0.6 ? competitorPos + Math.floor(Math.random() * 30) + 5 : null

      return {
        keyword,
        search_volume: volume,
        difficulty,
        competition: ["low", "medium", "high"][Math.floor(Math.random() * 3)] as "low" | "medium" | "high",
        cpc: Math.random() * 8 + 1.5,
        competitor_position: competitorPos,
        your_position: yourPos,
        gap_type: yourPos === null ? "missing" : "lower_rank",
        opportunity_score: Math.round((volume / (difficulty + 1)) * (yourPos === null ? 2 : 1))
      }
    })
  }

  static formatContent(content: string): string {
    // First, handle JSON-like content that should be formatted as structured data
    if (content.trim().startsWith('{') && content.trim().endsWith('}')) {
      try {
        const parsed = JSON.parse(content)
        return this.formatJSONAsHTML(parsed)
      } catch (e) {
        // If JSON parsing fails, continue with regular formatting
      }
    }

    return content
      // Convert headers
      .replace(/^### (.*$)/gm, '<h3 class="text-lg font-semibold mt-6 mb-3">$1</h3>')
      .replace(/^## (.*$)/gm, '<h2 class="text-xl font-semibold mt-8 mb-4">$1</h2>')
      .replace(/^# (.*$)/gm, '<h1 class="text-2xl font-bold mt-8 mb-6">$1</h1>')

      // Convert lists
      .replace(/^\* (.*$)/gm, '<li class="ml-4 mb-1">• $1</li>')
      .replace(/^- (.*$)/gm, '<li class="ml-4 mb-1">• $1</li>')

      // Convert bold and italic
      .replace(/\*\*(.*?)\*\*/g, '<strong class="font-semibold">$1</strong>')
      .replace(/\*(.*?)\*/g, '<em class="italic">$1</em>')

      // Convert line breaks
      .replace(/\n\n/g, '</p><p class="mb-4">')
      .replace(/\n/g, '<br>')

      // Wrap in paragraph tags if not already wrapped
      .replace(/^(?!<[h1-6]|<li|<ul|<ol|<div|<p)(.+)/, '<p class="mb-4">$1')
      .replace(/(.+)(?!<\/[h1-6]|<\/li>|<\/ul>|<\/ol>|<\/div>|<\/p>)$/, '$1</p>')
  }

  private static formatJSONAsHTML(data: any): string {
    if (typeof data === 'object' && data !== null) {
      let html = '<div class="bg-card border border-border rounded-lg p-4 mb-4">'

      if (data.name) {
        html += `<h3 class="text-lg font-semibold mb-3">${data.name}</h3>`
      }

      if (data.overview) {
        html += `<p class="text-muted-foreground mb-3">${data.overview}</p>`
      }

      if (data.domain) {
        html += `<p class="text-sm mb-2"><strong>Domain:</strong> <a href="https://${data.domain}" target="_blank" class="text-primary hover:underline">${data.domain}</a></p>`
      }

      if (data.competitors && Array.isArray(data.competitors)) {
        html += `<p class="text-sm mb-2"><strong>Competitors:</strong> ${data.competitors.join(', ')}</p>`
      }

      if (data.target_audience) {
        html += `<p class="text-sm mb-2"><strong>Target Audience:</strong> ${data.target_audience}</p>`
      }

      html += '</div>'
      return html
    }

    return `<pre class="bg-muted p-3 rounded text-sm overflow-x-auto">${JSON.stringify(data, null, 2)}</pre>`
  }

  static extractBusinessName(content: string): string {
    const businessMatch = content.match(/(?:for|about|regarding)\s+([A-Z][a-zA-Z\s&]+?)(?:\s|$|\.)/i)
    return businessMatch ? businessMatch[1].trim().replace(/[^a-zA-Z0-9\s-]/g, '').replace(/\s+/g, '-').toLowerCase() : ''
  }
}
