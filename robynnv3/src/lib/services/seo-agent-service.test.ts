import { describe, it, expect, beforeEach } from 'vitest'
import { SEOAgentService } from './seo-agent-service'
import type { CompanyProfile, ContentStrategy } from './seo-agent-service'

describe('SEO Agent Service - Enhanced Workflow', () => {
  let service: SEOAgentService

  beforeEach(() => {
    service = new SEOAgentService('test-env')
  })

  describe('parseStructuredResponse', () => {
    it('should parse company profile from COMPANY_PROFILE tags', () => {
      const mockResponse = `
        <COMPANY_PROFILE>
        {
          "name": "TechStartup Inc.",
          "domain": "techstartup.com",
          "overview": "AI-powered business automation tools",
          "products_services": ["AI automation", "Business tools"],
          "competitors": ["Zapier", "Monday.com"],
          "target_audience": "Small businesses",
          "business_model": "B2B SaaS",
          "market_position": "Emerging player in automation space"
        }
        </COMPANY_PROFILE>
      `

      const result = SEOAgentService.parseStructuredResponse(mockResponse)
      
      expect(result.companyProfile).toBeDefined()
      expect(result.companyProfile?.name).toBe('TechStartup Inc.')
      expect(result.companyProfile?.domain).toBe('techstartup.com')
      expect(result.companyProfile?.products_services).toEqual(['AI automation', 'Business tools'])
      expect(result.companyProfile?.competitors).toEqual(['Zapier', 'Monday.com'])
    })

    it('should parse content strategy from CONTENT_STRATEGY tags', () => {
      const mockResponse = `
        <CONTENT_STRATEGY>
        {
          "recommendations": [
            {
              "title": "Ultimate Guide to AI Business Automation",
              "target_keywords": ["ai automation", "business automation", "workflow automation"],
              "content_angle": "Comprehensive guide for beginners",
              "expected_impact": "500+ monthly organic visits",
              "priority_score": 9
            },
            {
              "title": "Small Business Automation Tools Comparison",
              "target_keywords": ["automation tools", "small business software"],
              "content_angle": "Detailed comparison with competitors",
              "expected_impact": "300+ monthly organic visits",
              "priority_score": 7
            }
          ],
          "overall_strategy": "Focus on educational content that positions the company as automation experts"
        }
        </CONTENT_STRATEGY>
      `

      const result = SEOAgentService.parseStructuredResponse(mockResponse)
      
      expect(result.contentStrategy).toBeDefined()
      expect(result.contentStrategy?.recommendations).toHaveLength(2)
      expect(result.contentStrategy?.recommendations[0].title).toBe('Ultimate Guide to AI Business Automation')
      expect(result.contentStrategy?.recommendations[0].priority_score).toBe(9)
      expect(result.contentStrategy?.overall_strategy).toContain('educational content')
    })

    it('should handle malformed JSON gracefully', () => {
      const mockResponse = `
        <COMPANY_PROFILE>
        { invalid json }
        </COMPANY_PROFILE>
        <CONTENT_STRATEGY>
        { also invalid }
        </CONTENT_STRATEGY>
      `

      const result = SEOAgentService.parseStructuredResponse(mockResponse)

      expect(result.companyProfile).toBeNull()
      // Content strategy should now have fallback data instead of being null
      expect(result.contentStrategy).toBeDefined()
      expect(result.contentStrategy?.recommendations).toBeDefined()
      expect(result.contentStrategy?.recommendations.length).toBeGreaterThan(0)
    })

    it('should parse all sections together', () => {
      const mockResponse = `
        <COMPANY_PROFILE>
        {
          "name": "Test Company",
          "domain": "test.com",
          "overview": "Test overview",
          "products_services": ["Product 1"],
          "competitors": ["Competitor 1"],
          "target_audience": "Test audience",
          "business_model": "B2B",
          "market_position": "Test position"
        }
        </COMPANY_PROFILE>

        <KEYWORDS>
        [{"keyword": "test keyword", "volume": 1000, "difficulty": 30}]
        </KEYWORDS>

        <CONTENT_STRATEGY>
        {
          "recommendations": [
            {
              "title": "Test Article",
              "target_keywords": ["test keyword"],
              "content_angle": "Test angle",
              "expected_impact": "Test impact",
              "priority_score": 8
            }
          ],
          "overall_strategy": "Test strategy"
        }
        </CONTENT_STRATEGY>

        <RECOMMENDATIONS>
        Test recommendations content
        </RECOMMENDATIONS>
      `

      const result = SEOAgentService.parseStructuredResponse(mockResponse)
      
      expect(result.companyProfile).toBeDefined()
      expect(result.contentStrategy).toBeDefined()
      expect(result.keywords).toHaveLength(1)
      expect(result.recommendations).toBe('Test recommendations content')
    })
  })

  describe('Enhanced Workflow Integration', () => {
    it('should handle company intelligence phase data', () => {
      const companyProfile: CompanyProfile = {
        name: 'AI Solutions Inc.',
        domain: 'aisolutions.com',
        overview: 'Leading AI consulting firm',
        products_services: ['AI consulting', 'Machine learning'],
        competitors: ['IBM Watson', 'Google AI'],
        target_audience: 'Enterprise clients',
        business_model: 'B2B consulting',
        market_position: 'Premium AI consulting provider'
      }

      expect(companyProfile.name).toBe('AI Solutions Inc.')
      expect(companyProfile.products_services).toContain('AI consulting')
      expect(companyProfile.competitors).toContain('IBM Watson')
    })

    it('should handle content strategy phase data', () => {
      const contentStrategy: ContentStrategy = {
        recommendations: [
          {
            title: 'AI Implementation Best Practices',
            target_keywords: ['ai implementation', 'machine learning deployment'],
            content_angle: 'Technical guide for CTOs',
            expected_impact: '1000+ monthly visits',
            priority_score: 10
          }
        ],
        overall_strategy: 'Position as thought leaders in AI implementation'
      }

      expect(contentStrategy.recommendations).toHaveLength(1)
      expect(contentStrategy.recommendations[0].priority_score).toBe(10)
      expect(contentStrategy.overall_strategy).toContain('thought leaders')
    })
  })

  describe('Backward Compatibility', () => {
    it('should still parse existing keyword data', () => {
      const mockResponse = `
        <KEYWORDS>
        [{"keyword": "seo tools", "volume": 5000, "difficulty": 45}]
        </KEYWORDS>
        
        <GAP_RESULTS>
        [{"keyword": "competitor keyword", "search_volume": 2000, "difficulty": 30, "gap_type": "missing", "opportunity_score": 66.7}]
        </GAP_RESULTS>
      `

      const result = SEOAgentService.parseStructuredResponse(mockResponse)
      
      expect(result.keywords).toHaveLength(1)
      expect(result.gapKeywords).toHaveLength(1)
      expect(result.keywords[0].keyword).toBe('seo tools')
      expect(result.gapKeywords[0].keyword).toBe('competitor keyword')
    })
  })

  describe('formatContent', () => {
    test('should format Markdown headers correctly', () => {
      const input = '# Main Title\n## Section Title\n### Subsection'
      const result = SEOAgentService.formatContent(input)

      expect(result).toContain('<h1 class="text-2xl font-bold mt-8 mb-6">Main Title</h1>')
      expect(result).toContain('<h2 class="text-xl font-semibold mt-8 mb-4">Section Title</h2>')
      expect(result).toContain('<h3 class="text-lg font-semibold mt-6 mb-3">Subsection</h3>')
    })

    test('should format lists correctly', () => {
      const input = '* First item\n- Second item\n* Third item'
      const result = SEOAgentService.formatContent(input)

      expect(result).toContain('<li class="ml-4 mb-1">• First item</li>')
      expect(result).toContain('<li class="ml-4 mb-1">• Second item</li>')
      expect(result).toContain('<li class="ml-4 mb-1">• Third item</li>')
    })

    test('should format bold and italic text', () => {
      const input = '**Bold text** and *italic text*'
      const result = SEOAgentService.formatContent(input)

      expect(result).toContain('<strong class="font-semibold">Bold text</strong>')
      expect(result).toContain('<em class="italic">italic text</em>')
    })

    test('should format JSON company profile correctly', () => {
      const jsonInput = JSON.stringify({
        name: 'Test Company',
        domain: 'test.com',
        overview: 'A test company overview',
        competitors: ['competitor1.com', 'competitor2.com'],
        target_audience: 'Developers'
      })

      const result = SEOAgentService.formatContent(jsonInput)

      expect(result).toContain('<h3 class="text-lg font-semibold mb-3">Test Company</h3>')
      expect(result).toContain('<p class="text-muted-foreground mb-3">A test company overview</p>')
      expect(result).toContain('<strong>Domain:</strong>')
      expect(result).toContain('<strong>Competitors:</strong> competitor1.com, competitor2.com')
      expect(result).toContain('<strong>Target Audience:</strong> Developers')
    })

    test('should handle mixed content with headers and lists', () => {
      const input = '# SEO Analysis\n\n## Keywords\n* keyword1\n* keyword2\n\n**Important:** This is bold text.'
      const result = SEOAgentService.formatContent(input)

      expect(result).toContain('<h1 class="text-2xl font-bold mt-8 mb-6">SEO Analysis</h1>')
      expect(result).toContain('<h2 class="text-xl font-semibold mt-8 mb-4">Keywords</h2>')
      expect(result).toContain('<li class="ml-4 mb-1">• keyword1</li>')
      expect(result).toContain('<strong class="font-semibold">Important:</strong>')
    })
  })
})
