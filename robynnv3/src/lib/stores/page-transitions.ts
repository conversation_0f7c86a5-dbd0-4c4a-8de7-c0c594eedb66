import { writable } from "svelte/store"
import { browser } from "$app/environment"
import type { TransitionType } from "$lib/animations/page-transitions"

// Page transition state
interface PageTransitionState {
  isTransitioning: boolean
  transitionType: TransitionType
  direction?: "left" | "right" | "up" | "down"
  fromRoute?: string
  toRoute?: string
}

// Create the store
function createPageTransitionStore() {
  const { subscribe, set, update } = writable<PageTransitionState>({
    isTransitioning: false,
    transitionType: "fade",
    direction: undefined,
    fromRoute: undefined,
    toRoute: undefined,
  })

  return {
    subscribe,
    
    // Start a page transition
    startTransition: (
      type: TransitionType = "fade",
      direction?: "left" | "right" | "up" | "down",
      fromRoute?: string,
      toRoute?: string
    ) => {
      update(state => ({
        ...state,
        isTransitioning: true,
        transitionType: type,
        direction,
        fromRoute,
        toRoute,
      }))
    },
    
    // End the transition
    endTransition: () => {
      update(state => ({
        ...state,
        isTransitioning: false,
      }))
    },
    
    // Set transition type
    setTransitionType: (type: TransitionType) => {
      update(state => ({
        ...state,
        transitionType: type,
      }))
    },
    
    // Reset to default state
    reset: () => {
      set({
        isTransitioning: false,
        transitionType: "fade",
        direction: undefined,
        fromRoute: undefined,
        toRoute: undefined,
      })
    },
  }
}

export const pageTransitionStore = createPageTransitionStore()

// Navigation transition helper
export const navigateWithTransition = async (
  url: string,
  transitionType: TransitionType = "fade",
  direction?: "left" | "right" | "up" | "down"
) => {
  if (!browser) return

  const { goto } = await import("$app/navigation")
  const currentPath = window.location.pathname
  
  // Start transition
  pageTransitionStore.startTransition(transitionType, direction, currentPath, url)
  
  // Navigate
  await goto(url)
  
  // End transition after a short delay
  setTimeout(() => {
    pageTransitionStore.endTransition()
  }, 100)
}

// Route-based transition configuration
export const routeTransitions: Record<string, TransitionType> = {
  "/": "fade",
  "/pricing": "slide",
  "/contact": "scale",
  "/blog": "fade",
  "/login": "blur",
  "/dashboard": "slide",
}

// Get transition type for route
export const getTransitionForRoute = (route: string): TransitionType => {
  return routeTransitions[route] || "fade"
}

// Direction helper based on route hierarchy
export const getDirectionForRoutes = (
  fromRoute: string,
  toRoute: string
): "left" | "right" | "up" | "down" => {
  const routeHierarchy = [
    "/",
    "/pricing", 
    "/contact",
    "/blog",
    "/login",
    "/dashboard"
  ]
  
  const fromIndex = routeHierarchy.indexOf(fromRoute)
  const toIndex = routeHierarchy.indexOf(toRoute)
  
  if (fromIndex === -1 || toIndex === -1) return "right"
  
  return fromIndex < toIndex ? "right" : "left"
}

// Auto-configure transitions based on navigation
export const autoConfigureTransition = (fromRoute: string, toRoute: string) => {
  const transitionType = getTransitionForRoute(toRoute)
  const direction = transitionType === "slide" ? getDirectionForRoutes(fromRoute, toRoute) : undefined
  
  return { transitionType, direction }
}
