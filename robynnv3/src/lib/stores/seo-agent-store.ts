import { writable, derived } from 'svelte/store'

// Types
export interface SEOMessage {
  id: string
  role: 'user' | 'assistant'
  content: string
  timestamp: Date
  isReport?: boolean
  data?: any
}

export interface ProgressStep {
  id: number
  title: string
  description: string
  status: 'pending' | 'active' | 'completed'
  progress?: number
}

export interface NicheKeyword {
  keyword: string
  search_volume: number
  difficulty: number
  competition: string
  cpc: number
  opportunity_score?: number
}

export interface GapKeyword {
  keyword: string
  search_volume: number
  difficulty: number
  competition: string
  cpc: number
  competitor_position: number
  your_position: number | null
  gap_type: "missing" | "lower_rank" | "opportunity"
  opportunity_score?: number
}

export type SEOMode = 'chat' | 'niche' | 'gap'

// Core stores
export const messages = writable<SEOMessage[]>([])
export const isLoading = writable<boolean>(false)
export const mode = writable<SEOMode>('chat')
export const outputFormat = writable<string>('summary')

// Progress tracking
export const progressSteps = writable<ProgressStep[]>([])
export const currentProgress = writable<number>(0)

// Niche discovery
export const nicheKeywords = writable<NicheKeyword[]>([])
export const nicheProgressSteps = writable<ProgressStep[]>([])
export const nicheCurrentProgress = writable<number>(0)

// Gap analysis
export const gapKeywords = writable<GapKeyword[]>([])
export const gapProgressSteps = writable<ProgressStep[]>([])
export const gapCurrentProgress = writable<number>(0)
export const isUsingMockData = writable<boolean>(false)
export const gapAnalysisResponse = writable<string>('')

// UI state
export const selectedMessageId = writable<string>('')
export const showFilters = writable<boolean>(false)
export const targetAudience = writable<string>('')
export const regionFocus = writable<string>('')
export const funnelStage = writable<string>('awareness')

// Sidebar state
export const leftSidebarCollapsed = writable<boolean>(false)
export const rightSidebarCollapsed = writable<boolean>(false)

// Derived stores
export const showExamples = derived(
  messages,
  ($messages) => $messages.length === 0
)

export const hasResults = derived(
  [nicheKeywords, gapKeywords],
  ([$nicheKeywords, $gapKeywords]) => 
    $nicheKeywords.length > 0 || $gapKeywords.length > 0
)

// Utility functions
export function generateId(): string {
  if (typeof crypto !== 'undefined' && crypto.randomUUID) {
    return crypto.randomUUID()
  }
  return Math.random().toString(36).substring(2, 11)
}

export function addMessage(message: Omit<SEOMessage, 'id' | 'timestamp'>) {
  messages.update(msgs => [
    ...msgs,
    {
      ...message,
      id: generateId(),
      timestamp: new Date()
    }
  ])
}

export function clearMessages() {
  messages.set([])
}

export function resetProgress() {
  progressSteps.set([])
  currentProgress.set(0)
  nicheProgressSteps.set([])
  nicheCurrentProgress.set(0)
  gapProgressSteps.set([])
  gapCurrentProgress.set(0)
}

export function updateProgressStep(stepId: number, status: ProgressStep['status'], description?: string) {
  progressSteps.update(steps => 
    steps.map(step => {
      if (step.id === stepId) {
        return { ...step, status, description: description || step.description }
      } else if (step.id < stepId) {
        return { ...step, status: 'completed' }
      }
      return step
    })
  )
}

export function updateGapProgressStep(stepId: number, status: ProgressStep['status'], description?: string) {
  gapProgressSteps.update(steps =>
    steps.map(step => {
      if (step.id === stepId) {
        return { ...step, status, description: description || step.description }
      } else if (step.id < stepId) {
        return { ...step, status: 'completed' }
      }
      return step
    })
  )
}

export function handleRequestError(error: any, errorType: 'timeout' | 'network' | 'server' | 'unknown' = 'unknown') {
  console.error("SEO Agent error:", error)

  // Mark current active step as failed and reset others to pending
  progressSteps.update(steps =>
    steps.map(step => {
      if (step.status === "active") {
        return { ...step, status: "pending", description: "Failed - ready to retry" }
      }
      return { ...step, status: step.status === "completed" ? "completed" : "pending" }
    })
  )

  // Determine error message based on type
  let errorMessage = "Sorry, there was an error processing your request. Please try again."
  if (errorType === 'timeout') {
    errorMessage = "Request timed out. The SEO analysis is taking longer than expected. Please try again with a more specific query."
  } else if (errorType === 'network') {
    errorMessage = "Network connection issue. Please check your connection and try again."
  } else if (errorType === 'server') {
    errorMessage = "Server error occurred during processing. Please try again in a moment."
  }

  addMessage({
    role: "assistant",
    content: errorMessage,
    isReport: true,
  })
}

export function initializeChatProgressSteps() {
  progressSteps.set([
    {
      id: 1,
      title: "Industry Research",
      description: "Analyzing your business niche...",
      status: "pending",
    },
    {
      id: 2,
      title: "Keyword Discovery",
      description: "Finding relevant keywords...",
      status: "pending",
    },
    {
      id: 3,
      title: "Volume Analysis",
      description: "Checking search volumes...",
      status: "pending",
    },
    {
      id: 4,
      title: "Competition Analysis",
      description: "Analyzing keyword difficulty...",
      status: "pending",
    },
    {
      id: 5,
      title: "Report Generation",
      description: "Creating your SEO strategy...",
      status: "pending",
    },
  ])
  currentProgress.set(0)
}

export function initializeGapProgressSteps() {
  gapProgressSteps.set([
    {
      id: 1,
      title: "Domain Analysis",
      description: "Analyzing your domain authority...",
      status: "pending",
    },
    {
      id: 2,
      title: "Competitor Research",
      description: "Researching competitor keywords...",
      status: "pending",
    },
    {
      id: 3,
      title: "Keyword Extraction",
      description: "Extracting competitor keywords...",
      status: "pending",
    },
    {
      id: 4,
      title: "Gap Identification",
      description: "Identifying keyword gaps...",
      status: "pending",
    },
    {
      id: 5,
      title: "Calculating Gaps",
      description: "Identifying keyword opportunities...",
      status: "pending",
    },
    {
      id: 6,
      title: "Generating Report",
      description: "Formatting your gap analysis results...",
      status: "pending",
    },
  ])
  gapCurrentProgress.set(0)
}

// Reset all stores to initial state
export function resetAllStores() {
  messages.set([])
  isLoading.set(false)
  mode.set('chat')
  outputFormat.set('summary')
  resetProgress()
  nicheKeywords.set([])
  gapKeywords.set([])
  isUsingMockData.set(false)
  gapAnalysisResponse.set('')
  selectedMessageId.set('')
  showFilters.set(false)
  targetAudience.set('')
  regionFocus.set('')
  funnelStage.set('awareness')
}
