import { z } from 'zod'

// Base publishing configuration schema
export const publishingConfigSchema = z.object({
  destination: z.enum(['slack', 'webhook', 'hubspot', 'salesforce']).describe('Publishing destination type'),
  enabled: z.boolean().default(true).describe('Whether this publishing destination is enabled'),
  retryAttempts: z.number().min(0).max(5).default(3).describe('Number of retry attempts on failure'),
  timeout: z.number().min(1000).max(30000).default(10000).describe('Request timeout in milliseconds')
})

// Slack-specific configuration schema
export const slackWebhookConfigSchema = publishingConfigSchema.extend({
  destination: z.literal('slack'),
  webhookUrl: z.string().url().describe('Slack webhook URL for message delivery'),
  channel: z.string().optional().describe('Override channel (if webhook supports it)'),
  username: z.string().optional().describe('Bot username for messages'),
  iconEmoji: z.string().optional().describe('Bot icon emoji (e.g., ":robot_face:")'),
  threadTs: z.string().optional().describe('Thread timestamp for threaded replies')
})

// Slack message formatting options
export const slackFormattingOptionsSchema = z.object({
  includeMetadata: z.boolean().default(true).describe('Include research metadata in message'),
  includeContacts: z.boolean().default(true).describe('Include contact information'),
  includeCompetitors: z.boolean().default(true).describe('Include competitor analysis'),
  includeInsights: z.boolean().default(true).describe('Include actionable insights'),
  messageStyle: z.enum(['detailed', 'summary', 'executive']).default('detailed').describe('Message detail level'),
  useThreads: z.boolean().default(false).describe('Split content into threaded messages'),
  mentionUsers: z.array(z.string()).optional().describe('Slack user IDs to mention in message')
})

// Main Slack publishing tool schema
export const slackPublishingSchema = z.object({
  researchData: z.record(z.any()).describe('Orchestrator agent JSON response data'),
  config: slackWebhookConfigSchema.describe('Slack webhook configuration'),
  formatting: slackFormattingOptionsSchema.optional().describe('Message formatting preferences'),
  preview: z.boolean().default(false).describe('Return formatted message without sending')
})

// Generic webhook publishing schema
export const webhookPublishingSchema = z.object({
  webhookUrl: z.string().url().describe('Target webhook URL'),
  method: z.enum(['POST', 'PUT', 'PATCH']).default('POST').describe('HTTP method'),
  headers: z.record(z.string()).optional().describe('Custom headers for webhook request'),
  payload: z.record(z.any()).describe('Data payload to send'),
  authentication: z.object({
    type: z.enum(['none', 'bearer', 'basic', 'api_key']).default('none'),
    token: z.string().optional(),
    username: z.string().optional(),
    password: z.string().optional(),
    apiKeyHeader: z.string().optional()
  }).optional().describe('Authentication configuration')
})

// Publishing result schema
export const publishingResultSchema = z.object({
  success: z.boolean().describe('Whether publishing was successful'),
  destination: z.string().describe('Publishing destination identifier'),
  messageId: z.string().optional().describe('Message ID from destination (if available)'),
  timestamp: z.string().describe('ISO timestamp of publishing attempt'),
  deliveryTime: z.number().describe('Delivery time in milliseconds'),
  error: z.string().optional().describe('Error message if publishing failed'),
  retryCount: z.number().default(0).describe('Number of retry attempts made'),
  previewData: z.record(z.any()).optional().describe('Formatted message data for preview')
})

// Batch publishing schema for multiple destinations
export const batchPublishingSchema = z.object({
  researchData: z.record(z.any()).describe('Orchestrator agent JSON response data'),
  destinations: z.array(z.object({
    id: z.string().describe('Unique destination identifier'),
    type: z.enum(['slack', 'webhook', 'hubspot', 'salesforce']),
    config: z.record(z.any()).describe('Destination-specific configuration'),
    formatting: z.record(z.any()).optional().describe('Destination-specific formatting options')
  })).min(1).describe('List of publishing destinations'),
  parallel: z.boolean().default(true).describe('Whether to publish to destinations in parallel')
})

// Publishing analytics schema
export const publishingAnalyticsSchema = z.object({
  totalPublications: z.number().describe('Total number of publications attempted'),
  successfulPublications: z.number().describe('Number of successful publications'),
  failedPublications: z.number().describe('Number of failed publications'),
  averageDeliveryTime: z.number().describe('Average delivery time in milliseconds'),
  destinationBreakdown: z.record(z.object({
    attempts: z.number(),
    successes: z.number(),
    failures: z.number(),
    averageTime: z.number()
  })).describe('Per-destination analytics'),
  lastPublished: z.string().optional().describe('ISO timestamp of last publication'),
  errorSummary: z.array(z.object({
    error: z.string(),
    count: z.number(),
    lastOccurred: z.string()
  })).describe('Summary of common errors')
})
