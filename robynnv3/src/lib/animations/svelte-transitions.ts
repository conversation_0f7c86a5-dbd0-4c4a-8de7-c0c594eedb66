import { cubicOut, cubicIn, cubicInOut } from "svelte/easing"
import type { TransitionConfig } from "svelte/transition"
import { prefersReducedMotion } from "./index"
import { browser } from "$app/environment"

// Enhanced fade transition with better easing
export function enhancedFade(
  node: Element,
  {
    delay = 0,
    duration = 300,
    easing = cubicInOut,
  }: {
    delay?: number
    duration?: number
    easing?: (t: number) => number
  } = {}
): TransitionConfig {
  if (!browser || prefersReducedMotion()) {
    return { duration: 0 }
  }

  return {
    delay,
    duration,
    easing,
    css: (t) => `opacity: ${t}`,
  }
}

// Enhanced slide transition with multiple directions
export function enhancedSlide(
  node: Element,
  {
    delay = 0,
    duration = 300,
    easing = cubicOut,
    direction = "left",
    distance = 100,
  }: {
    delay?: number
    duration?: number
    easing?: (t: number) => number
    direction?: "left" | "right" | "up" | "down"
    distance?: number
  } = {}
): TransitionConfig {
  if (!browser || prefersReducedMotion()) {
    return { duration: 0 }
  }

  const transforms = {
    left: `translateX(-${distance}px)`,
    right: `translateX(${distance}px)`,
    up: `translateY(-${distance}px)`,
    down: `translateY(${distance}px)`,
  }

  return {
    delay,
    duration,
    easing,
    css: (t) => `
      transform: ${t === 1 ? "none" : transforms[direction]};
      opacity: ${t};
    `,
  }
}

// Enhanced scale transition
export function enhancedScale(
  node: Element,
  {
    delay = 0,
    duration = 300,
    easing = cubicOut,
    start = 0.8,
    opacity = 0,
  }: {
    delay?: number
    duration?: number
    easing?: (t: number) => number
    start?: number
    opacity?: number
  } = {}
): TransitionConfig {
  if (!browser || prefersReducedMotion()) {
    return { duration: 0 }
  }

  return {
    delay,
    duration,
    easing,
    css: (t) => `
      transform: scale(${start + (1 - start) * t});
      opacity: ${opacity + (1 - opacity) * t};
    `,
  }
}

// Blur transition
export function blurTransition(
  node: Element,
  {
    delay = 0,
    duration = 300,
    easing = cubicInOut,
    amount = 10,
  }: {
    delay?: number
    duration?: number
    easing?: (t: number) => number
    amount?: number
  } = {}
): TransitionConfig {
  if (!browser || prefersReducedMotion()) {
    return { duration: 0 }
  }

  return {
    delay,
    duration,
    easing,
    css: (t) => `
      filter: blur(${(1 - t) * amount}px);
      opacity: ${t};
    `,
  }
}

// Flip transition (great for cards)
export function flipTransition(
  node: Element,
  {
    delay = 0,
    duration = 400,
    easing = cubicOut,
    axis = "y",
  }: {
    delay?: number
    duration?: number
    easing?: (t: number) => number
    axis?: "x" | "y"
  } = {}
): TransitionConfig {
  if (!browser || prefersReducedMotion()) {
    return { duration: 0 }
  }

  return {
    delay,
    duration,
    easing,
    css: (t) => `
      transform: rotate${axis.toUpperCase()}(${(1 - t) * 180}deg);
      opacity: ${t};
    `,
  }
}

// Bounce transition
export function bounceTransition(
  node: Element,
  {
    delay = 0,
    duration = 500,
    bounces = 3,
  }: {
    delay?: number
    duration?: number
    bounces?: number
  } = {}
): TransitionConfig {
  if (!browser || prefersReducedMotion()) {
    return { duration: 0 }
  }

  return {
    delay,
    duration,
    css: (t) => {
      const bounce = Math.sin(t * Math.PI * bounces) * (1 - t)
      return `
        transform: translateY(${bounce * -20}px) scale(${0.8 + t * 0.2});
        opacity: ${t};
      `
    },
  }
}

// Typewriter transition
export function typewriterTransition(
  node: Element,
  {
    delay = 0,
    duration = 1000,
    speed = 50,
  }: {
    delay?: number
    duration?: number
    speed?: number
  } = {}
): TransitionConfig {
  if (!browser || prefersReducedMotion()) {
    return { duration: 0 }
  }

  const text = node.textContent || ""
  const totalLength = text.length

  return {
    delay,
    duration: Math.max(duration, totalLength * speed),
    tick: (t) => {
      const i = Math.trunc(totalLength * t)
      node.textContent = text.slice(0, i)
    },
  }
}

// Stagger transition for lists
export function staggerTransition(
  node: Element,
  {
    delay = 0,
    duration = 300,
    easing = cubicOut,
    stagger = 50,
    childSelector = "> *",
  }: {
    delay?: number
    duration?: number
    easing?: (t: number) => number
    stagger?: number
    childSelector?: string
  } = {}
): TransitionConfig {
  if (!browser || prefersReducedMotion()) {
    return { duration: 0 }
  }

  const children = node.querySelectorAll(childSelector)
  const totalDuration = duration + (children.length - 1) * stagger

  return {
    delay,
    duration: totalDuration,
    css: (t) => `opacity: ${t}`,
    tick: (t) => {
      children.forEach((child, index) => {
        const childStart = (index * stagger) / totalDuration
        const childEnd = (index * stagger + duration) / totalDuration
        const childT = Math.max(0, Math.min(1, (t - childStart) / (childEnd - childStart)))
        
        if (child instanceof HTMLElement) {
          child.style.opacity = String(childT)
          child.style.transform = `translateY(${(1 - childT) * 20}px)`
        }
      })
    },
  }
}

// Draw transition (for SVG paths)
export function drawTransition(
  node: Element,
  {
    delay = 0,
    duration = 1000,
    easing = cubicOut,
  }: {
    delay?: number
    duration?: number
    easing?: (t: number) => number
  } = {}
): TransitionConfig {
  if (!browser || prefersReducedMotion()) {
    return { duration: 0 }
  }

  if (!(node instanceof SVGPathElement)) {
    console.warn("drawTransition can only be used on SVG path elements")
    return { duration: 0 }
  }

  const length = node.getTotalLength()

  return {
    delay,
    duration,
    easing,
    css: (t) => `
      stroke-dasharray: ${length};
      stroke-dashoffset: ${length * (1 - t)};
    `,
  }
}

// Reveal transition (clip-path based)
export function revealTransition(
  node: Element,
  {
    delay = 0,
    duration = 400,
    easing = cubicOut,
    direction = "left",
  }: {
    delay?: number
    duration?: number
    easing?: (t: number) => number
    direction?: "left" | "right" | "up" | "down"
  } = {}
): TransitionConfig {
  if (!browser || prefersReducedMotion()) {
    return { duration: 0 }
  }

  const clipPaths = {
    left: (t: number) => `inset(0 ${100 - t * 100}% 0 0)`,
    right: (t: number) => `inset(0 0 0 ${100 - t * 100}%)`,
    up: (t: number) => `inset(${100 - t * 100}% 0 0 0)`,
    down: (t: number) => `inset(0 0 ${100 - t * 100}% 0)`,
  }

  return {
    delay,
    duration,
    easing,
    css: (t) => `
      clip-path: ${clipPaths[direction](t)};
    `,
  }
}

// Morphing transition (for shape changes)
export function morphTransition(
  node: Element,
  {
    delay = 0,
    duration = 500,
    easing = cubicInOut,
    fromPath,
    toPath,
  }: {
    delay?: number
    duration?: number
    easing?: (t: number) => number
    fromPath: string
    toPath: string
  }
): TransitionConfig {
  if (!browser || prefersReducedMotion()) {
    return { duration: 0 }
  }

  if (!(node instanceof SVGPathElement)) {
    console.warn("morphTransition can only be used on SVG path elements")
    return { duration: 0 }
  }

  return {
    delay,
    duration,
    easing,
    tick: (t) => {
      // Simple interpolation - in a real app you'd want a proper path interpolation library
      const interpolatedPath = t < 0.5 ? fromPath : toPath
      node.setAttribute("d", interpolatedPath)
    },
  }
}

// Glitch transition (for dramatic effect)
export function glitchTransition(
  node: Element,
  {
    delay = 0,
    duration = 300,
    intensity = 5,
  }: {
    delay?: number
    duration?: number
    intensity?: number
  } = {}
): TransitionConfig {
  if (!browser || prefersReducedMotion()) {
    return { duration: 0 }
  }

  return {
    delay,
    duration,
    css: (t) => {
      const glitch = Math.random() * intensity * (1 - t)
      return `
        transform: translateX(${glitch}px) skew(${glitch * 0.5}deg);
        opacity: ${0.8 + t * 0.2};
        filter: hue-rotate(${glitch * 10}deg);
      `
    },
  }
}
