# Animation System Documentation

A comprehensive, performant, and accessible animation system built for Svelte/SvelteKit applications using Motion One.

## 🚀 Features

- **Hardware Accelerated**: Uses Motion One's optimized Web Animations API
- **Accessibility First**: Respects `prefers-reduced-motion` setting
- **Performance Optimized**: Adaptive animations based on device capabilities
- **Mobile Responsive**: Touch-friendly and mobile-optimized
- **Type Safe**: Full TypeScript support
- **Testing Suite**: Comprehensive testing and performance monitoring

## 📦 Installation

The animation system is already installed and configured. Dependencies:
- `@motionone/dom` - Core animation library
- `@motionone/svelte` - Svelte integration (legacy)

## 🎯 Quick Start

### Basic Animations

```svelte
<script>
  import { fadeIn, parallax, hoverScale } from '$lib/animations/actions'
</script>

<!-- Fade in when element enters viewport -->
<div use:fadeIn={{ delay: 0.2, duration: 0.8 }}>
  Content that fades in
</div>

<!-- Parallax scrolling effect -->
<div use:parallax={{ speed: 0.5, direction: "up" }}>
  Parallax background
</div>

<!-- Hover scale effect -->
<button use:hoverScale={{ scale: 1.05 }}>
  Hover me
</button>
```

### Page Transitions

```svelte
<script>
  import { enhancedFade, enhancedSlide } from '$lib/animations/svelte-transitions'
</script>

{#if showModal}
  <div in:enhancedScale={{ duration: 300 }} out:enhancedFade={{ duration: 200 }}>
    Modal content
  </div>
{/if}
```

### Micro-interactions

```svelte
<script>
  import { clickFeedback, focusAnimation } from '$lib/animations/actions'
  import { successNotification } from '$lib/animations/micro-interactions'
  
  function handleSubmit() {
    successNotification("Form submitted successfully!")
  }
</script>

<button use:clickFeedback on:click={handleSubmit}>
  Submit
</button>

<input use:focusAnimation placeholder="Focus me for animation" />
```

## 📚 API Reference

### Actions

#### `fadeIn(options)`
Fade in animation when element enters viewport.

**Options:**
- `delay?: number` - Animation delay (default: 0)
- `duration?: number` - Animation duration (default: 0.4)
- `distance?: number` - Distance to animate from (default: 20)
- `direction?: "up" | "down" | "left" | "right"` - Animation direction (default: "up")
- `threshold?: number` - Intersection threshold (default: 0.1)

#### `parallax(options)`
Parallax scrolling effect.

**Options:**
- `speed?: number` - Parallax speed multiplier (default: 0.5)
- `direction?: "up" | "down"` - Scroll direction (default: "up")
- `offset?: [string, string]` - Scroll offset range (default: ["start end", "end start"])

#### `hoverScale(options)`
Scale animation on hover.

**Options:**
- `scale?: number` - Scale factor (default: 1.05)
- `duration?: number` - Animation duration (default: 0.2)

#### `clickFeedback()`
Ripple effect on click with scale feedback.

#### `cardHover()`
Lift animation for cards on hover.

### Transitions

#### `enhancedFade(options)`
Enhanced fade transition with better easing.

#### `enhancedSlide(options)`
Multi-directional slide transition.

#### `enhancedScale(options)`
Scale transition with opacity.

#### `blurTransition(options)`
Blur effect transition.

### Micro-interactions

#### `successNotification(message, container?)`
Show success notification with slide-in animation.

#### `errorNotification(message, container?)`
Show error notification with shake animation.

#### `createLoadingSpinner(container, size?)`
Create animated loading spinner.

## 🎛️ Configuration

### Animation Config

```typescript
export const ANIMATION_CONFIG = {
  duration: {
    fast: 0.2,
    normal: 0.4,
    slow: 0.6,
    verySlow: 1.0,
  },
  easing: {
    ease: "ease",
    easeIn: "ease-in",
    easeOut: "ease-out",
    easeInOut: "ease-in-out",
    spring: [0.25, 0.46, 0.45, 0.94],
    bounce: [0.68, -0.55, 0.265, 1.55],
  },
  stagger: {
    fast: 0.05,
    normal: 0.1,
    slow: 0.2,
  },
}
```

### Device Adaptation

The system automatically adapts animations based on:
- Device performance tier (low/medium/high)
- Mobile vs desktop
- Hardware acceleration support
- User motion preferences

## 🔧 Performance

### Monitoring

```typescript
import { AnimationPerformanceMonitor } from '$lib/animations/performance'

const monitor = AnimationPerformanceMonitor.getInstance()
monitor.startMonitoring()

// Get performance report
const report = monitor.getPerformanceReport()
```

### Optimization Features

- **Adaptive Duration**: Animations speed up on low-performance devices
- **Queue Management**: Limits concurrent animations
- **Hardware Acceleration**: Uses GPU when available
- **Intersection Observer**: Efficient viewport detection
- **Memory Management**: Automatic cleanup

## 🧪 Testing

### Manual Testing

```javascript
// Run in browser console
testAnimations()
```

### Programmatic Testing

```typescript
import { AnimationTester } from '$lib/animations/testing'

const tester = new AnimationTester()
const results = await tester.runAllTests()
```

### Test Coverage

- ✅ Reduced motion support
- ✅ Animation performance under load
- ✅ Device capability detection
- ✅ Intersection Observer functionality
- ✅ CSS animation support
- ✅ Cross-browser compatibility

## ♿ Accessibility

### Reduced Motion Support

The system automatically respects the `prefers-reduced-motion` setting:

```css
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    transition-duration: 0.01ms !important;
  }
}
```

### Best Practices

- All animations have reduced motion fallbacks
- Focus states are clearly animated
- Loading states provide clear feedback
- Animations don't interfere with screen readers

## 📱 Mobile Optimization

- Touch-friendly hover alternatives
- Reduced animation complexity on mobile
- Battery-conscious performance scaling
- Responsive animation timing

## 🌐 Browser Support

- **Modern Browsers**: Full feature support
- **Legacy Browsers**: Graceful degradation
- **Web Animations API**: Polyfill included
- **Intersection Observer**: Polyfill available

## 🚨 Troubleshooting

### Common Issues

1. **Animations not working**: Check if `prefers-reduced-motion` is enabled
2. **Performance issues**: Monitor with performance tools
3. **Mobile lag**: Reduce concurrent animations
4. **Memory leaks**: Ensure proper cleanup

### Debug Mode

```typescript
// Enable in development
if (import.meta.env.DEV) {
  // Performance monitoring is automatically enabled
  // Check console for performance warnings
}
```

## 🔄 Migration Guide

### From CSS Animations

```css
/* Old CSS */
.fade-in {
  animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}
```

```svelte
<!-- New Svelte Action -->
<div use:fadeIn={{ duration: 0.5 }}>Content</div>
```

### From Svelte Transitions

```svelte
<!-- Old -->
<div in:fade={{ duration: 300 }}>Content</div>

<!-- New -->
<div in:enhancedFade={{ duration: 300 }}>Content</div>
```

## 📈 Performance Metrics

- **Bundle Size**: ~5kb additional (Motion One)
- **Runtime Overhead**: <1ms per animation
- **Memory Usage**: Automatic cleanup prevents leaks
- **FPS Impact**: <5% on modern devices

## 🤝 Contributing

When adding new animations:

1. Follow the existing patterns
2. Add TypeScript types
3. Include accessibility considerations
4. Add tests for new features
5. Update documentation

## 📄 License

Part of the main application license.
