import { animate } from "@motionone/dom"
import { browser } from "$app/environment"
import { prefersReducedMotion, ANIMATION_CONFIG } from "./index"

// Button click feedback animation
export const buttonClickFeedback = (element: HTMLElement) => {
  if (!browser || prefersReducedMotion()) return

  // Create ripple effect
  const rect = element.getBoundingClientRect()
  const ripple = document.createElement("div")
  
  ripple.style.position = "absolute"
  ripple.style.borderRadius = "50%"
  ripple.style.background = "rgba(255, 255, 255, 0.3)"
  ripple.style.pointerEvents = "none"
  ripple.style.width = "20px"
  ripple.style.height = "20px"
  ripple.style.left = "50%"
  ripple.style.top = "50%"
  ripple.style.transform = "translate(-50%, -50%)"
  ripple.style.zIndex = "1000"

  element.style.position = "relative"
  element.style.overflow = "hidden"
  element.appendChild(ripple)

  // Animate ripple
  animate(
    ripple,
    {
      transform: ["translate(-50%, -50%) scale(0)", "translate(-50%, -50%) scale(4)"],
      opacity: [0.6, 0],
    },
    {
      duration: 0.6,
      easing: ANIMATION_CONFIG.easing.easeOut,
    }
  ).finished.then(() => {
    ripple.remove()
  })

  // Button scale feedback
  animate(
    element,
    {
      transform: ["scale(1)", "scale(0.95)", "scale(1)"],
    },
    {
      duration: 0.2,
      easing: ANIMATION_CONFIG.easing.easeOut,
    }
  )
}

// Form input focus animation
export const inputFocusAnimation = (input: HTMLInputElement | HTMLTextAreaElement) => {
  if (!browser || prefersReducedMotion()) return

  const handleFocus = () => {
    animate(
      input,
      {
        transform: "scale(1.02)",
        boxShadow: "0 0 0 2px rgba(59, 130, 246, 0.3)",
      },
      {
        duration: ANIMATION_CONFIG.duration.fast,
        easing: ANIMATION_CONFIG.easing.easeOut,
      }
    )
  }

  const handleBlur = () => {
    animate(
      input,
      {
        transform: "scale(1)",
        boxShadow: "0 0 0 0px rgba(59, 130, 246, 0)",
      },
      {
        duration: ANIMATION_CONFIG.duration.fast,
        easing: ANIMATION_CONFIG.easing.easeOut,
      }
    )
  }

  input.addEventListener("focus", handleFocus)
  input.addEventListener("blur", handleBlur)

  return () => {
    input.removeEventListener("focus", handleFocus)
    input.removeEventListener("blur", handleBlur)
  }
}

// Success notification animation
export const successNotification = (message: string, container?: HTMLElement) => {
  if (!browser) return

  const notification = document.createElement("div")
  notification.innerHTML = `
    <div class="flex items-center gap-3 bg-green-500 text-white px-4 py-3 rounded-lg shadow-lg">
      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
      </svg>
      <span>${message}</span>
    </div>
  `
  
  notification.style.position = "fixed"
  notification.style.top = "20px"
  notification.style.right = "20px"
  notification.style.zIndex = "10000"
  notification.style.opacity = "0"
  notification.style.transform = "translateX(100%)"

  const target = container || document.body
  target.appendChild(notification)

  if (prefersReducedMotion()) {
    notification.style.opacity = "1"
    notification.style.transform = "translateX(0)"
    setTimeout(() => notification.remove(), 3000)
    return
  }

  // Slide in animation
  animate(
    notification,
    {
      opacity: [0, 1],
      transform: ["translateX(100%)", "translateX(0)"],
    },
    {
      duration: ANIMATION_CONFIG.duration.normal,
      easing: ANIMATION_CONFIG.easing.easeOut,
    }
  ).finished.then(() => {
    // Auto-hide after 3 seconds
    setTimeout(() => {
      animate(
        notification,
        {
          opacity: [1, 0],
          transform: ["translateX(0)", "translateX(100%)"],
        },
        {
          duration: ANIMATION_CONFIG.duration.normal,
          easing: ANIMATION_CONFIG.easing.easeIn,
        }
      ).finished.then(() => {
        notification.remove()
      })
    }, 3000)
  })
}

// Error notification animation
export const errorNotification = (message: string, container?: HTMLElement) => {
  if (!browser) return

  const notification = document.createElement("div")
  notification.innerHTML = `
    <div class="flex items-center gap-3 bg-red-500 text-white px-4 py-3 rounded-lg shadow-lg">
      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
      </svg>
      <span>${message}</span>
    </div>
  `
  
  notification.style.position = "fixed"
  notification.style.top = "20px"
  notification.style.right = "20px"
  notification.style.zIndex = "10000"
  notification.style.opacity = "0"
  notification.style.transform = "translateX(100%)"

  const target = container || document.body
  target.appendChild(notification)

  if (prefersReducedMotion()) {
    notification.style.opacity = "1"
    notification.style.transform = "translateX(0)"
    setTimeout(() => notification.remove(), 4000)
    return
  }

  // Shake and slide in animation
  animate(
    notification,
    {
      opacity: [0, 1],
      transform: ["translateX(100%)", "translateX(0)"],
    },
    {
      duration: ANIMATION_CONFIG.duration.normal,
      easing: ANIMATION_CONFIG.easing.easeOut,
    }
  ).finished.then(() => {
    // Shake animation
    animate(
      notification,
      {
        transform: ["translateX(0)", "translateX(-5px)", "translateX(5px)", "translateX(0)"],
      },
      {
        duration: 0.5,
        easing: ANIMATION_CONFIG.easing.easeInOut,
      }
    )

    // Auto-hide after 4 seconds
    setTimeout(() => {
      animate(
        notification,
        {
          opacity: [1, 0],
          transform: ["translateX(0)", "translateX(100%)"],
        },
        {
          duration: ANIMATION_CONFIG.duration.normal,
          easing: ANIMATION_CONFIG.easing.easeIn,
        }
      ).finished.then(() => {
        notification.remove()
      })
    }, 4000)
  })
}

// Loading spinner animation
export const createLoadingSpinner = (container: HTMLElement, size: "sm" | "md" | "lg" = "md") => {
  if (!browser) return

  const sizeClasses = {
    sm: "w-4 h-4",
    md: "w-8 h-8",
    lg: "w-12 h-12"
  }

  const spinner = document.createElement("div")
  spinner.innerHTML = `
    <div class="flex items-center justify-center">
      <div class="${sizeClasses[size]} border-2 border-gray-300 border-t-blue-500 rounded-full animate-spin"></div>
    </div>
  `
  
  spinner.style.position = "absolute"
  spinner.style.top = "50%"
  spinner.style.left = "50%"
  spinner.style.transform = "translate(-50%, -50%)"
  spinner.style.zIndex = "1000"

  container.style.position = "relative"
  container.appendChild(spinner)

  if (!prefersReducedMotion()) {
    animate(
      spinner,
      {
        opacity: [0, 1],
      },
      {
        duration: ANIMATION_CONFIG.duration.fast,
        easing: ANIMATION_CONFIG.easing.easeOut,
      }
    )
  }

  return {
    remove: () => {
      if (!prefersReducedMotion()) {
        animate(
          spinner,
          {
            opacity: [1, 0],
          },
          {
            duration: ANIMATION_CONFIG.duration.fast,
            easing: ANIMATION_CONFIG.easing.easeIn,
          }
        ).finished.then(() => {
          spinner.remove()
        })
      } else {
        spinner.remove()
      }
    }
  }
}

// Card hover animation
export const cardHoverAnimation = (card: HTMLElement) => {
  if (!browser || prefersReducedMotion()) return

  let hoverAnimation: any = null

  const handleMouseEnter = () => {
    if (hoverAnimation) hoverAnimation.stop()
    hoverAnimation = animate(
      card,
      {
        transform: "translateY(-4px)",
        boxShadow: "0 10px 25px rgba(0, 0, 0, 0.15)",
      },
      {
        duration: ANIMATION_CONFIG.duration.normal,
        easing: ANIMATION_CONFIG.easing.easeOut,
      }
    )
  }

  const handleMouseLeave = () => {
    if (hoverAnimation) hoverAnimation.stop()
    hoverAnimation = animate(
      card,
      {
        transform: "translateY(0px)",
        boxShadow: "0 2px 8px rgba(0, 0, 0, 0.1)",
      },
      {
        duration: ANIMATION_CONFIG.duration.normal,
        easing: ANIMATION_CONFIG.easing.easeOut,
      }
    )
  }

  card.addEventListener("mouseenter", handleMouseEnter)
  card.addEventListener("mouseleave", handleMouseLeave)

  return () => {
    card.removeEventListener("mouseenter", handleMouseEnter)
    card.removeEventListener("mouseleave", handleMouseLeave)
    if (hoverAnimation) hoverAnimation.stop()
  }
}

// Progress bar animation
export const animateProgressBar = (progressBar: HTMLElement, progress: number) => {
  if (!browser) return

  const progressFill = progressBar.querySelector(".progress-fill") as HTMLElement
  if (!progressFill) return

  if (prefersReducedMotion()) {
    progressFill.style.width = `${progress}%`
    return
  }

  animate(
    progressFill,
    {
      width: `${progress}%`,
    },
    {
      duration: ANIMATION_CONFIG.duration.slow,
      easing: ANIMATION_CONFIG.easing.easeOut,
    }
  )
}
