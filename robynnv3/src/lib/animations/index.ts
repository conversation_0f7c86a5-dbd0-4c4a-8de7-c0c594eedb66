import { animate, scroll, inView, timeline } from "@motionone/dom"
import { browser } from "$app/environment"

// Animation configuration
export const ANIMATION_CONFIG = {
  // Durations
  duration: {
    fast: 0.2,
    normal: 0.4,
    slow: 0.6,
    verySlow: 1.0,
  },
  // Easing curves
  easing: {
    ease: "ease",
    easeIn: "ease-in",
    easeOut: "ease-out",
    easeInOut: "ease-in-out",
    spring: [0.25, 0.46, 0.45, 0.94],
    bounce: [0.68, -0.55, 0.265, 1.55],
  },
  // Stagger delays
  stagger: {
    fast: 0.05,
    normal: 0.1,
    slow: 0.2,
  },
}

// Check if user prefers reduced motion
export const prefersReducedMotion = () => {
  if (!browser) return false
  return window.matchMedia("(prefers-reduced-motion: reduce)").matches
}

// Enhanced fade-in animation with stagger support
export const fadeInUp = (
  elements: string | Element | Element[],
  options: {
    delay?: number
    duration?: number
    distance?: number
    stagger?: number
    threshold?: number
  } = {},
) => {
  if (!browser || prefersReducedMotion()) return

  const {
    delay = 0,
    duration = ANIMATION_CONFIG.duration.normal,
    distance = 20,
    stagger = 0,
    threshold = 0.1,
  } = options

  const elementsArray =
    typeof elements === "string"
      ? Array.from(document.querySelectorAll(elements))
      : Array.isArray(elements)
        ? elements
        : [elements]

  elementsArray.forEach((element, index) => {
    if (!element) return

    // Set initial state
    animate(
      element,
      {
        opacity: 0,
        transform: `translateY(${distance}px)`,
      },
      { duration: 0 },
    )

    // Animate in when in view
    inView(
      element,
      () => {
        animate(
          element,
          {
            opacity: 1,
            transform: "translateY(0px)",
          },
          {
            duration,
            delay: delay + index * stagger,
            easing: ANIMATION_CONFIG.easing.easeOut,
          },
        )
      },
      { margin: `0px 0px -${threshold * 100}% 0px` },
    )
  })
}

// Scroll-linked parallax animation
export const parallaxScroll = (
  element: string | Element,
  options: {
    speed?: number
    direction?: "up" | "down"
    offset?: [string, string]
  } = {},
) => {
  if (!browser || prefersReducedMotion()) return

  const {
    speed = 0.5,
    direction = "up",
    offset = ["start end", "end start"],
  } = options

  const target =
    typeof element === "string" ? document.querySelector(element) : element
  if (!target) return

  const multiplier = direction === "up" ? -speed : speed

  scroll(
    animate(target, {
      transform: [
        `translateY(${-50 * multiplier}px)`,
        `translateY(${50 * multiplier}px)`,
      ],
    }),
    {
      target,
      offset,
    },
  )
}

// Scale animation on scroll
export const scaleOnScroll = (
  element: string | Element,
  options: {
    from?: number
    to?: number
    offset?: [string, string]
  } = {},
) => {
  if (!browser || prefersReducedMotion()) return

  const { from = 0.8, to = 1, offset = ["start end", "end start"] } = options

  const target =
    typeof element === "string" ? document.querySelector(element) : element
  if (!target) return

  scroll(
    animate(target, {
      transform: [`scale(${from})`, `scale(${to})`],
    }),
    {
      target,
      offset,
    },
  )
}

// Staggered card animations
export const staggerCards = (
  selector: string,
  options: {
    delay?: number
    stagger?: number
    duration?: number
  } = {},
) => {
  if (!browser || prefersReducedMotion()) return

  const {
    delay = 0,
    stagger = ANIMATION_CONFIG.stagger.normal,
    duration = ANIMATION_CONFIG.duration.normal,
  } = options

  const cards = document.querySelectorAll(selector)

  cards.forEach((card, index) => {
    // Set initial state
    animate(
      card,
      {
        opacity: 0,
        transform: "translateY(30px) scale(0.95)",
      },
      { duration: 0 },
    )

    // Animate in when in view
    inView(
      card,
      () => {
        animate(
          card,
          {
            opacity: 1,
            transform: "translateY(0px) scale(1)",
          },
          {
            duration,
            delay: delay + index * stagger,
            easing: ANIMATION_CONFIG.easing.easeOut,
          },
        )
      },
      { margin: "0px 0px -10% 0px" },
    )
  })
}

// Hero section animations
export const heroAnimations = (heroElement: string | Element) => {
  if (!browser || prefersReducedMotion()) return

  const hero =
    typeof heroElement === "string"
      ? document.querySelector(heroElement)
      : heroElement
  if (!hero) return

  // Parallax background
  parallaxScroll(hero, { speed: 0.3, direction: "up" })

  // Fade and scale hero content on scroll
  const heroContent = hero.querySelector(".hero-content")
  if (heroContent) {
    scroll(
      animate(heroContent, {
        opacity: [1, 0.3],
        transform: ["scale(1)", "scale(0.95)"],
      }),
      {
        target: hero,
        offset: ["start start", "end start"],
      },
    )
  }
}

// Button hover animations
export const buttonHoverAnimation = (selector: string) => {
  if (!browser || prefersReducedMotion()) return

  const buttons = document.querySelectorAll(selector)

  buttons.forEach((button) => {
    let hoverAnimation: any = null

    button.addEventListener("mouseenter", () => {
      if (hoverAnimation) hoverAnimation.stop()
      hoverAnimation = animate(
        button,
        {
          transform: "scale(1.05)",
        },
        {
          duration: ANIMATION_CONFIG.duration.fast,
          easing: ANIMATION_CONFIG.easing.easeOut,
        },
      )
    })

    button.addEventListener("mouseleave", () => {
      if (hoverAnimation) hoverAnimation.stop()
      hoverAnimation = animate(
        button,
        {
          transform: "scale(1)",
        },
        {
          duration: ANIMATION_CONFIG.duration.fast,
          easing: ANIMATION_CONFIG.easing.easeOut,
        },
      )
    })
  })
}

// Loading animation
export const loadingAnimation = (element: string | Element) => {
  if (!browser) return

  const target =
    typeof element === "string" ? document.querySelector(element) : element
  if (!target) return

  return animate(
    target,
    {
      transform: ["rotate(0deg)", "rotate(360deg)"],
    },
    {
      duration: 1,
      repeat: Infinity,
      easing: "linear",
    },
  )
}

// Cleanup function for animations
export const cleanupAnimations = () => {
  // Motion One automatically handles cleanup, but we can add custom cleanup here if needed
}

// Export performance and testing utilities
export {
  AnimationPerformanceMonitor,
  deviceCapabilities,
  adaptiveAnimationConfig,
  AnimationQueue,
  initializeAnimationPerformance,
} from "./performance"

export {
  AnimationTester,
  browserCompatibilityTests,
  initializeAnimationTesting,
} from "./testing"
