import { browser } from "$app/environment"
import { prefersReducedMotion } from "./index"

// Performance monitoring for animations
export class AnimationPerformanceMonitor {
  private static instance: AnimationPerformanceMonitor
  private performanceEntries: PerformanceEntry[] = []
  private animationCount = 0
  private isMonitoring = false

  static getInstance(): AnimationPerformanceMonitor {
    if (!AnimationPerformanceMonitor.instance) {
      AnimationPerformanceMonitor.instance = new AnimationPerformanceMonitor()
    }
    return AnimationPerformanceMonitor.instance
  }

  startMonitoring() {
    if (!browser || this.isMonitoring) return
    
    this.isMonitoring = true
    
    // Monitor frame rate
    this.monitorFrameRate()
    
    // Monitor memory usage
    this.monitorMemoryUsage()
    
    // Monitor animation performance
    this.monitorAnimationPerformance()
  }

  stopMonitoring() {
    this.isMonitoring = false
  }

  private monitorFrameRate() {
    let lastTime = performance.now()
    let frameCount = 0
    let fps = 0

    const measureFPS = (currentTime: number) => {
      frameCount++
      
      if (currentTime - lastTime >= 1000) {
        fps = Math.round((frameCount * 1000) / (currentTime - lastTime))
        frameCount = 0
        lastTime = currentTime
        
        // Log warning if FPS drops below 30
        if (fps < 30) {
          console.warn(`Animation FPS dropped to ${fps}. Consider optimizing animations.`)
        }
      }
      
      if (this.isMonitoring) {
        requestAnimationFrame(measureFPS)
      }
    }
    
    requestAnimationFrame(measureFPS)
  }

  private monitorMemoryUsage() {
    if (!('memory' in performance)) return

    const checkMemory = () => {
      const memory = (performance as any).memory
      const usedMB = Math.round(memory.usedJSHeapSize / 1048576)
      const totalMB = Math.round(memory.totalJSHeapSize / 1048576)
      
      // Log warning if memory usage is high
      if (usedMB > 100) {
        console.warn(`High memory usage detected: ${usedMB}MB / ${totalMB}MB`)
      }
      
      if (this.isMonitoring) {
        setTimeout(checkMemory, 5000) // Check every 5 seconds
      }
    }
    
    checkMemory()
  }

  private monitorAnimationPerformance() {
    // Monitor long animation frames
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        entries.forEach((entry) => {
          if (entry.duration > 16.67) { // More than one frame at 60fps
            console.warn(`Long animation frame detected: ${entry.duration}ms`)
          }
        })
      })
      
      try {
        observer.observe({ entryTypes: ['measure', 'navigation'] })
      } catch (e) {
        // Fallback for browsers that don't support all entry types
        console.log('Performance monitoring partially supported')
      }
    }
  }

  incrementAnimationCount() {
    this.animationCount++
  }

  decrementAnimationCount() {
    this.animationCount = Math.max(0, this.animationCount - 1)
  }

  getAnimationCount(): number {
    return this.animationCount
  }

  getPerformanceReport(): object {
    return {
      animationCount: this.animationCount,
      reducedMotion: prefersReducedMotion(),
      isMonitoring: this.isMonitoring,
      timestamp: Date.now()
    }
  }
}

// Device capability detection
export const deviceCapabilities = {
  // Check if device supports hardware acceleration
  supportsHardwareAcceleration(): boolean {
    if (!browser) return false
    
    const canvas = document.createElement('canvas')
    const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl')
    return !!gl
  },

  // Check if device is likely mobile
  isMobile(): boolean {
    if (!browser) return false
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
  },

  // Check if device has touch support
  hasTouch(): boolean {
    if (!browser) return false
    return 'ontouchstart' in window || navigator.maxTouchPoints > 0
  },

  // Estimate device performance tier
  getPerformanceTier(): 'low' | 'medium' | 'high' {
    if (!browser) return 'medium'
    
    const cores = navigator.hardwareConcurrency || 4
    const memory = (navigator as any).deviceMemory || 4
    const connection = (navigator as any).connection
    
    let score = 0
    
    // CPU cores
    if (cores >= 8) score += 3
    else if (cores >= 4) score += 2
    else score += 1
    
    // Memory
    if (memory >= 8) score += 3
    else if (memory >= 4) score += 2
    else score += 1
    
    // Connection
    if (connection) {
      if (connection.effectiveType === '4g') score += 2
      else if (connection.effectiveType === '3g') score += 1
    } else {
      score += 1 // Assume decent connection if unknown
    }
    
    if (score >= 7) return 'high'
    if (score >= 4) return 'medium'
    return 'low'
  },

  // Check if device prefers reduced motion
  prefersReducedMotion(): boolean {
    return prefersReducedMotion()
  }
}

// Adaptive animation configuration based on device capabilities
export const adaptiveAnimationConfig = {
  getDurationMultiplier(): number {
    const tier = deviceCapabilities.getPerformanceTier()
    const isMobile = deviceCapabilities.isMobile()
    
    if (deviceCapabilities.prefersReducedMotion()) return 0
    
    if (tier === 'low' || isMobile) return 0.7 // Faster animations
    if (tier === 'high') return 1.2 // Slightly slower, more polished
    return 1 // Normal speed
  },

  shouldUseComplexAnimations(): boolean {
    if (deviceCapabilities.prefersReducedMotion()) return false
    
    const tier = deviceCapabilities.getPerformanceTier()
    return tier === 'high' || tier === 'medium'
  },

  getMaxConcurrentAnimations(): number {
    const tier = deviceCapabilities.getPerformanceTier()
    
    if (tier === 'low') return 3
    if (tier === 'medium') return 6
    return 10
  },

  shouldUseHardwareAcceleration(): boolean {
    return deviceCapabilities.supportsHardwareAcceleration() && 
           !deviceCapabilities.prefersReducedMotion()
  }
}

// Animation queue manager for performance
export class AnimationQueue {
  private static instance: AnimationQueue
  private queue: Array<() => Promise<void>> = []
  private running = false
  private maxConcurrent: number

  constructor() {
    this.maxConcurrent = adaptiveAnimationConfig.getMaxConcurrentAnimations()
  }

  static getInstance(): AnimationQueue {
    if (!AnimationQueue.instance) {
      AnimationQueue.instance = new AnimationQueue()
    }
    return AnimationQueue.instance
  }

  add(animationFn: () => Promise<void>): Promise<void> {
    return new Promise((resolve, reject) => {
      this.queue.push(async () => {
        try {
          await animationFn()
          resolve()
        } catch (error) {
          reject(error)
        }
      })
      
      if (!this.running) {
        this.process()
      }
    })
  }

  private async process() {
    if (this.running || this.queue.length === 0) return
    
    this.running = true
    const monitor = AnimationPerformanceMonitor.getInstance()
    
    while (this.queue.length > 0) {
      const batch = this.queue.splice(0, this.maxConcurrent)
      
      try {
        await Promise.all(batch.map(fn => {
          monitor.incrementAnimationCount()
          return fn().finally(() => {
            monitor.decrementAnimationCount()
          })
        }))
      } catch (error) {
        console.error('Animation batch failed:', error)
      }
      
      // Small delay between batches to prevent overwhelming the main thread
      if (this.queue.length > 0) {
        await new Promise(resolve => setTimeout(resolve, 16)) // One frame
      }
    }
    
    this.running = false
  }

  clear() {
    this.queue = []
    this.running = false
  }

  getQueueLength(): number {
    return this.queue.length
  }
}

// Intersection Observer with performance optimizations
export const createOptimizedIntersectionObserver = (
  callback: IntersectionObserverCallback,
  options: IntersectionObserverInit = {}
) => {
  if (!browser) return null

  const defaultOptions: IntersectionObserverInit = {
    rootMargin: '50px',
    threshold: 0.1,
    ...options
  }

  // Use passive observation for better performance
  const observer = new IntersectionObserver((entries, observer) => {
    // Batch process entries
    requestAnimationFrame(() => {
      callback(entries, observer)
    })
  }, defaultOptions)

  return observer
}

// Debounced resize handler for responsive animations
export const createResizeHandler = (
  callback: () => void,
  delay: number = 250
) => {
  if (!browser) return () => {}

  let timeoutId: number

  const debouncedCallback = () => {
    clearTimeout(timeoutId)
    timeoutId = window.setTimeout(callback, delay)
  }

  window.addEventListener('resize', debouncedCallback, { passive: true })

  return () => {
    clearTimeout(timeoutId)
    window.removeEventListener('resize', debouncedCallback)
  }
}

// Initialize performance monitoring
export const initializeAnimationPerformance = () => {
  if (!browser) return

  const monitor = AnimationPerformanceMonitor.getInstance()
  
  // Start monitoring in development
  if (import.meta.env.DEV) {
    monitor.startMonitoring()
    
    // Log device capabilities
    console.log('Device Capabilities:', {
      performanceTier: deviceCapabilities.getPerformanceTier(),
      isMobile: deviceCapabilities.isMobile(),
      hasTouch: deviceCapabilities.hasTouch(),
      supportsHardwareAcceleration: deviceCapabilities.supportsHardwareAcceleration(),
      prefersReducedMotion: deviceCapabilities.prefersReducedMotion(),
      maxConcurrentAnimations: adaptiveAnimationConfig.getMaxConcurrentAnimations()
    })
  }
}
