import { sendAdminEmail, sendTemplatedEmail } from "$lib/mailer"
import { env } from "$env/dynamic/private"

type ContactRow = {
  first_name: string
  last_name: string
  email: string
  company_name?: string | null
  phone?: string | null
  message_body?: string | null
}

export async function handlePostSubmit(row: ContactRow) {
  try {
    // Debug environment variables in production
    console.log("🔍 ENV CHECK:", {
      hasResendKey: !!env.PRIVATE_RESEND_API_KEY,
      hasSlackUrl: !!env.SLACK_WEBHOOK_URL,
      hasFromEmail: !!(env.PRIVATE_FROM_MARKETING_EMAIL || env.PRIVATE_FROM_ADMIN_EMAIL || env.PRIVATE_ADMIN_EMAIL),
      fromEmail: env.PRIVATE_FROM_MARKETING_EMAIL || env.PRIVATE_FROM_ADMIN_EMAIL || env.PRIVATE_ADMIN_EMAIL || "<EMAIL>"
    })

    // 1) Thank-you email to the user
    await sendTemplatedEmail({
      subject: "We got your message – <PERSON><PERSON>",
      to_emails: [row.email],
      from_email: env.PRIVATE_FROM_MARKETING_EMAIL || env.PRIVATE_FROM_ADMIN_EMAIL || env.PRIVATE_ADMIN_EMAIL || "<EMAIL>",
      template_name: "contact_thankyou",
      template_properties: { first_name: row.first_name ?? "" },
    })

    // 2) Slack notification for internal team
    if (env.SLACK_WEBHOOK_URL) {
      console.log("🔍 SLACK: Sending notification to webhook")
      await fetch(env.SLACK_WEBHOOK_URL, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          text: "New contact form submission",
          blocks: [
            {
              type: "header",
              text: {
                type: "plain_text",
                text: "🆕 New Contact Request"
              }
            },
            {
              type: "section",
              fields: [
                {
                  type: "mrkdwn",
                  text: `*Name:*\n${row.first_name} ${row.last_name}`
                },
                {
                  type: "mrkdwn", 
                  text: `*Email:*\n${row.email}`
                },
                {
                  type: "mrkdwn",
                  text: `*Company:*\n${row.company_name || "Not provided"}`
                },
                {
                  type: "mrkdwn",
                  text: `*Phone:*\n${row.phone || "Not provided"}`
                }
              ]
            },
            {
              type: "section",
              text: {
                type: "mrkdwn",
                text: `*Message:*\n${row.message_body || "No message"}`
              }
            }
          ]
        }),
      }).catch((e) => console.error("Slack webhook failed", e))
    } else {
      console.log("🔍 SLACK: No webhook URL found")
    }

    // 3) Admin email for redundancy
    await sendAdminEmail({
      subject: "New contact request",
      body: `Name: ${row.first_name} ${row.last_name}
Email: ${row.email}
Company: ${row.company_name ?? "—"}
Phone: ${row.phone ?? "—"}
Message:
${row.message_body ?? "—"}`,
    })
  } catch (error) {
    console.error("Error in handlePostSubmit:", error)
  }
}
