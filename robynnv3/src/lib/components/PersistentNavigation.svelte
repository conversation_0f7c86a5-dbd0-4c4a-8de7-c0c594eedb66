<script lang="ts">
  import { navigationState, navigationActions, type NavigationItem } from '$lib/stores/navigation'
  import { page } from '$app/stores'
  import { goto } from '$app/navigation'
  import { onMount } from 'svelte'
  import { fade, fly } from 'svelte/transition'
  import { ChevronDown, ChevronUp, X } from 'lucide-svelte'

  export let envSlug: string

  let isExpanded = false
  let showTooltip = false
  let tooltipItem: NavigationItem | null = null

  onMount(() => {
    // Update navigation items with current environment
    navigationActions.updateActiveItem($page.url.pathname, envSlug)
  })

  // Reactive statement to update active states
  $: if ($page.url.pathname && envSlug) {
    navigationActions.updateActiveItem($page.url.pathname, envSlug)
  }

  function handleItemClick(item: NavigationItem) {
    goto(item.href)
  }

  function handleClose() {
    navigationActions.setAgentInactive()
  }

  function showItemTooltip(item: NavigationItem, event: MouseEvent) {
    if (!isExpanded) {
      tooltipItem = item
      showTooltip = true
    }
  }

  function hideTooltip() {
    showTooltip = false
    tooltipItem = null
  }
</script>

{#if $navigationState.showPersistentNav}
  <!-- Floating Navigation Panel -->
  <div
    class="fixed top-4 right-4 z-40 bg-background border-2 border-border shadow-soft"
    transition:fly={{ x: 100, duration: 300 }}
  >
    <!-- Header -->
    <div class="flex items-center justify-between p-3 border-b border-border bg-muted/50">
      <div class="flex items-center gap-2">
        <span class="text-sm font-bold text-foreground">Tools</span>
        {#if $navigationState.activeAgentId}
          <span class="text-xs text-muted-foreground">
            ({$navigationState.items.find(i => i.id === $navigationState.activeAgentId)?.label} active)
          </span>
        {/if}
      </div>
      <div class="flex items-center gap-1">
        <button
          on:click={() => isExpanded = !isExpanded}
          class="p-1 hover:bg-background rounded transition-colors"
          aria-label={isExpanded ? 'Collapse' : 'Expand'}
        >
          {#if isExpanded}
            <ChevronUp class="w-4 h-4" />
          {:else}
            <ChevronDown class="w-4 h-4" />
          {/if}
        </button>
        <button
          on:click={handleClose}
          class="p-1 hover:bg-background rounded transition-colors"
          aria-label="Close navigation"
        >
          <X class="w-4 h-4" />
        </button>
      </div>
    </div>

    <!-- Navigation Items -->
    <div class="relative">
      {#if isExpanded}
        <div
          class="p-2 space-y-1 min-w-[200px]"
          transition:fade={{ duration: 200 }}
        >
          {#each $navigationState.items as item}
            <button
              on:click={() => handleItemClick(item)}
              class="w-full flex items-center gap-3 p-2 text-left rounded transition-colors border-2 {item.active
                ? 'bg-primary text-primary-foreground border-border shadow-soft-sm'
                : 'hover:bg-muted border-transparent'}"
            >
              <span class="text-lg">{item.icon}</span>
              <div class="flex-1 min-w-0">
                <div class="text-sm font-medium truncate">{item.label}</div>
                {#if item.description}
                  <div class="text-xs text-muted-foreground truncate">
                    {item.description}
                  </div>
                {/if}
              </div>
            </button>
          {/each}
        </div>
      {:else}
        <!-- Compact view -->
        <div class="p-2 flex gap-1">
          {#each $navigationState.items as item}
            <button
              on:click={() => handleItemClick(item)}
              on:mouseenter={(e) => showItemTooltip(item, e)}
              on:mouseleave={hideTooltip}
              class="p-2 rounded transition-colors border-2 {item.active
                ? 'bg-primary text-primary-foreground border-border shadow-soft-sm'
                : 'hover:bg-muted border-transparent'}"
              aria-label={item.label}
            >
              <span class="text-lg">{item.icon}</span>
            </button>
          {/each}
        </div>
      {/if}

      <!-- Tooltip for compact view -->
      {#if showTooltip && tooltipItem && !isExpanded}
        <div
          class="absolute bottom-full right-0 mb-2 bg-background border-2 border-border shadow-soft p-2 min-w-[150px] z-50"
          transition:fade={{ duration: 150 }}
        >
          <div class="text-sm font-medium text-foreground">{tooltipItem.label}</div>
          {#if tooltipItem.description}
            <div class="text-xs text-muted-foreground mt-1">
              {tooltipItem.description}
            </div>
          {/if}
        </div>
      {/if}
    </div>
  </div>
{/if}

<style>
  .shadow-soft {
    box-shadow: 4px 4px 0px 0px hsl(var(--border));
  }
  
  .shadow-soft-sm {
    box-shadow: 2px 2px 0px 0px hsl(var(--border));
  }
</style>
