<script lang="ts">
  import { writable } from 'svelte/store'
  import { createEventDispatcher, tick } from 'svelte'
  import {
    Bot,
    User,
    Send,
    Loader2,
    X,
    Megaphone,
    CheckCircle2
  } from 'lucide-svelte'
  import { slide, fade, fly, scale } from 'svelte/transition'
  import { quintOut, backOut, elasticOut } from 'svelte/easing'
  import ToolBadge from './ToolBadge.svelte'
  import { ToolRegistry } from '$lib/services/tool-registry'

  export let isOpen: boolean = false
  export let envSlug: string
  export let isLoading: boolean = false
  export let progressSteps: ProgressStep[] = []
  export let currentProgress: number = 0

  const dispatch = createEventDispatcher()

  interface CampaignMessage {
    id: string
    role: 'user' | 'assistant'
    content: string
    timestamp: Date
    data?: any
    isReport?: boolean
  }

  interface ProgressStep {
    id: number
    title: string
    description: string
    status: 'pending' | 'active' | 'completed'
  }

  let messages = writable<CampaignMessage[]>([])
  let input = ''
  let messagesContainer: HTMLElement

  // Get tools for Campaign Orchestrator
  const orchestratorTools = ToolRegistry.getToolsByAgent('orchestrator-agent')

  // Optimized ID generation using crypto.randomUUID when available
  function generateId(): string {
    if (typeof crypto !== 'undefined' && crypto.randomUUID) {
      return crypto.randomUUID()
    }
    return Math.random().toString(36).substring(2, 11)
  }

  async function sendMessage(messageText?: string) {
    const userMessage = messageText || input.trim()
    if (!userMessage || isLoading) return

    input = ''
    
    // Add user message to chat
    messages.update(msgs => [
      ...msgs,
      {
        id: generateId(),
        role: 'user',
        content: userMessage,
        timestamp: new Date()
      }
    ])

    // Dispatch to parent component
    dispatch('sendMessage', { message: userMessage })
  }

  function handleKeyDown(event: KeyboardEvent) {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault()
      sendMessage()
    }
    // Escape key to close sidebar
    if (event.key === 'Escape') {
      closeSidebar()
    }
  }

  // Removed global keyboard shortcuts - handled at page level if needed

  // Debounced scroll optimization
  let scrollTimeout: ReturnType<typeof setTimeout>
  function optimizedScroll() {
    if (scrollTimeout) clearTimeout(scrollTimeout)
    scrollTimeout = setTimeout(() => {
      if (messagesContainer) {
        messagesContainer.scrollTop = messagesContainer.scrollHeight
      }
    }, 16) // ~60fps
  }

  function closeSidebar() {
    isOpen = false
    dispatch('close')
  }

  // Format content for display
  function formatContent(content: string): string {
    return content
      .replace(/^### (.+)$/gm, '<h3 class="text-lg font-bold mt-4 mb-2">$1</h3>')
      .replace(/^## (.+)$/gm, '<h2 class="text-xl font-bold mt-6 mb-3">$1</h2>')
      .replace(/^# (.+)$/gm, '<h1 class="text-2xl font-bold mb-4">$1</h1>')
      .replace(/^\* (.+)$/gm, '<li class="ml-4">• $1</li>')
      .replace(/^- (.+)$/gm, '<li class="ml-4">• $1</li>')
      .replace(/\*\*(.+?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.+?)\*/g, '<em>$1</em>')
      .replace(/\n\n/g, '</p><p class="mb-4">')
      .replace(/^/, '<p class="mb-4">')
      .replace(/$/, '</p>')
  }

  // Export function to add assistant message
  export async function addAssistantMessage(content: string, data?: any) {
    messages.update(msgs => [
      ...msgs,
      {
        id: generateId(),
        role: 'assistant',
        content,
        timestamp: new Date(),
        data,
        isReport: !!data
      }
    ])

    // Optimize scrolling with debounced approach
    await tick()
    optimizedScroll()
  }

  // Export function to populate input field from example prompts
  export function populateInput(message: string) {
    input = message
    // Focus the input field for better UX
    const inputElement = document.getElementById('message-input') as HTMLTextAreaElement
    if (inputElement) {
      inputElement.focus()
      // Move cursor to end of text
      inputElement.setSelectionRange(message.length, message.length)
    }
  }
</script>

<!-- Mobile backdrop (only visible on mobile when sidebar is open) -->
{#if isOpen}
  <div
    class="mobile-backdrop fixed inset-0 bg-black/50 z-40 md:hidden"
    on:click={closeSidebar}
    on:keydown={(e) => e.key === 'Escape' && closeSidebar()}
    role="button"
    tabindex="0"
    aria-label="Close sidebar"
  ></div>
{/if}

<!-- Sidebar Container - Responsive positioning based on screen size -->
<div
  class="sidebar-container flex flex-col bg-card border-l border-border shadow-lg transition-all duration-400 ease-out"
  class:sidebar-open={isOpen}
  class:sidebar-closed={!isOpen}
  role="complementary"
  aria-label="Deep Researcher Agent Chat Sidebar"
  aria-live="polite"
>
  {#if isOpen}
    <!-- Header -->
    <header class="flex items-center justify-between p-4 border-b border-border bg-card">
      <div class="flex items-center gap-2">
        <Megaphone class="w-5 h-5 text-primary" aria-hidden="true" />
        <h2 class="linear-heading text-lg font-semibold" id="sidebar-title">Deep Researcher Agent</h2>
      </div>
      <button
        on:click={closeSidebar}
        class="p-2 hover:bg-muted rounded-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 min-h-[44px] min-w-[44px] flex items-center justify-center"
        aria-label="Close sidebar"
        type="button"
      >
        <X class="w-4 h-4" aria-hidden="true" />
      </button>
    </header>

    <!-- Tool Access Badges -->
    <section class="px-3 sm:px-4 py-2 border-b border-border" aria-labelledby="tools-heading">
      <h3
        id="tools-heading"
        class="text-xs text-muted-foreground mb-2"
        in:fade={{ duration: 300, delay: 200 }}
      >
        Available Tools
      </h3>
      <div
        class="flex flex-wrap gap-1 sm:gap-1.5"
        role="list"
        aria-label="Available orchestrator tools"
      >
        {#each orchestratorTools as tool, i}
          <div
            in:scale={{ duration: 300, delay: 300 + (i * 100), easing: backOut }}
            class="transform hover:scale-105 transition-transform duration-200 flex-shrink-0"
            role="listitem"
          >
            <ToolBadge {tool} />
          </div>
        {/each}
      </div>
    </section>

    <!-- Messages -->
    <main
      bind:this={messagesContainer}
      class="flex-1 overflow-y-auto p-3 sm:p-4 space-y-3 sm:space-y-4"
      role="log"
      aria-label="Chat conversation"
      aria-live="polite"
      aria-atomic="false"
    >
      {#each $messages as message}
        <div
          class="flex gap-3 {message.role === 'user' ? 'flex-row-reverse' : ''}"
          in:fly={{
            y: message.role === 'user' ? -20 : 20,
            duration: 400,
            easing: quintOut
          }}
          role="article"
          aria-label="{message.role === 'user' ? 'User' : 'Assistant'} message"
        >
          <div
            class="w-8 h-8 flex-shrink-0 flex items-center justify-center rounded-full border-2 border-border bg-{message.role === 'user' ? 'primary' : 'secondary'} transition-all duration-200 hover:scale-110"
            in:scale={{ duration: 300, delay: 100, easing: backOut }}
            aria-label="{message.role === 'user' ? 'User' : 'Assistant'} avatar"
          >
            {#if message.role === 'user'}
              <User class="w-4 h-4 text-primary-foreground" aria-hidden="true" />
            {:else}
              <Bot class="w-4 h-4 text-secondary-foreground" aria-hidden="true" />
            {/if}
          </div>
          <div class="flex-1 {message.role === 'user' ? 'text-right' : ''}">
            <div
              class="inline-block max-w-full p-3 rounded-lg {message.role === 'user' ? 'bg-primary text-primary-foreground' : 'bg-muted'} transform transition-all duration-200 hover:shadow-md"
              in:scale={{ duration: 300, delay: 200, easing: quintOut }}
            >
              {#if message.role === 'assistant'}
                <div class="prose prose-sm max-w-none" role="region" aria-label="Assistant response">
                  {@html formatContent(message.content)}
                </div>
              {:else}
                <p class="text-sm">{message.content}</p>
              {/if}
            </div>
            <div
              class="text-xs text-muted-foreground mt-1 {message.role === 'user' ? 'text-right' : ''}"
              in:fade={{ duration: 300, delay: 400 }}
              aria-label="Message sent at {message.timestamp.toLocaleTimeString()}"
            >
              {message.timestamp.toLocaleTimeString()}
            </div>
          </div>
        </div>
      {/each}

      <!-- Progress Steps -->
      {#if isLoading && progressSteps.length > 0}
        <section
          class="space-y-3"
          in:fly={{ y: 30, duration: 400, easing: quintOut }}
          out:fade={{ duration: 200 }}
          aria-label="Campaign orchestrator progress"
          role="status"
          aria-live="polite"
        >
          <!-- Overall Progress Bar -->
          <div
            class="p-4 rounded-lg bg-card border border-border shadow-sm transform transition-all duration-300 hover:shadow-md"
            in:scale={{ duration: 400, delay: 100, easing: backOut }}
            role="progressbar"
            aria-valuenow={currentProgress}
            aria-valuemin="0"
            aria-valuemax="100"
            aria-label="Overall progress: {currentProgress}%"
          >
            <div class="flex items-center justify-between mb-2">
              <span
                class="text-xs font-medium text-muted-foreground"
                in:fade={{ duration: 300, delay: 200 }}
                id="progress-label"
              >
                Overall Progress
              </span>
              <span
                class="text-xs font-bold text-foreground tabular-nums"
                in:scale={{ duration: 300, delay: 300, easing: elasticOut }}
                aria-describedby="progress-label"
              >
                {currentProgress}%
              </span>
            </div>
            <div class="h-2 rounded-full overflow-hidden bg-secondary" role="presentation">
              <div
                class="h-full transition-all duration-700 ease-out bg-primary rounded-full"
                style="width: {currentProgress}%"
                aria-hidden="true"
              ></div>
            </div>
          </div>

          <!-- Step Details -->
          <div class="space-y-2">
            {#each progressSteps as step, i}
              <div
                class="flex items-center gap-3 p-3 rounded-md border border-border bg-card/50 transition-all duration-200 hover:bg-card"
                in:fly={{ x: -20, duration: 300, delay: 200 + (i * 100), easing: quintOut }}
              >
                <div class="flex-shrink-0">
                  {#if step.status === 'completed'}
                    <div in:scale={{ duration: 400, easing: backOut }}>
                      <CheckCircle2
                        class="w-4 h-4 text-green-500 transition-all duration-300"
                        aria-label="Step completed"
                      />
                    </div>
                  {:else if step.status === 'active'}
                    <div in:scale={{ duration: 300, easing: backOut }}>
                      <Loader2
                        class="w-4 h-4 animate-spin text-primary"
                        aria-label="Step in progress"
                      />
                    </div>
                  {:else}
                    <div
                      class="w-4 h-4 rounded-full border-2 border-muted-foreground/30 transition-all duration-300"
                      in:scale={{ duration: 300, delay: 100 }}
                      aria-label="Step pending"
                    ></div>
                  {/if}
                </div>
                <div class="flex-1">
                  <span
                    class="text-sm {step.status === 'completed' ? 'text-muted-foreground' : 'text-foreground'} transition-colors duration-300"
                    in:fade={{ duration: 300, delay: 100 }}
                  >
                    {step.title}
                  </span>
                  {#if step.status === 'active' && step.description}
                    <div
                      class="text-xs text-muted-foreground mt-1"
                      in:fly={{ y: 10, duration: 300, delay: 200 }}
                    >
                      {step.description}
                    </div>
                  {/if}
                </div>
              </div>
            {/each}
          </div>
        </section>
      {/if}
    </main>

    <!-- Input -->
    <footer
      class="p-3 sm:p-4 border-t border-border"
      in:slide={{ duration: 300, delay: 400, axis: 'y' }}
    >
      <form on:submit|preventDefault={() => sendMessage()} class="flex gap-2">
        <label for="message-input" class="sr-only">Enter your message</label>
        <textarea
          id="message-input"
          bind:value={input}
          on:keydown={handleKeyDown}
          placeholder="Enter company name or domain to research..."
          class="flex-1 resize-none border border-input bg-background rounded-md px-3 py-2 text-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 transition-all duration-200 hover:border-primary/30 min-h-[44px] disabled:cursor-not-allowed disabled:opacity-50"
          rows="2"
          disabled={isLoading}
          aria-describedby="input-help"
          aria-label="Message input"
        ></textarea>
        <div id="input-help" class="sr-only">
          Press Enter to send message, Shift+Enter for new line
        </div>
        <button
          type="submit"
          disabled={!input.trim() || isLoading}
          class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-3 flex-shrink-0 min-w-[44px]"
          aria-label="{isLoading ? 'Sending message...' : 'Send message'}"
        >
          {#if isLoading}
            <Loader2 class="w-4 h-4 animate-spin" />
          {:else}
            <Send class="w-4 h-4" />
          {/if}
        </button>
      </form>
    </footer>
  {/if}
</div>

<style>
  /* Desktop: Sidebar in normal document flow */
  .sidebar-container {
    width: 0;
    overflow: hidden;
  }

  .sidebar-open {
    width: 24rem; /* 384px */
  }

  .sidebar-closed {
    width: 0;
  }

  /* Mobile: Overlay behavior (< 768px) */
  @media (max-width: 767px) {
    .sidebar-container {
      position: fixed;
      top: 0;
      right: 0;
      bottom: 0;
      z-index: 50;
      width: 100% !important;
      max-width: 100%;
      transform: translateX(100%);
      transition: transform 400ms ease-out;
    }

    .sidebar-open {
      transform: translateX(0);
      width: 100% !important;
    }

    .sidebar-closed {
      transform: translateX(100%);
      width: 100% !important;
    }
  }

  /* Mobile backdrop styling */
  .mobile-backdrop {
    transition: opacity 300ms ease-out;
  }
</style>
