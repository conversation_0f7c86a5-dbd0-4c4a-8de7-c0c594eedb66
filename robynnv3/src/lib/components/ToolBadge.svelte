<script lang="ts">
  import type { ToolInfo } from '$lib/services/tool-registry'

  export let tool: ToolInfo
</script>

<div
  class="inline-flex items-center gap-1.5 px-2 py-1 bg-secondary text-secondary-foreground text-xs font-medium border border-border rounded-md max-w-full transition-colors hover:bg-secondary/80"
  title={tool.description}
  role="button"
  tabindex="0"
>
  <span class="text-xs flex-shrink-0" aria-hidden="true">{tool.icon}</span>
  <span class="truncate">{tool.name}</span>
</div>
