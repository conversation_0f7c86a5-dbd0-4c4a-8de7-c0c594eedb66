<script lang="ts">
  import { createEventDispatcher } from "svelte"
  import { 
    Plus, 
    FileText, 
    Search, 
    Globe, 
    MessageSquare, 
    FolderOpen,
    ChevronDown,
    ChevronRight,
    Filter
  } from "lucide-svelte"

  export let documents: any[] = []
  export let contentTypes: any[] = []
  export let selectedContentType: string = "all"
  export let searchTerm: string = ""
  export let isLoading: boolean = false

  const dispatch = createEventDispatcher()

  // Panel sections state
  let documentsExpanded = true
  let externalSourcesExpanded = true
  let collectionsExpanded = true

  // External sources state
  let externalSources = [
    {
      id: 'websites',
      name: 'Websites',
      icon: Globe,
      count: 0,
      connected: false,
      color: 'text-blue-500'
    },
    {
      id: 'slack',
      name: 'Slack Channels',
      icon: MessageSquare,
      count: 0,
      connected: false,
      color: 'text-purple-500'
    },
    {
      id: 'gdrive',
      name: 'Google Drive',
      icon: FolderOpen,
      count: 0,
      connected: false,
      color: 'text-green-500'
    }
  ]

  // Mock data for external sources
  const mockWebsites = [
    {
      id: 'web-1',
      title: 'Company Blog - Latest Marketing Trends',
      url: 'https://company.com/blog/marketing-trends-2024',
      snippet: 'Discover the top marketing trends shaping 2024, from AI-powered personalization to sustainable branding strategies...',
      lastCrawled: '2024-01-15'
    },
    {
      id: 'web-2',
      title: 'Product Documentation - API Guide',
      url: 'https://docs.company.com/api/getting-started',
      snippet: 'Complete guide to integrating our API, including authentication, endpoints, and best practices for developers...',
      lastCrawled: '2024-01-14'
    },
    {
      id: 'web-3',
      title: 'Customer Success Stories',
      url: 'https://company.com/case-studies/enterprise-growth',
      snippet: 'Learn how Fortune 500 companies achieved 300% growth using our platform with detailed case studies and metrics...',
      lastCrawled: '2024-01-13'
    },
    {
      id: 'web-4',
      title: 'Industry Report - Market Analysis',
      url: 'https://research.company.com/reports/market-analysis-q4',
      snippet: 'Comprehensive Q4 market analysis covering competitive landscape, emerging opportunities, and strategic recommendations...',
      lastCrawled: '2024-01-12'
    }
  ]

  const mockSlackChannels = [
    {
      id: 'slack-1',
      name: '#marketing-team',
      members: 24,
      lastMessage: 'Just shared the new campaign metrics - looking great!',
      lastActivity: '2 hours ago'
    },
    {
      id: 'slack-2',
      name: '#product-updates',
      members: 156,
      lastMessage: 'New feature release scheduled for next week',
      lastActivity: '4 hours ago'
    },
    {
      id: 'slack-3',
      name: '#customer-feedback',
      members: 12,
      lastMessage: 'Positive feedback on the latest UI improvements',
      lastActivity: '1 day ago'
    }
  ]

  const mockGoogleDrive = [
    {
      id: 'gdrive-1',
      name: 'Q4 Marketing Strategy.docx',
      type: 'document',
      icon: '📄',
      lastModified: '2024-01-15',
      size: '2.4 MB'
    },
    {
      id: 'gdrive-2',
      name: 'Campaign Performance Data.xlsx',
      type: 'spreadsheet',
      icon: '📊',
      lastModified: '2024-01-14',
      size: '1.8 MB'
    },
    {
      id: 'gdrive-3',
      name: 'Brand Guidelines Presentation.pptx',
      type: 'presentation',
      icon: '📈',
      lastModified: '2024-01-13',
      size: '5.2 MB'
    },
    {
      id: 'gdrive-4',
      name: 'Content Calendar Template.docx',
      type: 'document',
      icon: '📄',
      lastModified: '2024-01-12',
      size: '892 KB'
    }
  ]

  // Connected sources data
  let connectedWebsites: any[] = []
  let connectedSlackChannels: any[] = []
  let connectedGoogleDrive: any[] = []

  // Smart collections
  const collections = [
    { id: 'recent', name: 'Recent', count: documents.slice(0, 5).length },
    { id: 'favorites', name: 'Favorites', count: 0 },
    { id: 'shared', name: 'Shared', count: 0 }
  ]

  function handleCreateDocument() {
    dispatch('createDocument')
  }

  function handleOpenDocument(doc: any) {
    dispatch('openDocument', doc)
  }

  function handleDeleteDocument(docId: string) {
    dispatch('deleteDocument', docId)
  }

  function handleSearch() {
    dispatch('searchDocuments')
  }

  function handleContentTypeChange() {
    dispatch('loadDocuments')
  }

  function formatDate(dateString: string) {
    return new Date(dateString).toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
    })
  }

  function getContentTypeIcon(type: string) {
    switch (type) {
      case 'article': return '📄'
      case 'blog-post': return '📝'
      case 'whitepaper': return '📋'
      case 'social-media': return '📱'
      case 'email': return '✉️'
      case 'documentation': return '📚'
      case 'press-release': return '📢'
      case 'case-study': return '🔍'
      case 'newsletter': return '📰'
      case 'landing-page': return '🎯'
      default: return '📄'
    }
  }

  function handleConnectSource(sourceId: string) {
    console.log('handleConnectSource called with sourceId:', sourceId)

    const sourceIndex = externalSources.findIndex(s => s.id === sourceId)
    console.log('sourceIndex found:', sourceIndex)

    if (sourceIndex === -1) {
      console.error('Source not found:', sourceId)
      return
    }

    // Update the source to connected state and add mock data
    externalSources[sourceIndex].connected = true
    console.log('Updated source to connected:', externalSources[sourceIndex])

    let sourceData: any[] = []
    switch (sourceId) {
      case 'websites':
        connectedWebsites = [...mockWebsites]
        externalSources[sourceIndex].count = mockWebsites.length
        sourceData = mockWebsites
        console.log('Connected websites:', connectedWebsites.length)
        break
      case 'slack':
        connectedSlackChannels = [...mockSlackChannels]
        externalSources[sourceIndex].count = mockSlackChannels.length
        sourceData = mockSlackChannels
        console.log('Connected slack channels:', connectedSlackChannels.length)
        break
      case 'gdrive':
        connectedGoogleDrive = [...mockGoogleDrive]
        externalSources[sourceIndex].count = mockGoogleDrive.length
        sourceData = mockGoogleDrive
        console.log('Connected google drive files:', connectedGoogleDrive.length)
        break
    }

    // Trigger reactivity
    externalSources = [...externalSources]
    console.log('Updated externalSources:', externalSources)

    // Emit event to parent to display content in main panel
    dispatch('externalSourceConnected', { sourceId, data: sourceData })
  }

  function handleSelectExternalSource(sourceId: string) {
    if (!externalSources.find(s => s.id === sourceId)?.connected) return

    let sourceData: any[] = []
    switch (sourceId) {
      case 'websites':
        sourceData = connectedWebsites
        break
      case 'slack':
        sourceData = connectedSlackChannels
        break
      case 'gdrive':
        sourceData = connectedGoogleDrive
        break
    }

    dispatch('externalSourceSelected', { sourceId })
  }

  function formatLastModified(dateString: string) {
    return new Date(dateString).toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
    })
  }
</script>

<div class="h-full flex flex-col bg-card">
  <!-- Header -->
  <div class="p-4 border-b border-border">
    <div class="flex items-center justify-between mb-3">
      <h2 class="text-lg font-semibold text-foreground">Content Board</h2>
      <button
        on:click={handleCreateDocument}
        class="p-1.5 hover:bg-muted rounded-lg transition-colors"
        title="Create new document"
      >
        <Plus class="w-4 h-4 text-foreground" />
      </button>
    </div>

    <!-- Search -->
    <div class="relative">
      <Search class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
      <input
        bind:value={searchTerm}
        on:input={handleSearch}
        placeholder="Search content..."
        class="w-full pl-9 pr-3 py-2 text-sm border border-border rounded-lg bg-background focus:outline-none focus:ring-2 focus:ring-primary"
      />
    </div>
  </div>

  <!-- Content -->
  <div class="flex-1 overflow-y-auto">
    <!-- Documents Section -->
    <div class="p-2">
      <button
        on:click={() => documentsExpanded = !documentsExpanded}
        class="flex items-center justify-between w-full p-2 hover:bg-muted rounded-lg transition-colors"
      >
        <div class="flex items-center gap-2">
          {#if documentsExpanded}
            <ChevronDown class="w-4 h-4 text-muted-foreground" />
          {:else}
            <ChevronRight class="w-4 h-4 text-muted-foreground" />
          {/if}
          <FileText class="w-4 h-4 text-foreground" />
          <span class="text-sm font-medium text-foreground">Documents</span>
        </div>
        <span class="text-xs text-muted-foreground bg-muted px-2 py-0.5 rounded-full">
          {documents.length}
        </span>
      </button>

      {#if documentsExpanded}
        <div class="ml-6 mt-2 space-y-1">
          <!-- Content Type Filter -->
          <div class="mb-3">
            <select
              bind:value={selectedContentType}
              on:change={handleContentTypeChange}
              class="w-full text-xs px-2 py-1.5 border border-border bg-background rounded focus:outline-none focus:ring-1 focus:ring-primary"
            >
              {#each contentTypes as type}
                <option value={type.value}>{type.label}</option>
              {/each}
            </select>
          </div>

          <!-- Document List -->
          {#if isLoading}
            <div class="space-y-2">
              {#each Array(3) as _}
                <div class="h-12 bg-muted animate-pulse rounded"></div>
              {/each}
            </div>
          {:else if documents.length === 0}
            <div class="text-xs text-muted-foreground text-center py-4">
              No documents found
            </div>
          {:else}
            <div class="space-y-1 max-h-64 overflow-y-auto">
              {#each documents.slice(0, 10) as doc}
                <button
                  on:click={() => handleOpenDocument(doc)}
                  class="w-full p-2 hover:bg-muted rounded-lg transition-colors text-left group"
                >
                  <div class="flex items-start gap-2">
                    <span class="text-sm mt-0.5">{getContentTypeIcon(doc.content_type)}</span>
                    <div class="flex-1 min-w-0">
                      <div class="text-xs font-medium text-foreground truncate">
                        {doc.title}
                      </div>
                      <div class="text-xs text-muted-foreground">
                        {formatDate(doc.updated_at)}
                      </div>
                    </div>
                  </div>
                </button>
              {/each}
            </div>
          {/if}
        </div>
      {/if}
    </div>

    <!-- External Sources Section -->
    <div class="p-2">
      <button
        on:click={() => externalSourcesExpanded = !externalSourcesExpanded}
        class="flex items-center justify-between w-full p-2 hover:bg-muted rounded-lg transition-colors"
      >
        <div class="flex items-center gap-2">
          {#if externalSourcesExpanded}
            <ChevronDown class="w-4 h-4 text-muted-foreground" />
          {:else}
            <ChevronRight class="w-4 h-4 text-muted-foreground" />
          {/if}
          <Globe class="w-4 h-4 text-foreground" />
          <span class="text-sm font-medium text-foreground">External Sources</span>
        </div>
      </button>

      {#if externalSourcesExpanded}
        <div class="ml-6 mt-2 space-y-3">
          {#each externalSources as source}
            <div class="space-y-2">
              <!-- Source Header -->
              <div class="flex items-center justify-between p-2 hover:bg-muted rounded-lg transition-colors {source.connected ? 'cursor-pointer' : ''}"
                   on:click={() => source.connected && handleSelectExternalSource(source.id)}>
                <div class="flex items-center gap-2">
                  <svelte:component this={source.icon} class="w-4 h-4 {source.color}" />
                  <span class="text-xs text-foreground">{source.name}</span>
                  {#if source.connected}
                    <span class="text-xs text-muted-foreground">• Click to view</span>
                  {/if}
                </div>
                <div class="flex items-center gap-2">
                  <span class="text-xs text-muted-foreground bg-muted px-2 py-0.5 rounded-full">
                    {source.count}
                  </span>
                  {#if !source.connected}
                    <button
                      on:click|stopPropagation={() => {
                        console.log('Button clicked for source:', source.id)
                        handleConnectSource(source.id)
                      }}
                      class="text-xs text-primary hover:underline"
                    >
                      Connect
                    </button>
                  {/if}
                </div>
              </div>


            </div>
          {/each}
        </div>
      {/if}
    </div>

    <!-- Collections Section -->
    <div class="p-2">
      <button
        on:click={() => collectionsExpanded = !collectionsExpanded}
        class="flex items-center justify-between w-full p-2 hover:bg-muted rounded-lg transition-colors"
      >
        <div class="flex items-center gap-2">
          {#if collectionsExpanded}
            <ChevronDown class="w-4 h-4 text-muted-foreground" />
          {:else}
            <ChevronRight class="w-4 h-4 text-muted-foreground" />
          {/if}
          <Filter class="w-4 h-4 text-foreground" />
          <span class="text-sm font-medium text-foreground">Collections</span>
        </div>
      </button>

      {#if collectionsExpanded}
        <div class="ml-6 mt-2 space-y-1">
          {#each collections as collection}
            <button class="flex items-center justify-between w-full p-2 hover:bg-muted rounded-lg transition-colors">
              <span class="text-xs text-foreground">{collection.name}</span>
              <span class="text-xs text-muted-foreground bg-muted px-2 py-0.5 rounded-full">
                {collection.count}
              </span>
            </button>
          {/each}
        </div>
      {/if}
    </div>
  </div>
</div>
