<script lang="ts">
  import { createEventDispatcher } from 'svelte'
  import { Search, Building2, Users, TrendingUp } from 'lucide-svelte'

  const dispatch = createEventDispatcher()

  const examplePrompts = [
    {
      id: 'deep-web-analysis',
      title: 'Deep Web Analysis',
      description: 'Comprehensive analysis including landing page strategy, pricing intelligence, and technical stack',
      icon: Search,
      prompt: 'Deep analysis of Stripe\'s pricing strategy and web presence',
      category: 'Enhanced Research'
    },
    {
      id: 'technical-competitive-analysis',
      title: 'Technical Competitive Analysis',
      description: 'Technology stack analysis and competitive positioning with real-time web scraping',
      icon: TrendingUp,
      prompt: 'Technical stack analysis and competitive positioning for Vercel',
      category: 'Technical Intelligence'
    },
    {
      id: 'market-intelligence',
      title: 'Market Intelligence',
      description: 'Comprehensive market research combining business data with web intelligence',
      icon: Building2,
      prompt: 'Comprehensive market research for AI companies like Anthropic',
      category: 'Market Research'
    },
    {
      id: 'conversion-optimization',
      title: 'Conversion Optimization',
      description: 'Landing page optimization insights from top SaaS competitors with contact discovery',
      icon: Users,
      prompt: 'Landing page optimization insights from top SaaS competitors',
      category: 'Growth Intelligence'
    }
  ]

  function selectPrompt(prompt: typeof examplePrompts[0]) {
    dispatch('selectPrompt', {
      message: prompt.prompt,
      title: prompt.title
    })
  }
</script>

<div class="max-w-4xl mx-auto">
  <!-- Header -->
  <div class="text-center mb-12">
    <h1 class="text-3xl font-bold mb-4">Deep Researcher Agent</h1>
    <p class="text-lg text-muted-foreground mb-2">
      Comprehensive company research with competitor analysis and contact discovery
    </p>
    <p class="text-sm text-muted-foreground">
      Powered by Apollo API, Exa Search, and Firecrawl web intelligence
    </p>
  </div>

  <!-- Example Prompts Grid -->
  <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
    {#each examplePrompts as prompt}
      <button 
        on:click={() => selectPrompt(prompt)}
        class="card-professional p-6 text-left hover-transform transition-all duration-200 group"
      >
        <div class="flex items-start gap-4">
          <div class="flex-shrink-0 w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center group-hover:bg-primary/20 transition-colors">
            <svelte:component this={prompt.icon} class="w-6 h-6 text-primary" />
          </div>
          
          <div class="flex-1 min-w-0">
            <div class="flex items-center gap-2 mb-2">
              <h3 class="font-semibold text-lg">{prompt.title}</h3>
              <span class="text-xs px-2 py-1 bg-muted text-muted-foreground rounded">
                {prompt.category}
              </span>
            </div>
            
            <p class="text-sm text-muted-foreground mb-4 leading-relaxed">
              {prompt.description}
            </p>
            
            <div class="bg-muted/50 rounded-lg p-3 border-l-4 border-primary">
              <div class="text-xs text-muted-foreground mb-1">Example:</div>
              <div class="text-sm font-medium text-primary">"{prompt.prompt}"</div>
            </div>
          </div>
        </div>
      </button>
    {/each}
  </div>

  <!-- Getting Started Tips -->
  <div class="bg-card border border-border rounded-lg p-6">
    <h3 class="font-semibold mb-3">Getting Started</h3>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
      <div class="flex items-start gap-2">
        <div class="w-6 h-6 bg-primary/10 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
          <span class="text-xs font-bold text-primary">1</span>
        </div>
        <div>
          <div class="font-medium mb-1">Enter Company</div>
          <div class="text-muted-foreground">Provide a company name or domain</div>
        </div>
      </div>
      
      <div class="flex items-start gap-2">
        <div class="w-6 h-6 bg-primary/10 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
          <span class="text-xs font-bold text-primary">2</span>
        </div>
        <div>
          <div class="font-medium mb-1">AI Research</div>
          <div class="text-muted-foreground">Watch as AI gathers intelligence</div>
        </div>
      </div>
      
      <div class="flex items-start gap-2">
        <div class="w-6 h-6 bg-primary/10 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
          <span class="text-xs font-bold text-primary">3</span>
        </div>
        <div>
          <div class="font-medium mb-1">Get Results</div>
          <div class="text-muted-foreground">Receive comprehensive insights</div>
        </div>
      </div>
    </div>
  </div>
</div>
