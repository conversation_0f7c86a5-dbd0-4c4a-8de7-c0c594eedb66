<script lang="ts">
  import { 
    Target, 
    TrendingUp, 
    Lightbulb, 
    BarChart3,
    CheckCircle2,
    Clock,
    ExternalLink,
    Copy,
    ChevronDown,
    ChevronUp,
    AlertCircle
  } from 'lucide-svelte'
  import { fade, slide } from 'svelte/transition'
  import { quintOut } from 'svelte/easing'

  export let intelligence: any = {}
  export let actionableInsights: any = {}

  // Get confidence color based on score
  function getConfidenceColor(score: number): string {
    if (score >= 0.9) return 'text-green-600'
    if (score >= 0.7) return 'text-yellow-600'
    return 'text-red-600'
  }

  // Format confidence score as percentage
  function formatConfidence(score: number): string {
    return `${Math.round(score * 100)}%`
  }

  // Format date for display
  function formatDate(dateString: string): string {
    if (!dateString) return 'Unknown'
    try {
      return new Date(dateString).toLocaleDateString()
    } catch {
      return 'Unknown'
    }
  }

  // Get priority color
  function getPriorityColor(priority: string): string {
    switch (priority?.toLowerCase()) {
      case 'high': return 'bg-red-100 text-red-800'
      case 'medium': return 'bg-yellow-100 text-yellow-800'
      case 'low': return 'bg-green-100 text-green-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  // Get impact color
  function getImpactColor(impact: string): string {
    switch (impact?.toLowerCase()) {
      case 'high': return 'bg-purple-100 text-purple-800'
      case 'medium': return 'bg-blue-100 text-blue-800'
      case 'low': return 'bg-gray-100 text-gray-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  // Copy insight to clipboard
  async function copyInsight(text: string) {
    try {
      await navigator.clipboard.writeText(text)
      // Could add a toast notification here
    } catch (err) {
      console.error('Failed to copy insight:', err)
    }
  }

  // Expandable sections state
  let expandedSections = {
    market_insights: false,
    competitive_landscape: false,
    key_differentiators: false,
    business_model: false,
    target_market: false
  }

  function toggleSection(section: string) {
    expandedSections[section] = !expandedSections[section]
  }

  // Intelligence sections configuration
  $: intelligenceSections = [
    {
      key: 'market_insights',
      title: 'Market Insights',
      icon: BarChart3,
      data: intelligence.market_insights
    },
    {
      key: 'competitive_landscape',
      title: 'Competitive Landscape',
      icon: TrendingUp,
      data: intelligence.competitive_landscape
    },
    {
      key: 'key_differentiators',
      title: 'Key Differentiators',
      icon: Target,
      data: intelligence.key_differentiators
    },
    {
      key: 'business_model',
      title: 'Business Model',
      icon: Lightbulb,
      data: intelligence.business_model
    },
    {
      key: 'target_market',
      title: 'Target Market',
      icon: Target,
      data: intelligence.target_market
    }
  ].filter(section => section.data && section.data.content)
</script>

<div 
  class="card-professional p-6 space-y-6"
  in:fade={{ duration: 400, easing: quintOut }}
>
  <!-- Header -->
  <div class="flex items-center justify-between">
    <div class="flex items-center gap-3">
      <div class="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
        <Lightbulb class="w-5 h-5 text-primary" />
      </div>
      <div>
        <h2 class="text-lg font-bold text-foreground">Intelligence Insights</h2>
        <p class="text-sm text-muted-foreground">
          Market analysis and strategic intelligence
        </p>
      </div>
    </div>
  </div>

  <!-- Intelligence Sections -->
  {#if intelligenceSections.length > 0}
    <div class="space-y-4">
      {#each intelligenceSections as section}
        <div class="border border-border rounded-lg overflow-hidden">
          <!-- Section Header -->
          <button
            on:click={() => toggleSection(section.key)}
            class="w-full p-4 text-left hover:bg-secondary/50 transition-colors"
          >
            <div class="flex items-center justify-between">
              <div class="flex items-center gap-3">
                <div class="w-8 h-8 bg-primary/10 rounded-md flex items-center justify-center">
                  <svelte:component this={section.icon} class="w-4 h-4 text-primary" />
                </div>
                <div>
                  <h3 class="font-semibold text-foreground">{section.title}</h3>
                  <div class="flex items-center gap-2 mt-1">
                    {#if section.data.confidence_score}
                      <div class="flex items-center gap-1">
                        <CheckCircle2 class="w-3 h-3 {getConfidenceColor(section.data.confidence_score)}" />
                        <span class="text-xs {getConfidenceColor(section.data.confidence_score)}">
                          {formatConfidence(section.data.confidence_score)}
                        </span>
                      </div>
                    {/if}
                    {#if section.data.last_updated}
                      <div class="flex items-center gap-1 text-xs text-muted-foreground">
                        <Clock class="w-3 h-3" />
                        <span>{formatDate(section.data.last_updated)}</span>
                      </div>
                    {/if}
                  </div>
                </div>
              </div>
              
              <div class="flex items-center gap-2">
                <div
                  role="button"
                  tabindex="0"
                  on:click|stopPropagation={() => copyInsight(section.data.content)}
                  on:keydown|stopPropagation={(e) => e.key === 'Enter' && copyInsight(section.data.content)}
                  class="p-1 text-muted-foreground hover:text-foreground transition-colors cursor-pointer"
                  title="Copy insight"
                >
                  <Copy class="w-4 h-4" />
                </div>

                {#if expandedSections[section.key]}
                  <ChevronUp class="w-4 h-4 text-muted-foreground" />
                {:else}
                  <ChevronDown class="w-4 h-4 text-muted-foreground" />
                {/if}
              </div>
            </div>
          </button>

          <!-- Section Content -->
          {#if expandedSections[section.key]}
            <div 
              class="px-4 pb-4 border-t border-border bg-secondary/20"
              transition:slide={{ duration: 200, easing: quintOut }}
            >
              <div class="mt-4 space-y-3">
                <p class="text-sm text-foreground leading-relaxed">
                  {section.data.content}
                </p>
                
                {#if section.data.sources && section.data.sources.length > 0}
                  <div class="flex items-center gap-2 text-xs text-muted-foreground">
                    <span>Sources:</span>
                    <span class="font-medium">{section.data.sources.join(', ')}</span>
                  </div>
                {/if}
              </div>
            </div>
          {/if}
        </div>
      {/each}
    </div>
  {/if}

  <!-- Actionable Insights -->
  {#if actionableInsights && (actionableInsights.marketing_opportunities || actionableInsights.competitive_advantages || actionableInsights.contact_strategy)}
    <div class="space-y-4 pt-6 border-t border-border">
      <h3 class="text-lg font-semibold text-foreground flex items-center gap-2">
        <Target class="w-5 h-5" />
        Actionable Insights
      </h3>

      <!-- Marketing Opportunities -->
      {#if actionableInsights.marketing_opportunities && actionableInsights.marketing_opportunities.length > 0}
        <div class="space-y-3">
          <h4 class="text-sm font-medium text-foreground">Marketing Opportunities</h4>
          <div class="space-y-2">
            {#each actionableInsights.marketing_opportunities as opportunity}
              <div class="p-3 border border-border rounded-lg bg-secondary/30">
                <div class="flex items-start justify-between mb-2">
                  <div class="flex-1">
                    <p class="text-sm font-medium text-foreground">{opportunity.insight}</p>
                    {#if opportunity.recommended_action}
                      <p class="text-xs text-muted-foreground mt-1">
                        <strong>Action:</strong> {opportunity.recommended_action}
                      </p>
                    {/if}
                  </div>
                  <div class="flex items-center gap-2 ml-3">
                    {#if opportunity.priority}
                      <span class="px-2 py-1 text-xs rounded {getPriorityColor(opportunity.priority)}">
                        {opportunity.priority}
                      </span>
                    {/if}
                    {#if opportunity.confidence_score}
                      <div class="flex items-center gap-1">
                        <CheckCircle2 class="w-3 h-3 {getConfidenceColor(opportunity.confidence_score)}" />
                        <span class="text-xs {getConfidenceColor(opportunity.confidence_score)}">
                          {formatConfidence(opportunity.confidence_score)}
                        </span>
                      </div>
                    {/if}
                  </div>
                </div>
              </div>
            {/each}
          </div>
        </div>
      {/if}

      <!-- Competitive Advantages -->
      {#if actionableInsights.competitive_advantages && actionableInsights.competitive_advantages.length > 0}
        <div class="space-y-3">
          <h4 class="text-sm font-medium text-foreground">Competitive Advantages</h4>
          <div class="space-y-2">
            {#each actionableInsights.competitive_advantages as advantage}
              <div class="p-3 border border-border rounded-lg bg-secondary/30">
                <div class="flex items-start justify-between">
                  <p class="text-sm text-foreground flex-1">{advantage.advantage}</p>
                  <div class="flex items-center gap-2 ml-3">
                    {#if advantage.impact}
                      <span class="px-2 py-1 text-xs rounded {getImpactColor(advantage.impact)}">
                        {advantage.impact} impact
                      </span>
                    {/if}
                    {#if advantage.confidence_score}
                      <div class="flex items-center gap-1">
                        <CheckCircle2 class="w-3 h-3 {getConfidenceColor(advantage.confidence_score)}" />
                        <span class="text-xs {getConfidenceColor(advantage.confidence_score)}">
                          {formatConfidence(advantage.confidence_score)}
                        </span>
                      </div>
                    {/if}
                  </div>
                </div>
              </div>
            {/each}
          </div>
        </div>
      {/if}

      <!-- Contact Strategy -->
      {#if actionableInsights.contact_strategy}
        <div class="space-y-3">
          <h4 class="text-sm font-medium text-foreground">Contact Strategy</h4>
          <div class="p-3 border border-border rounded-lg bg-secondary/30">
            {#if actionableInsights.contact_strategy.primary_contacts && actionableInsights.contact_strategy.primary_contacts.length > 0}
              <div class="mb-3">
                <p class="text-xs text-muted-foreground mb-1">Priority Contacts:</p>
                <div class="flex flex-wrap gap-1">
                  {#each actionableInsights.contact_strategy.primary_contacts as contact}
                    <span class="px-2 py-1 bg-primary/10 text-primary text-xs rounded">
                      {contact}
                    </span>
                  {/each}
                </div>
              </div>
            {/if}
            
            {#if actionableInsights.contact_strategy.approach_recommendations}
              <p class="text-sm text-foreground">
                <strong>Approach:</strong> {actionableInsights.contact_strategy.approach_recommendations}
              </p>
            {/if}
            
            {#if actionableInsights.contact_strategy.confidence_score}
              <div class="flex items-center gap-1 mt-2">
                <CheckCircle2 class="w-3 h-3 {getConfidenceColor(actionableInsights.contact_strategy.confidence_score)}" />
                <span class="text-xs {getConfidenceColor(actionableInsights.contact_strategy.confidence_score)}">
                  {formatConfidence(actionableInsights.contact_strategy.confidence_score)} confidence
                </span>
              </div>
            {/if}
          </div>
        </div>
      {/if}
    </div>
  {/if}

  <!-- No Data State -->
  {#if intelligenceSections.length === 0 && (!actionableInsights || (!actionableInsights.marketing_opportunities && !actionableInsights.competitive_advantages && !actionableInsights.contact_strategy))}
    <div class="text-center py-8 text-muted-foreground">
      <AlertCircle class="w-12 h-12 mx-auto mb-3 opacity-50" />
      <p class="text-sm">No intelligence insights available</p>
      <p class="text-xs mt-1">Try running a more comprehensive research query</p>
    </div>
  {/if}
</div>
