<script lang="ts">
  import { 
    User, 
    Building2, 
    MapPin, 
    Mail, 
    Phone, 
    Linkedin, 
    ExternalLink,
    Briefcase,
    Star,
    CheckCircle2,
    AlertCircle
  } from "lucide-svelte"

  export let contact: any
  export let showCompanyInfo = true
  export let compact = false

  // Extract contact data with fallbacks
  $: contactData = {
    name: contact?.name || `${contact?.first_name || ''} ${contact?.last_name || ''}`.trim() || 'Unknown Contact',
    first_name: contact?.first_name || '',
    last_name: contact?.last_name || '',
    title: contact?.title || 'Unknown Title',
    email: contact?.email || null,
    phone: contact?.phone || null,
    linkedin_url: contact?.linkedin_url || null,
    company_name: contact?.company_name || contact?.organization?.name || 'Unknown Company',
    company_domain: contact?.company_domain || contact?.domain || null,
    location: contact?.location || [contact?.city, contact?.state, contact?.country].filter(Boolean).join(', ') || 'Unknown Location',
    seniority: contact?.seniority || 'Unknown',
    department: contact?.department || contact?.departments?.[0] || 'Unknown',
    confidence_score: contact?.confidence_score || 0.75,
    source: contact?.source || 'apollo'
  }

  function getConfidenceColor(score: number) {
    if (score >= 0.8) return 'text-green-600'
    if (score >= 0.6) return 'text-yellow-600'
    return 'text-red-600'
  }

  function getConfidenceIcon(score: number) {
    if (score >= 0.8) return CheckCircle2
    if (score >= 0.6) return AlertCircle
    return AlertCircle
  }

  function getSeniorityColor(seniority: string) {
    const level = seniority.toLowerCase()
    if (level.includes('c_suite') || level.includes('ceo') || level.includes('cto') || level.includes('cfo')) {
      return 'bg-purple-100 text-purple-800 border-purple-200'
    }
    if (level.includes('vp') || level.includes('vice')) {
      return 'bg-blue-100 text-blue-800 border-blue-200'
    }
    if (level.includes('director')) {
      return 'bg-green-100 text-green-800 border-green-200'
    }
    if (level.includes('manager')) {
      return 'bg-yellow-100 text-yellow-800 border-yellow-200'
    }
    return 'bg-gray-100 text-gray-800 border-gray-200'
  }

  function formatSeniority(seniority: string) {
    return seniority.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
  }

  function formatDepartment(department: string) {
    return department.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
  }
</script>

<div class="border border-border bg-card shadow-sm rounded-lg overflow-hidden {compact ? 'p-3' : 'p-4'}">
  <!-- Contact Header -->
  <div class="flex items-start justify-between mb-3">
    <div class="flex items-start gap-3">
      <div class="p-2 bg-primary/10 rounded-lg">
        <User class="w-4 h-4 text-primary" />
      </div>
      <div class="min-w-0 flex-1">
        <h3 class="font-semibold {compact ? 'text-sm' : 'text-base'} text-foreground truncate">
          {contactData.name}
        </h3>
        <p class="text-xs text-muted-foreground mt-1 flex items-center gap-1">
          <Briefcase class="w-3 h-3" />
          {contactData.title}
        </p>
      </div>
    </div>
    
    <!-- Confidence Score -->
    <div class="flex items-center gap-1 text-xs">
      <svelte:component 
        this={getConfidenceIcon(contactData.confidence_score)} 
        class="w-3 h-3 {getConfidenceColor(contactData.confidence_score)}" 
      />
      <span class="{getConfidenceColor(contactData.confidence_score)}">
        {Math.round(contactData.confidence_score * 100)}%
      </span>
    </div>
  </div>

  <!-- Company Info -->
  {#if showCompanyInfo}
    <div class="flex items-center gap-2 mb-3 p-2 bg-muted/30 rounded-md">
      <Building2 class="w-3 h-3 text-muted-foreground" />
      <div class="min-w-0 flex-1">
        <div class="text-xs font-medium text-foreground truncate">
          {contactData.company_name}
        </div>
        {#if contactData.company_domain}
          <div class="text-xs text-muted-foreground truncate">
            {contactData.company_domain}
          </div>
        {/if}
      </div>
    </div>
  {/if}

  <!-- Contact Details -->
  <div class="space-y-2 mb-3">
    <!-- Location -->
    {#if contactData.location !== 'Unknown Location'}
      <div class="flex items-center gap-2 text-xs">
        <MapPin class="w-3 h-3 text-muted-foreground" />
        <span class="text-muted-foreground">{contactData.location}</span>
      </div>
    {/if}

    <!-- Department & Seniority -->
    <div class="flex items-center gap-2 flex-wrap">
      {#if contactData.seniority !== 'Unknown'}
        <span class="text-xs px-2 py-1 rounded-md border {getSeniorityColor(contactData.seniority)}">
          {formatSeniority(contactData.seniority)}
        </span>
      {/if}
      {#if contactData.department !== 'Unknown'}
        <span class="text-xs px-2 py-1 rounded-md bg-accent text-accent-foreground">
          {formatDepartment(contactData.department)}
        </span>
      {/if}
    </div>
  </div>

  <!-- Contact Actions -->
  <div class="flex items-center gap-2 pt-2 border-t border-border">
    {#if contactData.email}
      <a 
        href="mailto:{contactData.email}" 
        class="flex items-center gap-1 text-xs text-primary hover:underline"
        title="Send Email"
      >
        <Mail class="w-3 h-3" />
        Email
      </a>
    {/if}
    
    {#if contactData.phone}
      <a 
        href="tel:{contactData.phone}" 
        class="flex items-center gap-1 text-xs text-primary hover:underline"
        title="Call Phone"
      >
        <Phone class="w-3 h-3" />
        Call
      </a>
    {/if}
    
    {#if contactData.linkedin_url}
      <a 
        href={contactData.linkedin_url} 
        target="_blank" 
        rel="noopener noreferrer"
        class="flex items-center gap-1 text-xs text-primary hover:underline"
        title="View LinkedIn Profile"
      >
        <Linkedin class="w-3 h-3" />
        LinkedIn
        <ExternalLink class="w-2 h-2" />
      </a>
    {/if}
  </div>

  <!-- Source Attribution -->
  {#if !compact}
    <div class="mt-3 pt-2 border-t border-border">
      <div class="flex items-center justify-between text-xs text-muted-foreground">
        <span>Source: {contactData.source.toUpperCase()}</span>
        <span>Confidence: {Math.round(contactData.confidence_score * 100)}%</span>
      </div>
    </div>
  {/if}
</div>
