<script lang="ts">
  import { 
    Code2, 
    Server, 
    Shield, 
    Zap, 
    ExternalLink,
    CheckCircle2,
    Clock,
    ChevronDown,
    ChevronUp,
    Database,
    Cloud,
    Smartphone,
    Monitor
  } from 'lucide-svelte'
  import { fade, fly, slide } from 'svelte/transition'
  import { quintOut } from 'svelte/easing'

  export let technicalData: any = {}
  export let companyName: string = 'Company'

  let expandedSections: Set<string> = new Set()

  // Toggle section expansion
  function toggleSection(sectionKey: string) {
    if (expandedSections.has(sectionKey)) {
      expandedSections.delete(sectionKey)
    } else {
      expandedSections.add(sectionKey)
    }
    expandedSections = expandedSections
  }

  // Get confidence color based on score
  function getConfidenceColor(score: number): string {
    if (score >= 0.9) return 'text-green-600'
    if (score >= 0.7) return 'text-yellow-600'
    return 'text-red-600'
  }

  // Format confidence score as percentage
  function formatConfidence(score: number): string {
    return `${Math.round(score * 100)}%`
  }

  // Format date for display
  function formatDate(dateString: string): string {
    if (!dateString) return 'Unknown'
    try {
      return new Date(dateString).toLocaleDateString()
    } catch {
      return 'Unknown'
    }
  }

  // Get technology category color
  function getTechCategoryColor(category: string): string {
    const colors = {
      'frontend': 'bg-blue-500/10 text-blue-600',
      'backend': 'bg-green-500/10 text-green-600',
      'database': 'bg-purple-500/10 text-purple-600',
      'infrastructure': 'bg-orange-500/10 text-orange-600',
      'analytics': 'bg-pink-500/10 text-pink-600',
      'security': 'bg-red-500/10 text-red-600',
      'marketing': 'bg-yellow-500/10 text-yellow-600',
      'default': 'bg-gray-500/10 text-gray-600'
    }
    return colors[category.toLowerCase()] || colors.default
  }

  // Technical analysis sections configuration
  $: technicalSections = [
    {
      key: 'technology_stack',
      title: 'Technology Stack',
      icon: Code2,
      data: technicalData.technology_stack,
      description: 'Frontend, backend, and infrastructure technologies'
    },
    {
      key: 'performance_metrics',
      title: 'Performance Metrics',
      icon: Zap,
      data: technicalData.performance_metrics,
      description: 'Site speed, optimization, and user experience'
    },
    {
      key: 'security_analysis',
      title: 'Security Analysis',
      icon: Shield,
      data: technicalData.security_analysis,
      description: 'SSL, security headers, and vulnerability assessment'
    },
    {
      key: 'mobile_optimization',
      title: 'Mobile Optimization',
      icon: Smartphone,
      data: technicalData.mobile_optimization,
      description: 'Responsive design and mobile performance'
    }
  ].filter(section => section.data && Object.keys(section.data).length > 0)
</script>

<div 
  class="card-professional p-6 space-y-6"
  in:fade={{ duration: 400, easing: quintOut }}
>
  <!-- Header -->
  <div class="flex items-center justify-between">
    <div class="flex items-center gap-3">
      <div class="w-10 h-10 bg-purple-500/10 rounded-lg flex items-center justify-center">
        <Code2 class="w-5 h-5 text-purple-500" />
      </div>
      <div>
        <h2 class="text-lg font-bold text-foreground">Technical Analysis</h2>
        <p class="text-sm text-muted-foreground">
          Technology stack and performance insights for {companyName}
        </p>
      </div>
    </div>
    
    <div class="flex items-center gap-1 px-2 py-1 bg-secondary rounded-md">
      <Server class="w-3 h-3 text-muted-foreground" />
      <span class="text-xs font-medium">Tech Intelligence</span>
    </div>
  </div>

  <!-- Technical Sections -->
  {#if technicalSections.length > 0}
    <div class="space-y-4">
      {#each technicalSections as section, index (section.key)}
        <div 
          class="border border-border rounded-lg overflow-hidden"
          in:fly={{ y: 20, duration: 300, delay: index * 100, easing: quintOut }}
        >
          <!-- Section Header -->
          <button
            class="w-full flex items-center justify-between p-4 hover:bg-secondary/50 transition-colors"
            on:click={() => toggleSection(section.key)}
          >
            <div class="flex items-center gap-3">
              <svelte:component this={section.icon} class="w-4 h-4 text-muted-foreground" />
              <div class="text-left">
                <h3 class="font-medium text-foreground">{section.title}</h3>
                <p class="text-xs text-muted-foreground">{section.description}</p>
              </div>
            </div>
            
            <div class="flex items-center gap-2">
              {#if section.data.confidence_score}
                <div class="flex items-center gap-1">
                  <CheckCircle2 class="w-3 h-3 {getConfidenceColor(section.data.confidence_score)}" />
                  <span class="text-xs {getConfidenceColor(section.data.confidence_score)}">
                    {formatConfidence(section.data.confidence_score)}
                  </span>
                </div>
              {/if}
              
              {#if expandedSections.has(section.key)}
                <ChevronUp class="w-4 h-4 text-muted-foreground" />
              {:else}
                <ChevronDown class="w-4 h-4 text-muted-foreground" />
              {/if}
            </div>
          </button>

          <!-- Section Content -->
          {#if expandedSections.has(section.key)}
            <div 
              class="px-4 pb-4 border-t border-border bg-secondary/20"
              in:slide={{ duration: 300, easing: quintOut }}
            >
              <div class="space-y-4 mt-4">
                
                <!-- Technology Stack Content -->
                {#if section.key === 'technology_stack'}
                  {#if section.data.technologies && section.data.technologies.length > 0}
                    <div>
                      <h4 class="text-sm font-medium text-foreground mb-3">Technologies Detected</h4>
                      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                        {#each section.data.technologies as tech}
                          <div class="flex items-center gap-2 p-2 border border-border rounded-lg">
                            <div class="w-2 h-2 rounded-full {getTechCategoryColor(tech.category || 'default')}"></div>
                            <div class="flex-1 min-w-0">
                              <div class="text-sm font-medium text-foreground truncate">{tech.name}</div>
                              {#if tech.version}
                                <div class="text-xs text-muted-foreground">v{tech.version}</div>
                              {/if}
                            </div>
                            {#if tech.category}
                              <span class="px-1.5 py-0.5 {getTechCategoryColor(tech.category)} text-xs rounded">
                                {tech.category}
                              </span>
                            {/if}
                          </div>
                        {/each}
                      </div>
                    </div>
                  {/if}
                  
                  {#if section.data.frameworks && section.data.frameworks.length > 0}
                    <div>
                      <h4 class="text-sm font-medium text-foreground mb-2">Frameworks & Libraries</h4>
                      <div class="flex flex-wrap gap-2">
                        {#each section.data.frameworks as framework}
                          <span class="px-2 py-1 bg-blue-500/10 text-blue-600 text-xs rounded">
                            {framework}
                          </span>
                        {/each}
                      </div>
                    </div>
                  {/if}
                  
                  {#if section.data.hosting_provider}
                    <div>
                      <h4 class="text-sm font-medium text-foreground mb-2">Hosting Provider</h4>
                      <div class="flex items-center gap-2">
                        <Cloud class="w-4 h-4 text-muted-foreground" />
                        <span class="text-sm text-muted-foreground">{section.data.hosting_provider}</span>
                      </div>
                    </div>
                  {/if}
                {/if}

                <!-- Performance Metrics Content -->
                {#if section.key === 'performance_metrics'}
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {#if section.data.page_load_time}
                      <div class="p-3 bg-secondary/50 rounded-lg">
                        <div class="flex items-center gap-2 mb-1">
                          <Zap class="w-4 h-4 text-muted-foreground" />
                          <span class="text-sm font-medium">Page Load Time</span>
                        </div>
                        <div class="text-lg font-bold text-foreground">{section.data.page_load_time}</div>
                      </div>
                    {/if}
                    
                    {#if section.data.performance_score}
                      <div class="p-3 bg-secondary/50 rounded-lg">
                        <div class="flex items-center gap-2 mb-1">
                          <Monitor class="w-4 h-4 text-muted-foreground" />
                          <span class="text-sm font-medium">Performance Score</span>
                        </div>
                        <div class="text-lg font-bold text-foreground">{section.data.performance_score}/100</div>
                      </div>
                    {/if}
                    
                    {#if section.data.core_web_vitals}
                      <div class="md:col-span-2">
                        <h4 class="text-sm font-medium text-foreground mb-2">Core Web Vitals</h4>
                        <div class="grid grid-cols-3 gap-2">
                          {#if section.data.core_web_vitals.lcp}
                            <div class="text-center p-2 bg-secondary/30 rounded">
                              <div class="text-xs text-muted-foreground">LCP</div>
                              <div class="text-sm font-medium">{section.data.core_web_vitals.lcp}</div>
                            </div>
                          {/if}
                          {#if section.data.core_web_vitals.fid}
                            <div class="text-center p-2 bg-secondary/30 rounded">
                              <div class="text-xs text-muted-foreground">FID</div>
                              <div class="text-sm font-medium">{section.data.core_web_vitals.fid}</div>
                            </div>
                          {/if}
                          {#if section.data.core_web_vitals.cls}
                            <div class="text-center p-2 bg-secondary/30 rounded">
                              <div class="text-xs text-muted-foreground">CLS</div>
                              <div class="text-sm font-medium">{section.data.core_web_vitals.cls}</div>
                            </div>
                          {/if}
                        </div>
                      </div>
                    {/if}
                  </div>
                  
                  {#if section.data.optimization_suggestions && section.data.optimization_suggestions.length > 0}
                    <div>
                      <h4 class="text-sm font-medium text-foreground mb-2">Optimization Suggestions</h4>
                      <ul class="space-y-1">
                        {#each section.data.optimization_suggestions as suggestion}
                          <li class="text-xs text-muted-foreground">• {suggestion}</li>
                        {/each}
                      </ul>
                    </div>
                  {/if}
                {/if}

                <!-- Security Analysis Content -->
                {#if section.key === 'security_analysis'}
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {#if section.data.ssl_certificate}
                      <div class="p-3 bg-secondary/50 rounded-lg">
                        <div class="flex items-center gap-2 mb-1">
                          <Shield class="w-4 h-4 text-green-600" />
                          <span class="text-sm font-medium">SSL Certificate</span>
                        </div>
                        <div class="text-sm text-muted-foreground">
                          {section.data.ssl_certificate.issuer || 'Valid'}
                        </div>
                        {#if section.data.ssl_certificate.expires}
                          <div class="text-xs text-muted-foreground">
                            Expires: {formatDate(section.data.ssl_certificate.expires)}
                          </div>
                        {/if}
                      </div>
                    {/if}
                    
                    {#if section.data.security_score}
                      <div class="p-3 bg-secondary/50 rounded-lg">
                        <div class="flex items-center gap-2 mb-1">
                          <Shield class="w-4 h-4 text-muted-foreground" />
                          <span class="text-sm font-medium">Security Score</span>
                        </div>
                        <div class="text-lg font-bold text-foreground">{section.data.security_score}/100</div>
                      </div>
                    {/if}
                  </div>
                  
                  {#if section.data.security_headers && section.data.security_headers.length > 0}
                    <div>
                      <h4 class="text-sm font-medium text-foreground mb-2">Security Headers</h4>
                      <div class="flex flex-wrap gap-2">
                        {#each section.data.security_headers as header}
                          <span class="px-2 py-1 bg-green-500/10 text-green-600 text-xs rounded">
                            {header}
                          </span>
                        {/each}
                      </div>
                    </div>
                  {/if}
                  
                  {#if section.data.vulnerabilities && section.data.vulnerabilities.length > 0}
                    <div>
                      <h4 class="text-sm font-medium text-foreground mb-2">Potential Vulnerabilities</h4>
                      <ul class="space-y-1">
                        {#each section.data.vulnerabilities as vulnerability}
                          <li class="text-xs text-red-600">• {vulnerability}</li>
                        {/each}
                      </ul>
                    </div>
                  {/if}
                {/if}

                <!-- Mobile Optimization Content -->
                {#if section.key === 'mobile_optimization'}
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {#if section.data.mobile_friendly !== undefined}
                      <div class="p-3 bg-secondary/50 rounded-lg">
                        <div class="flex items-center gap-2 mb-1">
                          <Smartphone class="w-4 h-4 text-muted-foreground" />
                          <span class="text-sm font-medium">Mobile Friendly</span>
                        </div>
                        <div class="text-sm {section.data.mobile_friendly ? 'text-green-600' : 'text-red-600'}">
                          {section.data.mobile_friendly ? 'Yes' : 'No'}
                        </div>
                      </div>
                    {/if}
                    
                    {#if section.data.responsive_design !== undefined}
                      <div class="p-3 bg-secondary/50 rounded-lg">
                        <div class="flex items-center gap-2 mb-1">
                          <Monitor class="w-4 h-4 text-muted-foreground" />
                          <span class="text-sm font-medium">Responsive Design</span>
                        </div>
                        <div class="text-sm {section.data.responsive_design ? 'text-green-600' : 'text-red-600'}">
                          {section.data.responsive_design ? 'Yes' : 'No'}
                        </div>
                      </div>
                    {/if}
                  </div>
                  
                  {#if section.data.mobile_performance_score}
                    <div>
                      <h4 class="text-sm font-medium text-foreground mb-2">Mobile Performance Score</h4>
                      <div class="text-lg font-bold text-foreground">{section.data.mobile_performance_score}/100</div>
                    </div>
                  {/if}
                  
                  {#if section.data.mobile_issues && section.data.mobile_issues.length > 0}
                    <div>
                      <h4 class="text-sm font-medium text-foreground mb-2">Mobile Issues</h4>
                      <ul class="space-y-1">
                        {#each section.data.mobile_issues as issue}
                          <li class="text-xs text-muted-foreground">• {issue}</li>
                        {/each}
                      </ul>
                    </div>
                  {/if}
                {/if}

                <!-- Data Attribution -->
                <div class="flex items-center justify-between pt-2 border-t border-border text-xs text-muted-foreground">
                  <div class="flex items-center gap-1">
                    <Code2 class="w-3 h-3" />
                    <span>{section.data.source || 'Firecrawl Analysis'}</span>
                  </div>
                  {#if section.data.last_updated}
                    <div class="flex items-center gap-1">
                      <Clock class="w-3 h-3" />
                      <span>Updated {formatDate(section.data.last_updated)}</span>
                    </div>
                  {/if}
                </div>
              </div>
            </div>
          {/if}
        </div>
      {/each}
    </div>
  {:else}
    <!-- No Technical Data -->
    <div class="text-center py-8 text-muted-foreground">
      <Code2 class="w-12 h-12 mx-auto mb-3 opacity-50" />
      <h3 class="font-medium mb-1">No Technical Data</h3>
      <p class="text-sm">Technical analysis data will appear here when available.</p>
    </div>
  {/if}
</div>
