<script lang="ts">
  import { 
    Target, 
    TrendingUp, 
    Users, 
    MessageSquare,
    CheckCircle2,
    Copy,
    ExternalLink,
    ArrowRight,
    Lightbulb,
    Zap
  } from 'lucide-svelte'
  import { fade, fly } from 'svelte/transition'
  import { quintOut } from 'svelte/easing'

  export let actionableInsights: any = {}

  // Get confidence color based on score
  function getConfidenceColor(score: number): string {
    if (score >= 0.9) return 'text-green-600'
    if (score >= 0.7) return 'text-yellow-600'
    return 'text-red-600'
  }

  // Format confidence score as percentage
  function formatConfidence(score: number): string {
    return `${Math.round(score * 100)}%`
  }

  // Get priority color
  function getPriorityColor(priority: string): string {
    switch (priority?.toLowerCase()) {
      case 'high': return 'bg-red-100 text-red-800 border-red-200'
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'low': return 'bg-green-100 text-green-800 border-green-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  // Get impact color
  function getImpactColor(impact: string): string {
    switch (impact?.toLowerCase()) {
      case 'high': return 'bg-purple-100 text-purple-800 border-purple-200'
      case 'medium': return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'low': return 'bg-gray-100 text-gray-800 border-gray-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  // Copy insight to clipboard
  async function copyInsight(text: string) {
    try {
      await navigator.clipboard.writeText(text)
      // Could add a toast notification here
    } catch (err) {
      console.error('Failed to copy insight:', err)
    }
  }

  // Export insights as text
  function exportInsights() {
    const insights = []
    
    if (actionableInsights.marketing_opportunities) {
      insights.push('MARKETING OPPORTUNITIES:')
      actionableInsights.marketing_opportunities.forEach((opp, i) => {
        insights.push(`${i + 1}. ${opp.insight}`)
        if (opp.recommended_action) {
          insights.push(`   Action: ${opp.recommended_action}`)
        }
        insights.push('')
      })
    }
    
    if (actionableInsights.competitive_advantages) {
      insights.push('COMPETITIVE ADVANTAGES:')
      actionableInsights.competitive_advantages.forEach((adv, i) => {
        insights.push(`${i + 1}. ${adv.advantage}`)
      })
      insights.push('')
    }
    
    if (actionableInsights.contact_strategy) {
      insights.push('CONTACT STRATEGY:')
      if (actionableInsights.contact_strategy.approach_recommendations) {
        insights.push(actionableInsights.contact_strategy.approach_recommendations)
      }
    }
    
    const exportText = insights.join('\n')
    const blob = new Blob([exportText], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `actionable-insights-${new Date().toISOString().split('T')[0]}.txt`
    a.click()
    URL.revokeObjectURL(url)
  }
</script>

<div 
  class="card-professional p-6 space-y-6"
  in:fade={{ duration: 400, easing: quintOut }}
>
  <!-- Header -->
  <div class="flex items-center justify-between">
    <div class="flex items-center gap-3">
      <div class="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
        <Target class="w-5 h-5 text-primary" />
      </div>
      <div>
        <h2 class="text-lg font-bold text-foreground">Actionable Insights</h2>
        <p class="text-sm text-muted-foreground">
          Strategic recommendations and next steps
        </p>
      </div>
    </div>
    
    <button
      on:click={exportInsights}
      class="inline-flex items-center gap-2 px-3 py-1.5 bg-primary text-primary-foreground text-sm rounded-md hover:bg-primary/90 transition-colors"
    >
      <Copy class="w-3 h-3" />
      Export
    </button>
  </div>

  <!-- Marketing Opportunities -->
  {#if actionableInsights.marketing_opportunities && actionableInsights.marketing_opportunities.length > 0}
    <div class="space-y-4">
      <div class="flex items-center gap-2">
        <Lightbulb class="w-5 h-5 text-primary" />
        <h3 class="text-lg font-semibold text-foreground">Marketing Opportunities</h3>
        <span class="px-2 py-1 bg-primary/10 text-primary text-xs rounded-full">
          {actionableInsights.marketing_opportunities.length}
        </span>
      </div>
      
      <div class="grid gap-4">
        {#each actionableInsights.marketing_opportunities as opportunity, index}
          <div 
            class="border border-border rounded-lg p-4 hover:shadow-md transition-all duration-200"
            in:fly={{ y: 20, duration: 300, delay: index * 100, easing: quintOut }}
          >
            <div class="flex items-start justify-between mb-3">
              <div class="flex-1">
                <div class="flex items-center gap-2 mb-2">
                  <span class="w-6 h-6 bg-primary/10 text-primary text-xs rounded-full flex items-center justify-center font-medium">
                    {index + 1}
                  </span>
                  {#if opportunity.priority}
                    <span class="px-2 py-1 text-xs rounded border {getPriorityColor(opportunity.priority)}">
                      {opportunity.priority} priority
                    </span>
                  {/if}
                  {#if opportunity.confidence_score}
                    <div class="flex items-center gap-1">
                      <CheckCircle2 class="w-3 h-3 {getConfidenceColor(opportunity.confidence_score)}" />
                      <span class="text-xs {getConfidenceColor(opportunity.confidence_score)}">
                        {formatConfidence(opportunity.confidence_score)}
                      </span>
                    </div>
                  {/if}
                </div>
                
                <p class="text-sm font-medium text-foreground mb-2">
                  {opportunity.insight}
                </p>
                
                {#if opportunity.recommended_action}
                  <div class="flex items-start gap-2 p-3 bg-secondary/30 rounded-md">
                    <ArrowRight class="w-4 h-4 text-primary mt-0.5 flex-shrink-0" />
                    <div>
                      <div class="text-xs font-medium text-foreground mb-1">Recommended Action:</div>
                      <p class="text-xs text-muted-foreground">{opportunity.recommended_action}</p>
                    </div>
                  </div>
                {/if}
              </div>
              
              <button
                on:click={() => copyInsight(opportunity.insight + (opportunity.recommended_action ? '\nAction: ' + opportunity.recommended_action : ''))}
                class="p-1 text-muted-foreground hover:text-foreground transition-colors"
                title="Copy opportunity"
              >
                <Copy class="w-4 h-4" />
              </button>
            </div>
          </div>
        {/each}
      </div>
    </div>
  {/if}

  <!-- Competitive Advantages -->
  {#if actionableInsights.competitive_advantages && actionableInsights.competitive_advantages.length > 0}
    <div class="space-y-4 pt-6 border-t border-border">
      <div class="flex items-center gap-2">
        <TrendingUp class="w-5 h-5 text-primary" />
        <h3 class="text-lg font-semibold text-foreground">Competitive Advantages</h3>
        <span class="px-2 py-1 bg-primary/10 text-primary text-xs rounded-full">
          {actionableInsights.competitive_advantages.length}
        </span>
      </div>
      
      <div class="grid gap-3">
        {#each actionableInsights.competitive_advantages as advantage, index}
          <div 
            class="flex items-center justify-between p-3 border border-border rounded-lg hover:bg-secondary/30 transition-colors"
            in:fly={{ y: 20, duration: 300, delay: index * 50, easing: quintOut }}
          >
            <div class="flex items-center gap-3 flex-1">
              <div class="w-6 h-6 bg-green-100 text-green-800 text-xs rounded-full flex items-center justify-center font-medium">
                {index + 1}
              </div>
              <p class="text-sm text-foreground flex-1">{advantage.advantage}</p>
            </div>
            
            <div class="flex items-center gap-2">
              {#if advantage.impact}
                <span class="px-2 py-1 text-xs rounded border {getImpactColor(advantage.impact)}">
                  {advantage.impact} impact
                </span>
              {/if}
              {#if advantage.confidence_score}
                <div class="flex items-center gap-1">
                  <CheckCircle2 class="w-3 h-3 {getConfidenceColor(advantage.confidence_score)}" />
                  <span class="text-xs {getConfidenceColor(advantage.confidence_score)}">
                    {formatConfidence(advantage.confidence_score)}
                  </span>
                </div>
              {/if}
              <button
                on:click={() => copyInsight(advantage.advantage)}
                class="p-1 text-muted-foreground hover:text-foreground transition-colors"
                title="Copy advantage"
              >
                <Copy class="w-3 h-3" />
              </button>
            </div>
          </div>
        {/each}
      </div>
    </div>
  {/if}

  <!-- Contact Strategy -->
  {#if actionableInsights.contact_strategy}
    <div class="space-y-4 pt-6 border-t border-border">
      <div class="flex items-center gap-2">
        <Users class="w-5 h-5 text-primary" />
        <h3 class="text-lg font-semibold text-foreground">Contact Strategy</h3>
      </div>
      
      <div class="border border-border rounded-lg p-4 bg-secondary/20">
        {#if actionableInsights.contact_strategy.primary_contacts && actionableInsights.contact_strategy.primary_contacts.length > 0}
          <div class="mb-4">
            <h4 class="text-sm font-medium text-foreground mb-2">Priority Contacts:</h4>
            <div class="flex flex-wrap gap-2">
              {#each actionableInsights.contact_strategy.primary_contacts as contact}
                <span class="inline-flex items-center gap-1 px-3 py-1 bg-primary/10 text-primary text-sm rounded-full">
                  <Users class="w-3 h-3" />
                  {contact}
                </span>
              {/each}
            </div>
          </div>
        {/if}
        
        {#if actionableInsights.contact_strategy.approach_recommendations}
          <div class="space-y-2">
            <h4 class="text-sm font-medium text-foreground">Recommended Approach:</h4>
            <div class="flex items-start gap-2 p-3 bg-background rounded border">
              <MessageSquare class="w-4 h-4 text-primary mt-0.5 flex-shrink-0" />
              <p class="text-sm text-foreground">{actionableInsights.contact_strategy.approach_recommendations}</p>
            </div>
          </div>
        {/if}
        
        <div class="flex items-center justify-between mt-4 pt-3 border-t border-border">
          {#if actionableInsights.contact_strategy.confidence_score}
            <div class="flex items-center gap-1">
              <CheckCircle2 class="w-3 h-3 {getConfidenceColor(actionableInsights.contact_strategy.confidence_score)}" />
              <span class="text-xs {getConfidenceColor(actionableInsights.contact_strategy.confidence_score)}">
                {formatConfidence(actionableInsights.contact_strategy.confidence_score)} confidence
              </span>
            </div>
          {/if}
          
          <button
            on:click={() => copyInsight(actionableInsights.contact_strategy.approach_recommendations || 'Contact strategy recommendations')}
            class="inline-flex items-center gap-1 px-2 py-1 text-xs text-muted-foreground hover:text-foreground transition-colors"
          >
            <Copy class="w-3 h-3" />
            Copy Strategy
          </button>
        </div>
      </div>
    </div>
  {/if}

  <!-- No Data State -->
  {#if (!actionableInsights.marketing_opportunities || actionableInsights.marketing_opportunities.length === 0) && 
        (!actionableInsights.competitive_advantages || actionableInsights.competitive_advantages.length === 0) && 
        !actionableInsights.contact_strategy}
    <div class="text-center py-8 text-muted-foreground">
      <Zap class="w-12 h-12 mx-auto mb-3 opacity-50" />
      <p class="text-sm">No actionable insights available</p>
      <p class="text-xs mt-1">Run a comprehensive research query to generate strategic recommendations</p>
    </div>
  {/if}
</div>
