<script lang="ts">
  import { 
    CheckCircle2, 
    Loader2, 
    Circle, 
    AlertTriangle, 
    Clock,
    Zap,
    Search,
    Building2,
    Users,
    Globe,
    FileText,
    ChevronDown,
    ChevronRight
  } from "lucide-svelte"
  import { slide } from "svelte/transition"

  export let progressSteps: any[] = []
  export let currentProgress = 0
  export let isLoading = false
  export let showDetails = true

  let expandedSteps = new Set()

  // Enhanced step configuration with icons and descriptions
  const stepConfig = {
    1: { 
      icon: Search, 
      title: "Company Research", 
      description: "Gathering basic company information and profile data"
    },
    2: { 
      icon: Building2, 
      title: "Competitor Discovery", 
      description: "Finding similar companies and market competitors"
    },
    3: { 
      icon: Globe, 
      title: "Web Analysis", 
      description: "Analyzing website content and digital presence"
    },
    4: { 
      icon: FileText, 
      title: "Content Extraction", 
      description: "Extracting detailed content and technical information"
    },
    5: { 
      icon: Users, 
      title: "Contact Discovery", 
      description: "Finding key contacts and decision makers"
    },
    6: { 
      icon: Zap, 
      title: "Final Analysis", 
      description: "Compiling comprehensive research report"
    }
  }

  function getStepStatus(step: any) {
    if (step.status === 'completed') return 'completed'
    if (step.status === 'in_progress' || step.status === 'running') return 'running'
    if (step.status === 'failed' || step.status === 'error') return 'error'
    if (step.id <= currentProgress) return 'running'
    return 'pending'
  }

  function getStatusIcon(status: string) {
    switch (status) {
      case 'completed': return CheckCircle2
      case 'running': return Loader2
      case 'error': return AlertTriangle
      default: return Circle
    }
  }

  function getStatusColor(status: string) {
    switch (status) {
      case 'completed': return 'text-green-600'
      case 'running': return 'text-blue-600'
      case 'error': return 'text-red-600'
      default: return 'text-muted-foreground'
    }
  }

  function getStatusBgColor(status: string) {
    switch (status) {
      case 'completed': return 'bg-green-100 border-green-200'
      case 'running': return 'bg-blue-100 border-blue-200'
      case 'error': return 'bg-red-100 border-red-200'
      default: return 'bg-muted border-border'
    }
  }

  function toggleStepExpansion(stepId: number) {
    if (expandedSteps.has(stepId)) {
      expandedSteps.delete(stepId)
    } else {
      expandedSteps.add(stepId)
    }
    expandedSteps = expandedSteps
  }

  function formatDuration(startTime?: string, endTime?: string) {
    if (!startTime) return ''
    const start = new Date(startTime)
    const end = endTime ? new Date(endTime) : new Date()
    const duration = Math.round((end.getTime() - start.getTime()) / 1000)
    
    if (duration < 60) return `${duration}s`
    if (duration < 3600) return `${Math.round(duration / 60)}m ${duration % 60}s`
    return `${Math.round(duration / 3600)}h ${Math.round((duration % 3600) / 60)}m`
  }

  // Calculate overall progress percentage
  $: overallProgress = progressSteps.length > 0 
    ? (progressSteps.filter(step => getStepStatus(step) === 'completed').length / progressSteps.length) * 100
    : 0
</script>

<div class="bg-card border border-border rounded-lg p-4 space-y-4">
  <!-- Progress Header -->
  <div class="flex items-center justify-between">
    <h3 class="font-semibold text-foreground flex items-center gap-2">
      {#if isLoading}
        <Loader2 class="w-4 h-4 animate-spin text-blue-600" />
      {:else}
        <CheckCircle2 class="w-4 h-4 text-green-600" />
      {/if}
      Research Progress
    </h3>
    <div class="text-sm text-muted-foreground">
      {Math.round(overallProgress)}% Complete
    </div>
  </div>

  <!-- Overall Progress Bar -->
  <div class="w-full bg-muted rounded-full h-2">
    <div 
      class="bg-primary h-2 rounded-full transition-all duration-300 ease-out"
      style="width: {overallProgress}%"
    ></div>
  </div>

  <!-- Step Details -->
  {#if showDetails && progressSteps.length > 0}
    <div class="space-y-2">
      {#each progressSteps as step}
        {@const status = getStepStatus(step)}
        {@const config = stepConfig[step.id] || { icon: Circle, title: `Step ${step.id}`, description: step.description }}
        {@const isExpanded = expandedSteps.has(step.id)}
        
        <div class="border border-border rounded-lg overflow-hidden">
          <!-- Step Header -->
          <button
            on:click={() => toggleStepExpansion(step.id)}
            class="w-full p-3 flex items-center gap-3 hover:bg-muted/50 transition-colors {getStatusBgColor(status)}"
          >
            <!-- Status Icon -->
            <div class="flex-shrink-0">
              <svelte:component 
                this={getStatusIcon(status)} 
                class="w-4 h-4 {getStatusColor(status)} {status === 'running' ? 'animate-spin' : ''}" 
              />
            </div>

            <!-- Step Icon -->
            <div class="flex-shrink-0">
              <svelte:component 
                this={config.icon} 
                class="w-4 h-4 text-muted-foreground" 
              />
            </div>

            <!-- Step Info -->
            <div class="flex-1 text-left">
              <div class="font-medium text-sm text-foreground">
                {config.title}
              </div>
              <div class="text-xs text-muted-foreground">
                {step.description || config.description}
              </div>
            </div>

            <!-- Duration -->
            {#if step.start_time}
              <div class="text-xs text-muted-foreground flex items-center gap-1">
                <Clock class="w-3 h-3" />
                {formatDuration(step.start_time, step.end_time)}
              </div>
            {/if}

            <!-- Expand Icon -->
            <div class="flex-shrink-0">
              {#if isExpanded}
                <ChevronDown class="w-4 h-4 text-muted-foreground" />
              {:else}
                <ChevronRight class="w-4 h-4 text-muted-foreground" />
              {/if}
            </div>
          </button>

          <!-- Expanded Details -->
          {#if isExpanded}
            <div transition:slide={{ duration: 200 }} class="border-t border-border bg-muted/20 p-3">
              <div class="space-y-2 text-xs">
                <!-- Step Status -->
                <div class="flex items-center justify-between">
                  <span class="text-muted-foreground">Status:</span>
                  <span class="font-medium {getStatusColor(status)}">
                    {status.replace('_', ' ').toUpperCase()}
                  </span>
                </div>

                <!-- Error Details -->
                {#if status === 'error' && step.error}
                  <div class="bg-red-50 border border-red-200 rounded p-2">
                    <div class="text-red-800 font-medium mb-1">Error Details:</div>
                    <div class="text-red-700">{step.error}</div>
                  </div>
                {/if}

                <!-- Success Details -->
                {#if status === 'completed' && step.result}
                  <div class="bg-green-50 border border-green-200 rounded p-2">
                    <div class="text-green-800 font-medium mb-1">Results:</div>
                    <div class="text-green-700">
                      {#if typeof step.result === 'object'}
                        {JSON.stringify(step.result, null, 2)}
                      {:else}
                        {step.result}
                      {/if}
                    </div>
                  </div>
                {/if}

                <!-- Timing Details -->
                {#if step.start_time}
                  <div class="flex items-center justify-between">
                    <span class="text-muted-foreground">Started:</span>
                    <span class="font-mono">{new Date(step.start_time).toLocaleTimeString()}</span>
                  </div>
                {/if}
                {#if step.end_time}
                  <div class="flex items-center justify-between">
                    <span class="text-muted-foreground">Completed:</span>
                    <span class="font-mono">{new Date(step.end_time).toLocaleTimeString()}</span>
                  </div>
                {/if}
              </div>
            </div>
          {/if}
        </div>
      {/each}
    </div>
  {/if}

  <!-- Summary Stats -->
  {#if progressSteps.length > 0}
    <div class="flex items-center justify-between text-xs text-muted-foreground pt-2 border-t border-border">
      <div class="flex items-center gap-4">
        <span class="flex items-center gap-1">
          <CheckCircle2 class="w-3 h-3 text-green-600" />
          {progressSteps.filter(step => getStepStatus(step) === 'completed').length} Completed
        </span>
        <span class="flex items-center gap-1">
          <Loader2 class="w-3 h-3 text-blue-600" />
          {progressSteps.filter(step => getStepStatus(step) === 'running').length} Running
        </span>
        <span class="flex items-center gap-1">
          <AlertTriangle class="w-3 h-3 text-red-600" />
          {progressSteps.filter(step => getStepStatus(step) === 'error').length} Failed
        </span>
      </div>
      <div>
        Total: {progressSteps.length} steps
      </div>
    </div>
  {/if}
</div>
