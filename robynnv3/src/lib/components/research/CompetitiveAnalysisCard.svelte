<script lang="ts">
  import { 
    TrendingUp, 
    Building2, 
    Users, 
    DollarSign, 
    ExternalLink, 
    Globe, 
    Linkedin,
    CheckCircle2,
    ChevronRight,
    BarChart3
  } from 'lucide-svelte'
  import { fade, fly } from 'svelte/transition'
  import { quintOut } from 'svelte/easing'

  export let competitors: any[] = []
  export let targetCompany: any = null

  // Get confidence color based on score
  function getConfidenceColor(score: number): string {
    if (score >= 0.9) return 'text-green-600'
    if (score >= 0.7) return 'text-yellow-600'
    return 'text-red-600'
  }

  // Format confidence score as percentage
  function formatConfidence(score: number): string {
    return `${Math.round(score * 100)}%`
  }

  // Format employee count for display
  function formatEmployeeCount(employees: string | number): string {
    if (typeof employees === 'number') {
      return employees.toLocaleString()
    }
    return employees || 'Unknown'
  }

  // Format revenue for display
  function formatRevenue(revenue: string): string {
    if (!revenue || revenue === 'Unknown') return 'Not disclosed'
    return revenue
  }

  // Get company size category
  function getCompanySize(employees: string | number): string {
    if (typeof employees === 'number') {
      if (employees < 50) return 'Startup'
      if (employees < 200) return 'Small'
      if (employees < 1000) return 'Medium'
      return 'Enterprise'
    }
    
    const empStr = employees?.toString().toLowerCase() || ''
    if (empStr.includes('1-10') || empStr.includes('11-50')) return 'Startup'
    if (empStr.includes('51-200')) return 'Small'
    if (empStr.includes('201-1000')) return 'Medium'
    return 'Enterprise'
  }

  // Get size color
  function getSizeColor(size: string): string {
    switch (size) {
      case 'Startup': return 'bg-blue-100 text-blue-800'
      case 'Small': return 'bg-green-100 text-green-800'
      case 'Medium': return 'bg-yellow-100 text-yellow-800'
      case 'Enterprise': return 'bg-purple-100 text-purple-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  let expandedCompetitor: string | null = null

  function toggleExpanded(competitorId: string) {
    expandedCompetitor = expandedCompetitor === competitorId ? null : competitorId
  }
</script>

<div 
  class="card-professional p-6 space-y-6"
  in:fade={{ duration: 400, easing: quintOut }}
>
  <!-- Header -->
  <div class="flex items-center justify-between">
    <div class="flex items-center gap-3">
      <div class="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
        <TrendingUp class="w-5 h-5 text-primary" />
      </div>
      <div>
        <h2 class="text-lg font-bold text-foreground">Competitive Landscape</h2>
        <p class="text-sm text-muted-foreground">
          {competitors.length} competitors identified
        </p>
      </div>
    </div>
    
    <div class="flex items-center gap-1 px-2 py-1 bg-secondary rounded-md">
      <BarChart3 class="w-3 h-3 text-muted-foreground" />
      <span class="text-xs font-medium">Market Analysis</span>
    </div>
  </div>

  <!-- Competitive Overview Stats -->
  {#if competitors.length > 0}
    <div class="grid grid-cols-2 md:grid-cols-4 gap-4 p-4 bg-secondary/30 rounded-lg">
      <div class="text-center">
        <div class="text-lg font-bold text-foreground">{competitors.length}</div>
        <div class="text-xs text-muted-foreground">Competitors</div>
      </div>
      <div class="text-center">
        <div class="text-lg font-bold text-foreground">
          {competitors.filter(c => getCompanySize(c.employees) === 'Enterprise').length}
        </div>
        <div class="text-xs text-muted-foreground">Enterprise</div>
      </div>
      <div class="text-center">
        <div class="text-lg font-bold text-foreground">
          {competitors.filter(c => c.industry === targetCompany?.industry).length}
        </div>
        <div class="text-xs text-muted-foreground">Same Industry</div>
      </div>
      <div class="text-center">
        <div class="text-lg font-bold text-foreground">
          {Math.round(competitors.reduce((acc, c) => acc + (c.confidence_score || 0.5), 0) / competitors.length * 100)}%
        </div>
        <div class="text-xs text-muted-foreground">Avg Confidence</div>
      </div>
    </div>
  {/if}

  <!-- Competitors List -->
  <div class="space-y-3">
    {#each competitors as competitor, index (competitor.domain || competitor.name)}
      <div 
        class="border border-border rounded-lg overflow-hidden transition-all duration-200 hover:shadow-md"
        in:fly={{ y: 20, duration: 300, delay: index * 100, easing: quintOut }}
      >
        <!-- Competitor Header -->
        <button
          on:click={() => toggleExpanded(competitor.domain || competitor.name)}
          class="w-full p-4 text-left hover:bg-secondary/50 transition-colors"
        >
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-3 flex-1">
              <div class="w-8 h-8 bg-primary/10 rounded-md flex items-center justify-center flex-shrink-0">
                <Building2 class="w-4 h-4 text-primary" />
              </div>
              
              <div class="flex-1 min-w-0">
                <div class="flex items-center gap-2 mb-1">
                  <h3 class="font-semibold text-foreground truncate">{competitor.name}</h3>
                  {#if competitor.confidence_score}
                    <div class="flex items-center gap-1">
                      <CheckCircle2 class="w-3 h-3 {getConfidenceColor(competitor.confidence_score)}" />
                      <span class="text-xs {getConfidenceColor(competitor.confidence_score)}">
                        {formatConfidence(competitor.confidence_score)}
                      </span>
                    </div>
                  {/if}
                </div>
                
                <div class="flex items-center gap-3 text-xs text-muted-foreground">
                  <span>{competitor.domain}</span>
                  {#if competitor.industry}
                    <span>•</span>
                    <span>{competitor.industry}</span>
                  {/if}
                  {#if competitor.employees}
                    <span>•</span>
                    <span class="px-1.5 py-0.5 rounded text-xs {getSizeColor(getCompanySize(competitor.employees))}">
                      {getCompanySize(competitor.employees)}
                    </span>
                  {/if}
                </div>
              </div>
            </div>
            
            <div class="flex items-center gap-2">
              {#if competitor.website_url}
                <a 
                  href={competitor.website_url} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  class="p-1 text-muted-foreground hover:text-primary transition-colors"
                  on:click|stopPropagation
                >
                  <Globe class="w-4 h-4" />
                </a>
              {/if}
              
              {#if competitor.linkedin_url}
                <a 
                  href={competitor.linkedin_url} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  class="p-1 text-muted-foreground hover:text-primary transition-colors"
                  on:click|stopPropagation
                >
                  <Linkedin class="w-4 h-4" />
                </a>
              {/if}
              
              <ChevronRight 
                class="w-4 h-4 text-muted-foreground transition-transform duration-200 {expandedCompetitor === (competitor.domain || competitor.name) ? 'rotate-90' : ''}" 
              />
            </div>
          </div>
        </button>

        <!-- Expanded Details -->
        {#if expandedCompetitor === (competitor.domain || competitor.name)}
          <div 
            class="px-4 pb-4 border-t border-border bg-secondary/20"
            in:fly={{ y: -10, duration: 200 }}
          >
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
              <!-- Company Details -->
              <div class="space-y-2">
                <h4 class="text-sm font-medium text-foreground">Company Details</h4>
                <div class="space-y-1 text-xs">
                  {#if competitor.employees}
                    <div class="flex items-center gap-2">
                      <Users class="w-3 h-3 text-muted-foreground" />
                      <span>{formatEmployeeCount(competitor.employees)} employees</span>
                    </div>
                  {/if}
                  {#if competitor.revenue}
                    <div class="flex items-center gap-2">
                      <DollarSign class="w-3 h-3 text-muted-foreground" />
                      <span>{formatRevenue(competitor.revenue)}</span>
                    </div>
                  {/if}
                  {#if competitor.location}
                    <div class="text-muted-foreground">{competitor.location}</div>
                  {/if}
                </div>
              </div>

              <!-- Description -->
              <div class="md:col-span-2 space-y-2">
                <h4 class="text-sm font-medium text-foreground">Description</h4>
                <p class="text-xs text-muted-foreground leading-relaxed">
                  {competitor.description || 'No description available'}
                </p>
                {#if competitor.similarity}
                  <div class="mt-2">
                    <span class="text-xs font-medium text-foreground">Similarity: </span>
                    <span class="text-xs text-muted-foreground">{competitor.similarity}</span>
                  </div>
                {/if}
              </div>
            </div>

            <!-- Technologies -->
            {#if competitor.technologies && competitor.technologies.length > 0}
              <div class="mt-4 space-y-2">
                <h4 class="text-sm font-medium text-foreground">Technologies</h4>
                <div class="flex flex-wrap gap-1">
                  {#each competitor.technologies.slice(0, 6) as tech}
                    <span class="inline-flex items-center px-2 py-1 bg-primary/10 text-primary text-xs rounded-md">
                      {tech}
                    </span>
                  {/each}
                  {#if competitor.technologies.length > 6}
                    <span class="inline-flex items-center px-2 py-1 bg-secondary text-secondary-foreground text-xs rounded-md">
                      +{competitor.technologies.length - 6} more
                    </span>
                  {/if}
                </div>
              </div>
            {/if}

            <!-- Key Contacts -->
            {#if competitor.contacts && competitor.contacts.length > 0}
              <div class="mt-4 space-y-2">
                <h4 class="text-sm font-medium text-foreground">Key Contacts ({competitor.contacts.length})</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-2">
                  {#each competitor.contacts.slice(0, 4) as contact}
                    <div class="flex items-center justify-between p-2 bg-background rounded border">
                      <div>
                        <div class="text-xs font-medium">{contact.name}</div>
                        <div class="text-xs text-muted-foreground">{contact.title}</div>
                      </div>
                      {#if contact.linkedin_url}
                        <a 
                          href={contact.linkedin_url} 
                          target="_blank" 
                          rel="noopener noreferrer"
                          class="text-primary hover:text-primary/80 transition-colors"
                        >
                          <Linkedin class="w-3 h-3" />
                        </a>
                      {/if}
                    </div>
                  {/each}
                </div>
              </div>
            {/if}
          </div>
        {/if}
      </div>
    {/each}
  </div>

  {#if competitors.length === 0}
    <div class="text-center py-8 text-muted-foreground">
      <TrendingUp class="w-12 h-12 mx-auto mb-3 opacity-50" />
      <p class="text-sm">No competitors identified</p>
      <p class="text-xs mt-1">Try refining your search criteria</p>
    </div>
  {/if}
</div>
