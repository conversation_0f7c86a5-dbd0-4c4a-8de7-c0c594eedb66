<script lang="ts">
  import { page } from "$app/stores"
  import { writable } from "svelte/store"
  import { onMount, tick } from "svelte"
  import {
    Building2,
    Zap,
    ChevronRight,
    Download,
    Clock,
    User,
    Bot,
    ChartBar,
    Eye,
    MessageSquare,
    CheckCircle2,
    Loader2,
    Circle,
    Search,
    Info,
  } from "lucide-svelte"
  import SkeletonLoader from "./SkeletonLoader.svelte"
  import ErrorBoundary from "./ErrorBoundary.svelte"
  import CompanyResultCard from "./CompanyResultCard.svelte"
  import ContactCard from "./ContactCard.svelte"
  import EnhancedProgressIndicator from "./EnhancedProgressIndicator.svelte"
  import {
    parseResearchResult,
    isStructuredResearchData,
    extractResearchSummary,
    getToolStatus,
    type ParsedResearchResult
  } from "$lib/utils/research-result-parser"

  // Props for messages and state
  export let messages: any[] = []
  export let isLoading = false
  export let progressSteps: any[] = []
  export let currentProgress = 0
  export let leftSidebarCollapsed = false
  export let rightSidebarCollapsed = false

  // Create a local writable store for messages to enable updates
  const messagesStore = writable(messages)

  // Update the store when messages prop changes
  $: messagesStore.set(messages)

  // Input and state management  
  export let input = ""
  let outputFormat = "comprehensive"
  let searchQuery = ""
  let showSearch = false
  let isInitialLoad = true
  let retryCount = 0
  let chatContainer: HTMLElement
  let inputElement: HTMLTextAreaElement

  // Output format options
  const outputFormats = [
    {
      value: "comprehensive",
      label: "Comprehensive",
      description: "Full detailed analysis",
    },
    {
      value: "executive",
      label: "Executive Summary",
      description: "High-level overview",
    },
    {
      value: "slide-ready",
      label: "Slide-ready",
      description: "Sections + headers for export",
    },
    {
      value: "battlecard",
      label: "Competitive Battlecard",
      description: "Strategic comparison format",
    },
  ]

  // Placeholder examples for animated cycling
  const placeholderExamples = [
    "Research Stripe's competitive positioning and market strategy...",
    "Analyze OpenAI's business model and recent developments...",
    "Compare Notion vs. Obsidian feature sets and pricing...",
    "Investigate Figma's growth strategy and market expansion...",
    "Study Shopify's competitive advantages in e-commerce...",
  ]

  let currentPlaceholder = placeholderExamples[0]
  let placeholderIndex = 0

  // Use actual messages if provided, otherwise show empty state
  $: displayMessages = $messagesStore.length > 0 ? $messagesStore : []

  // Auto-resize textarea when input changes
  $: if (inputElement && input) {
    // Reset height to auto to get the correct scrollHeight
    inputElement.style.height = 'auto'
    // Set height to scrollHeight, but respect max-height
    const maxHeight = 200
    const newHeight = Math.min(inputElement.scrollHeight, maxHeight)
    inputElement.style.height = newHeight + 'px'
  }

  function generateId(): string {
    return Math.random().toString(36).substring(2, 11)
  }

  async function sendMessage() {
    if (!input.trim() || isLoading) return

    // Add output format context to the message
    let formatPrefix = ""
    if (outputFormat === "executive") {
      formatPrefix =
        "[Executive Summary Format] Provide concise bullet points, key insights, and executive-level highlights. Focus on high-level strategic information that executives need to know quickly. Use clear sections and numbered citations. "
    } else if (outputFormat === "slide-ready") {
      formatPrefix =
        "[Slide-ready Format] Structure with clear sections and headers suitable for presentation export. Use numbered sections, clear headings, and bullet points that can be easily converted to slides. Include numbered citations for all claims. "
    } else if (outputFormat === "battlecard") {
      formatPrefix =
        "[Competitive Battlecard Format] Focus on strategic comparison and competitive positioning. Emphasize competitive advantages, market differentiation, and head-to-head comparisons. Include numbered citations and competitive analysis. "
    }

    const userMessage = formatPrefix + input.trim()
    input = ""
    isLoading = true

    // Initialize progress steps
    progressSteps = [
      {
        id: 1,
        title: "Initial Analysis",
        description: "Analyzing research request...",
        status: "pending",
      },
      {
        id: 2,
        title: "Web Search",
        description: "Conducting comprehensive search...",
        status: "pending",
      },
      {
        id: 3,
        title: "Financial Analysis",
        description: "Gathering financial data...",
        status: "pending",
      },
      {
        id: 4,
        title: "Market Research",
        description: "Analyzing market position...",
        status: "pending",
      },
      {
        id: 5,
        title: "Report Generation",
        description: "Creating research report...",
        status: "pending",
      },
    ]
    currentProgress = 0

    // Add user message to chat
    const newUserMessage = {
      id: generateId(),
      role: "user",
      content: userMessage,
      timestamp: new Date(),
    }
    messagesStore.update((msgs) => [...msgs, newUserMessage])
    messages = [...messages, newUserMessage]

    try {
      // Use streaming for progress updates
      const response = await fetch(
        `/dashboard/${$page.params.envSlug}/researcher?stream=true`,
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ message: userMessage }),
        },
      )

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const reader = response.body?.getReader()
      const decoder = new TextDecoder()

      if (reader) {
        while (true) {
          const { done, value } = await reader.read()
          if (done) break

          const chunk = decoder.decode(value)
          const lines = chunk.split("\n")

          for (const line of lines) {
            if (line.startsWith("data: ")) {
              try {
                const data = JSON.parse(line.slice(6))

                if (data.type === "final_response") {
                  // Add assistant response to chat
                  const assistantMessage = {
                    id: generateId(),
                    role: "assistant",
                    content: data.response,
                    timestamp: new Date(),
                    isReport: true,
                  }
                  messagesStore.update((msgs) => [...msgs, assistantMessage])
                  messages = [...messages, assistantMessage]
                } else if (data.step) {
                  // Update progress
                  currentProgress = data.progress
                  progressSteps = progressSteps.map((step) => {
                    if (step.id === data.step) {
                      return {
                        ...step,
                        status: data.status,
                        description: data.action,
                      }
                    } else if (step.id < data.step) {
                      return { ...step, status: "completed" }
                    }
                    return step
                  })
                }
              } catch (e) {
                console.error("Error parsing SSE data:", e)
              }
            }
          }
        }
      }
    } catch (error) {
      console.error("Error sending message:", error)
      const errorMessage = {
        id: generateId(),
        role: "assistant",
        content:
          "I apologize, but Compass encountered an error while processing your request. Please try again.",
        timestamp: new Date(),
      }
      messagesStore.update((msgs) => [...msgs, errorMessage])
      messages = [...messages, errorMessage]
    } finally {
      isLoading = false
      // Reset progress
      progressSteps = []
      currentProgress = 0
    }
  }

  function handleKeyDown(event: KeyboardEvent) {
    if (event.key === "Enter" && !event.shiftKey) {
      event.preventDefault()
      sendMessage()
    }
  }

  function handleTextareaResize(event: Event) {
    const textarea = event.target as HTMLTextAreaElement
    if (textarea) {
      // Reset height to auto to get the correct scrollHeight
      textarea.style.height = 'auto'
      // Set height to scrollHeight, but respect max-height
      const maxHeight = 200 // matches max-height in CSS
      const newHeight = Math.min(textarea.scrollHeight, maxHeight)
      textarea.style.height = newHeight + 'px'
    }
  }

  function handleGlobalKeyDown(event: KeyboardEvent) {
    // Ctrl/Cmd + K for search
    if ((event.ctrlKey || event.metaKey) && event.key === "k") {
      event.preventDefault()
      showSearch = !showSearch
      if (showSearch) {
        tick().then(() => {
          const searchInput = document.querySelector(
            "#search-input",
          ) as HTMLInputElement
          searchInput?.focus()
        })
      }
    }

    // Escape to close search or cancel loading
    if (event.key === "Escape") {
      if (showSearch) {
        showSearch = false
        searchQuery = ""
      } else if (isLoading) {
        // Cancel current request (would need API support)
        console.log("Cancel request requested")
      }
    }

    // Ctrl/Cmd + Enter to send message
    if (
      (event.ctrlKey || event.metaKey) &&
      event.key === "Enter" &&
      !isLoading
    ) {
      sendMessage()
    }

    // Focus input with '/' key
    if (
      event.key === "/" &&
      !showSearch &&
      document.activeElement?.tagName !== "INPUT" &&
      document.activeElement?.tagName !== "TEXTAREA"
    ) {
      event.preventDefault()
      inputElement?.focus()
    }
  }

  function scrollToBottom() {
    if (chatContainer) {
      chatContainer.scrollTop = chatContainer.scrollHeight
    }
  }

  function retryLastMessage() {
    retryCount++
    sendMessage()
  }

  function searchMessages(query: string) {
    if (!query.trim()) return displayMessages

    const lowercaseQuery = query.toLowerCase()
    return displayMessages.filter((message) =>
      message.content.toLowerCase().includes(lowercaseQuery),
    )
  }

  $: filteredMessages = searchQuery
    ? searchMessages(searchQuery)
    : displayMessages

  function extractInsights(content: string): {
    summary: string
    insights: string[]
    badges: string[]
  } {
    // Simple insight extraction - in a real app this would be more sophisticated
    const lines = content.split("\n").filter((line) => line.trim())
    const summary = lines.slice(0, 2).join(" ").substring(0, 200) + "..."

    const insights = lines
      .filter(
        (line) =>
          line.includes("key") ||
          line.includes("important") ||
          line.includes("significant"),
      )
      .slice(0, 3)

    const badges = []
    if (content.includes("growth") || content.includes("increase"))
      badges.push("↑ Trending")
    if (content.includes("insight") || content.includes("analysis"))
      badges.push("💡 Insight")
    if (content.includes("challenge") || content.includes("weakness"))
      badges.push("⚠ Weakness")

    return { summary, insights, badges }
  }

  function downloadAsMarkdown(message: any) {
    const companyName = extractCompanyName(message.content)
    const timestamp = message.timestamp.toISOString().split("T")[0]
    const filename = `${companyName || "research-report"}-${timestamp}.md`

    const markdownContent = `# Marketing Research Report by Compass
**Generated on:** ${message.timestamp.toLocaleDateString()}
**Time:** ${message.timestamp.toLocaleTimeString()}
**Format:** ${outputFormat.charAt(0).toUpperCase() + outputFormat.slice(1)}

---

${message.content}

---

*Report generated by Compass - Your AI Market Researcher*
`

    const blob = new Blob([markdownContent], { type: "text/markdown" })
    const url = URL.createObjectURL(blob)
    const a = document.createElement("a")
    a.href = url
    a.download = filename
    a.click()
    URL.revokeObjectURL(url)
  }

  function extractCompanyName(content: string): string {
    // Simple extraction - looks for company name in the first line or headers
    const lines = content.split("\n")
    for (const line of lines.slice(0, 5)) {
      if (line.includes("Company:") || line.includes("Company Name:")) {
        return (
          line
            .split(":")[1]
            ?.trim()
            .replace(/[^a-zA-Z0-9-]/g, "") || ""
        )
      }
      if (
        line.startsWith("# ") &&
        !line.includes("Research") &&
        !line.includes("Report")
      ) {
        return (
          line
            .replace("# ", "")
            .trim()
            .replace(/[^a-zA-Z0-9-]/g, "") || ""
        )
      }
    }
    return ""
  }

  // Simple formatting for summary (no source extraction)
  function formatSummary(content: string): string {
    let formatted = content
      // Basic bold text
      .replace(/\*\*(.*?)\*\*/g, '<strong class="font-bold font-serif" style="color: var(--foreground);">$1</strong>')
      // Basic italic text
      .replace(/\*(.*?)\*/g, '<em class="italic font-serif" style="color: var(--muted-foreground);">$1</em>')
      // Remove any citations
      .replace(/\[\d+\]/g, '')
      // Line breaks
      .replace(/\n/g, ' ')

    return `<p class="mb-2 font-serif" style="color: var(--muted-foreground); line-height: 1.7;">${formatted}</p>`
  }

  // Full formatting for main content (with source extraction)  
  function formatContent(content: string): string {
    // Enhanced markdown to HTML conversion with proper citation system
    const citations: Array<{number: string, source: string, url: string}> = []
    
    // Extract sources from the content first
    const sourceMatches = content.match(/^[\s]*-[\s]*([^-\n]*?)[\s]*-[\s]*([^-\n]*?)[\s]*-[\s]*([^-\n]*?)$/gim) || []
    const sources: Array<{title: string, source: string, date: string, url: string}> = []
    
    sourceMatches.forEach(match => {
      const parts = match.match(/^[\s]*-[\s]*([^-\n]*?)[\s]*-[\s]*([^-\n]*?)[\s]*-[\s]*([^-\n]*?)$/i)
      if (parts) {
        const [, title, source, date] = parts
        let url = ''
        
        if (source.toLowerCase().includes('simply wall st')) {
          url = 'https://simplywall.st'
        } else if (source.toLowerCase().includes('yahoo finance')) {
          url = 'https://finance.yahoo.com'
        } else if (source.toLowerCase().includes('linkedin')) {
          url = 'https://linkedin.com'
        } else if (source.toLowerCase().includes('nasdaq')) {
          url = 'https://nasdaq.com'
        } else if (source.toLowerCase().includes('investing.com')) {
          url = 'https://investing.com'
        } else if (source.toLowerCase().includes('business wire')) {
          url = 'https://businesswire.com'
        } else if (source.toLowerCase().includes('fortune')) {
          url = 'https://fortune.com'
        } else if (source.toLowerCase().includes('pitchbook')) {
          url = 'https://pitchbook.com'
        } else if (source.toLowerCase().includes('directorstalk')) {
          url = 'https://directorstalk.com'
        }
        
        sources.push({
          title: title.trim(),
          source: source.trim(),
          date: date.trim(),
          url
        })
      }
    })

    // Remove original sources section BEFORE markdown processing
    let cleanedContent = content.replace(/^[\s]*-[\s]*([^-\n]*?)[\s]*-[\s]*([^-\n]*?)[\s]*-[\s]*([^-\n]*?)$/gim, '')

    let formatted = cleanedContent
      // Headers with consistent serif font and spacing
      .replace(
        /^# (.*$)/gim,
        '<h1 class="text-3xl font-bold font-serif mb-3 mt-4 first:mt-0" style="color: var(--foreground); border-bottom: 2px solid var(--border); padding-bottom: 0.5rem;">$1</h1>',
      )
      .replace(
        /^## (.*$)/gim,
        '<h2 class="text-2xl font-bold font-serif mb-2 mt-4" style="color: var(--foreground);">$1</h2>',
      )
      .replace(
        /^### (.*$)/gim,
        '<h3 class="text-xl font-semibold font-serif mb-2 mt-3" style="color: var(--foreground);">$1</h3>',
      )
      .replace(
        /^#### (.*$)/gim,
        '<h4 class="text-lg font-semibold font-serif mb-1 mt-2" style="color: var(--foreground);">$1</h4>',
      )

      // Bold text with consistent styling
      .replace(
        /\*\*(.*?)\*\*/g,
        '<strong class="font-bold" style="color: var(--foreground); font-family: \'Merriweather\', Georgia, serif;">$1</strong>',
      )

      // Italic text with consistent styling
      .replace(
        /\*(.*?)\*/g,
        '<em class="italic" style="color: var(--muted-foreground); font-family: \'Merriweather\', Georgia, serif;">$1</em>',
      )

      // Code blocks
      .replace(
        /```([\s\S]*?)```/g,
        '<pre class="bg-muted p-3 border border-border my-2 overflow-x-auto" style="border-radius: var(--radius);"><code class="text-sm font-mono" style="color: var(--foreground);">$1</code></pre>',
      )

      // Inline code
      .replace(
        /`([^`]+)`/g,
        '<code class="bg-muted px-2 py-1 text-sm font-mono" style="color: var(--foreground); border-radius: var(--radius);">$1</code>',
      )

      // Lists with consistent spacing - use div instead of li to remove bullets
      .replace(
        /^[\s]*[-*+] (.+)$/gim,
        '<div class="mb-0" style="color: var(--muted-foreground); font-family: \'Merriweather\', Georgia, serif; line-height: 1.7; margin-bottom: 2px;">- $1</div>',
      )
      // Numbered lists - but avoid matching citation references in "Sources and References" section
      .replace(
        /^[\s]*(\d+)\.\s+(.+)$/gim,
        '<div class="mb-0" style="color: var(--muted-foreground); font-family: \'Merriweather\', Georgia, serif; line-height: 1.7; margin-bottom: 2px;">- $2</div>',
      )

      // Blockquotes with consistent styling
      .replace(
        /^> (.+)$/gim,
        '<blockquote class="border-l-4 border-primary pl-4 italic my-2" style="color: var(--muted-foreground); font-family: \'Merriweather\', Georgia, serif; line-height: 1.7;">$1</blockquote>',
      )

      // Citations - Handle [1], [2], [3] etc. and collect them
      .replace(
        /\[(\d+)\]/g,
        (match, number) => {
          const citationNum = parseInt(number)
          if (citationNum <= sources.length && !citations.find(c => c.number === number)) {
            const sourceItem = sources[citationNum - 1]
            citations.push({
              number,
              source: `${sourceItem.title} - ${sourceItem.source} - ${sourceItem.date}`,
              url: sourceItem.url
            })
          }
          return `<sup class="citation-number bg-primary text-primary-foreground px-1 py-0.5 text-xs font-bold ml-1 cursor-pointer hover:opacity-70" onclick="document.getElementById('citation-${number}')?.scrollIntoView({behavior: 'smooth'})" title="Click to go to source ${number}" style="border-radius: var(--radius);">[${ number}]</sup>`
        }
      )

      // Links
      .replace(
        /\[([^\]]+)\]\(([^)]+)\)/g,
        '<a href="$2" class="text-primary underline hover:opacity-70" target="_blank" rel="noopener noreferrer" style="font-family: \'Merriweather\', Georgia, serif;">$1</a>',
      )

      // Line breaks and paragraphs with consistent spacing and line height
      .replace(
        /\n\n/g,
        '</p><p class="mb-2" style="color: var(--muted-foreground); font-family: \'Merriweather\', Georgia, serif; line-height: 1.7;">',
      )
      .replace(/\n/g, "<br>")

    // Wrap content in paragraph if it doesn't start with a block element
    if (
      !formatted.startsWith("<h") &&
      !formatted.startsWith("<p") &&
      !formatted.startsWith("<ul") &&
      !formatted.startsWith("<ol") &&
      !formatted.startsWith("<blockquote")
    ) {
      formatted =
        '<p class="mb-2" style="color: var(--muted-foreground); font-family: \'Merriweather\', Georgia, serif; line-height: 1.7;">' +
        formatted +
        "</p>"
    }

    // Sources already removed before markdown processing

    // Create proper numbered references section if citations exist
    if (citations.length > 0) {
      formatted += '<div class="mt-8 pt-4 border-t border-border">'
      formatted += '<h3 class="text-lg font-semibold mb-3" style="color: var(--foreground); font-family: \'Merriweather\', Georgia, serif;">Sources and References</h3>'
      formatted += '<div class="space-y-2">'
      
      citations.forEach(citation => {
        const linkElement = citation.url 
          ? `<a href="${citation.url}" target="_blank" rel="noopener noreferrer" class="text-primary hover:underline">${citation.source}</a>`
          : citation.source
          
        formatted += `<div id="citation-${citation.number}" class="text-sm" style="color: var(--muted-foreground); font-family: 'Merriweather', Georgia, serif; line-height: 1.7;">
          <span class="font-bold">[${citation.number}]</span> ${linkElement}
        </div>`
      })
      
      formatted += '</div></div>'
    }

    return formatted
  }

  // Animated placeholder effect and setup
  onMount(() => {
    currentPlaceholder = placeholderExamples[0]
    isInitialLoad = false

    const interval = setInterval(() => {
      placeholderIndex = (placeholderIndex + 1) % placeholderExamples.length
      currentPlaceholder = placeholderExamples[placeholderIndex]
    }, 4000)

    // Add global keyboard listeners
    document.addEventListener("keydown", handleGlobalKeyDown)

    // Auto-scroll to bottom when new messages arrive
    const unsubscribe = messagesStore.subscribe(() => {
      tick().then(scrollToBottom)
    })

    return () => {
      clearInterval(interval)
      document.removeEventListener("keydown", handleGlobalKeyDown)
      unsubscribe()
    }
  })
</script>

<!-- Main Content Area -->
<main class="flex-1 flex flex-col bg-background">
  <!-- Header -->
  <header
    class="flex items-center justify-between p-6 border-b border-border bg-background"
  >
    <div class="flex items-center">
      <!-- Breadcrumb Navigation -->
      <nav
        class="flex items-center space-x-2 text-sm text-muted-foreground mb-2"
      >
        <a
          href="/dashboard/{$page.params.envSlug}"
          class="hover:text-foreground transition-colors"
        >
          Dashboard
        </a>
        <ChevronRight class="w-4 h-4" />
        <span class="text-foreground font-medium">Research Agent</span>
      </nav>
    </div>

    <!-- Header Actions -->
    <div class="flex items-center space-x-2">
      <!-- Search Toggle -->
      <button
        on:click={() => (showSearch = !showSearch)}
        class="p-2 hover:bg-accent transition-colors"
        title="Search messages (Ctrl+K)"
        style="border-radius: var(--radius);"
      >
        <Search class="w-4 h-4" />
      </button>
    </div>
  </header>

  <!-- Search Bar -->
  {#if showSearch}
    <div class="px-6 py-3 border-b border-border bg-muted/30">
      <div class="max-w-4xl mx-auto relative">
        <input
          id="search-input"
          bind:value={searchQuery}
          placeholder="Search messages..."
          class="w-full px-4 py-2 pl-10 border border-border focus:outline-none focus:ring-2 focus:ring-ring bg-background text-foreground"
          style="border-radius: var(--radius);"
        />
        <Search
          class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground"
        />
        {#if searchQuery}
          <span
            class="absolute right-3 top-1/2 transform -translate-y-1/2 text-xs text-muted-foreground"
          >
            {filteredMessages.length} results
          </span>
        {/if}
      </div>
    </div>
  {/if}

  <!-- Agent Branding -->
  <div class="px-6 py-4 border-b border-border">
    <div class="flex items-center space-x-4">
      <div
        class="w-12 h-12 flex items-center justify-center border-2 border-border bg-primary"
        style="box-shadow: var(--shadow-sm); border-radius: var(--radius);"
      >
        <Building2 class="w-6 h-6 text-primary-foreground" />
      </div>
      <div>
        <h1 class="text-3xl font-black flex items-center gap-3 text-foreground">
          <Zap class="w-8 h-8 text-primary" />
          Compass
        </h1>
        <p class="text-muted-foreground">
          AI-powered research assistant for competitive analysis and market
          insights
        </p>
      </div>
    </div>
  </div>

  <!-- Chat Messages Area -->
  <div bind:this={chatContainer} class="flex-1 overflow-y-auto p-6">
    <div class="max-w-4xl mx-auto space-y-6">
      <ErrorBoundary onRetry={retryLastMessage}>
        {#if isInitialLoad}
          <!-- Initial Loading State -->
          <SkeletonLoader type="message" count={2} />
        {:else if filteredMessages.length === 0 && !searchQuery}
          <!-- Empty State -->
          <div class="text-center py-8">
            <div class="flex items-center justify-center gap-2 mb-6">
              <Zap class="w-6 h-6" style="color: var(--primary);" />
              <h3 class="text-2xl font-bold" style="color: var(--foreground);">
                Ready to Research
              </h3>
            </div>
            <p
              class="font-medium mb-8 max-w-2xl mx-auto"
              style="color: var(--muted-foreground);"
            >
              Get marketing intelligence on any company. Ask your research
              question below.
            </p>

            <!-- Keyboard Shortcuts Hint -->
            <div class="text-xs text-muted-foreground space-y-1">
              <p>
                <kbd class="px-2 py-1 bg-muted" style="border-radius: var(--radius);">Ctrl+K</kbd> to search
              </p>
              <p>
                <kbd class="px-2 py-1 bg-muted" style="border-radius: var(--radius);">/</kbd> to focus input
              </p>
              <p>
                <kbd class="px-2 py-1 bg-muted" style="border-radius: var(--radius);">Ctrl+Enter</kbd> to send
              </p>
            </div>
          </div>
        {:else if filteredMessages.length === 0 && searchQuery}
          <!-- No Search Results -->
          <div class="text-center py-8">
            <Search class="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <h3 class="text-lg font-semibold text-foreground mb-2">
              No results found
            </h3>
            <p class="text-muted-foreground">
              No messages match your search for "{searchQuery}"
            </p>
          </div>
        {/if}

        {#each filteredMessages as message (message.id)}
          <div class="flex items-start space-x-4">
            <div
              class="w-10 h-10 flex-shrink-0 flex items-center justify-center border-2"
              style="background: var(--{message.role === 'user'
                ? 'primary'
                : 'secondary'}); border-color: var(--border); box-shadow: var(--shadow-sm); border-radius: var(--radius);"
            >
              {#if message.role === "user"}
                <User
                  class="w-5 h-5"
                  style="color: var(--primary-foreground);"
                />
              {:else}
                <Bot
                  class="w-5 h-5"
                  style="color: var(--secondary-foreground);"
                />
              {/if}
            </div>

            <div class="flex-1 max-w-4xl">
              <div class="flex items-center gap-2 mb-2">
                <span
                  class="text-sm font-bold"
                  style="color: var(--foreground);"
                >
                  {message.role === "user" ? "You" : "Compass"}
                </span>
                <div class="flex items-center gap-1">
                  <Clock
                    class="w-3 h-3"
                    style="color: var(--muted-foreground);"
                  />
                  <span class="text-xs" style="color: var(--muted-foreground);">
                    {message.timestamp.toLocaleTimeString()}
                  </span>
                </div>
                {#if message.role === "assistant" && message.isReport}
                  <button
                    on:click={() => downloadAsMarkdown(message)}
                    class="btn-secondary px-2 py-1 text-xs flex items-center gap-1"
                    title="Download as Markdown"
                  >
                    <Download class="w-3 h-3" />
                    Download
                  </button>
                {/if}
              </div>

              {#if message.role === "user"}
                <div
                  class="p-4 border-2"
                  style="background: var(--primary); border-color: var(--border); box-shadow: var(--shadow-sm); border-radius: var(--radius);"
                >
                  <p
                    class="font-medium"
                    style="color: var(--primary-foreground);"
                  >
                    {message.content}
                  </p>
                </div>
              {:else}
                {@const insights = extractInsights(message.content)}
                {@const isStructured = (() => {
                  try {
                    return message.content && isStructuredResearchData(message.content)
                  } catch (error) {
                    console.error('Error checking structured data:', error)
                    return false
                  }
                })()}
                {@const parsedResult = (() => {
                  try {
                    return isStructured ? parseResearchResult(message.content) : null
                  } catch (error) {
                    console.error('Error parsing research result:', error)
                    return null
                  }
                })()}
                <div
                  class="border-2"
                  style="background: var(--background); border-color: var(--border); box-shadow: var(--shadow); border-radius: 0.5rem;"
                >
                  <!-- TL;DR Summary -->
                  {#if insights.summary}
                    <div class="p-4 border-b-2 border-border bg-muted/50">
                      <div class="flex items-start gap-2">
                        <span
                          class="text-xs font-bold px-2 py-1 bg-primary text-primary-foreground"
                          style="border-radius: var(--radius);"
                          >TL;DR</span
                        >
                        <div
                          class="text-sm font-medium formatted-summary"
                          style="color: var(--foreground);"
                        >
                          {@html formatSummary(insights.summary)}
                        </div>
                      </div>
                    </div>
                  {/if}

                  <!-- Badges -->
                  {#if insights.badges.length > 0}
                    <div class="px-6 pt-4 pb-2">
                      <div class="flex flex-wrap gap-2">
                        {#each insights.badges as badge}
                          <span
                            class="text-xs font-bold px-2 py-1 border border-border"
                            style="background: var(--accent); color: var(--accent-foreground); border-radius: var(--radius);"
                          >
                            {badge}
                          </span>
                        {/each}
                      </div>
                    </div>
                  {/if}

                  <!-- Structured Research Results -->
                  {#if isStructured && parsedResult && (parsedResult.target_company || parsedResult.all_contacts || parsedResult.summary)}
                    <div class="p-6 space-y-6">
                      <!-- Research Summary -->
                      {#if parsedResult.summary}
                        <div class="bg-muted/30 border border-border rounded-lg p-4">
                          <h4 class="font-semibold text-foreground mb-2 flex items-center gap-2">
                            <Info class="w-4 h-4" />
                            Executive Summary
                          </h4>
                          <p class="text-sm text-muted-foreground leading-relaxed">
                            {parsedResult.summary}
                          </p>
                        </div>
                      {/if}

                      <!-- Target Company -->
                      {#if parsedResult.target_company}
                        <div>
                          <h4 class="font-semibold text-foreground mb-3 flex items-center gap-2">
                            <Building2 class="w-4 h-4" />
                            Target Company
                          </h4>
                          <CompanyResultCard
                            company={parsedResult.target_company}
                            showContacts={true}
                            showWebPresence={true}
                            showTechnicalAnalysis={true}
                          />
                        </div>
                      {/if}

                      <!-- Competitor Companies -->
                      {#if parsedResult.competitor_companies && parsedResult.competitor_companies.length > 0}
                        <div>
                          <h4 class="font-semibold text-foreground mb-3 flex items-center gap-2">
                            <Building2 class="w-4 h-4" />
                            Competitor Analysis ({parsedResult.competitor_companies.length})
                          </h4>
                          <div class="grid gap-4 md:grid-cols-2">
                            {#each parsedResult.competitor_companies.slice(0, 4) as competitor}
                              <CompanyResultCard
                                company={competitor}
                                showContacts={false}
                                showWebPresence={false}
                                showTechnicalAnalysis={false}
                              />
                            {/each}
                          </div>
                          {#if parsedResult.competitor_companies.length > 4}
                            <div class="text-center mt-4">
                              <span class="text-sm text-muted-foreground">
                                +{parsedResult.competitor_companies.length - 4} more competitors
                              </span>
                            </div>
                          {/if}
                        </div>
                      {/if}

                      <!-- Key Contacts -->
                      {#if parsedResult.all_contacts && parsedResult.all_contacts.length > 0}
                        <div>
                          <h4 class="font-semibold text-foreground mb-3 flex items-center gap-2">
                            <User class="w-4 h-4" />
                            Key Contacts ({parsedResult.all_contacts.length})
                          </h4>
                          <div class="grid gap-3 md:grid-cols-2 lg:grid-cols-3">
                            {#each parsedResult.all_contacts.slice(0, 6) as contact}
                              <ContactCard
                                {contact}
                                showCompanyInfo={true}
                                compact={true}
                              />
                            {/each}
                          </div>
                          {#if parsedResult.all_contacts.length > 6}
                            <div class="text-center mt-4">
                              <span class="text-sm text-muted-foreground">
                                +{parsedResult.all_contacts.length - 6} more contacts
                              </span>
                            </div>
                          {/if}
                        </div>
                      {/if}

                      <!-- Tool Status & Confidence -->
                      {#if parsedResult.tool_status || parsedResult.confidence_score}
                        <div class="border-t border-border pt-4">
                          <div class="flex items-center justify-between text-xs text-muted-foreground">
                            <div class="flex items-center gap-4">
                              {#if parsedResult.sources_used}
                                <span>Sources: {parsedResult.sources_used.join(', ')}</span>
                              {/if}
                              {#if parsedResult.processing_time}
                                <span>Processing Time: {parsedResult.processing_time}</span>
                              {/if}
                            </div>
                            {#if parsedResult.confidence_score}
                              <span class="flex items-center gap-1">
                                <CheckCircle2 class="w-3 h-3" />
                                Overall Confidence: {Math.round(parsedResult.confidence_score * 100)}%
                              </span>
                            {/if}
                          </div>
                        </div>
                      {/if}
                    </div>
                  {:else}
                    <!-- Fallback to Original Content Display -->
                    <div class="p-6">
                      <div class="formatted-content max-w-none">
                        {@html formatContent(message.content)}
                      </div>
                    </div>
                  {/if}

                  <!-- Follow-up Actions -->
                  <div class="px-6 pb-4 border-t border-border">
                    <div class="flex flex-wrap gap-2 mt-4">
                      <button
                        on:click={() =>
                          (input = `Compare this analysis with their main competitor`)}
                        class="text-xs font-bold px-3 py-2 border-2 border-border bg-card hover:bg-muted transition-colors flex items-center gap-1"
                        style="border-radius: var(--radius);"
                      >
                        <Eye class="w-3 h-3" />
                        Compare with competitor
                      </button>
                      <button
                        on:click={() =>
                          (input = `Add visual charts and graphs to this analysis`)}
                        class="text-xs font-bold px-3 py-2 border-2 border-border bg-card hover:bg-muted transition-colors flex items-center gap-1"
                        style="border-radius: var(--radius);"
                      >
                        <ChartBar class="w-3 h-3" />
                        Add visuals
                      </button>
                      <button
                        on:click={() =>
                          (input = `Turn this analysis into presentation slides`)}
                        class="text-xs font-bold px-3 py-2 border-2 border-border bg-card hover:bg-muted transition-colors flex items-center gap-1"
                        style="border-radius: var(--radius);"
                      >
                        <MessageSquare class="w-3 h-3" />
                        Turn into slides
                      </button>
                    </div>
                  </div>
                </div>
              {/if}
            </div>
          </div>
        {/each}

        <!-- Enhanced Progress Tracking -->
        {#if isLoading && progressSteps.length > 0}
          <EnhancedProgressIndicator
            {progressSteps}
            {currentProgress}
            {isLoading}
            showDetails={true}
          />
        {/if}
      </ErrorBoundary>
    </div>
  </div>


  <!-- Input Area -->
  <div class="p-6 border-t border-border bg-background">
    <div class="max-w-4xl mx-auto">

      <div class="relative">
        <textarea
          bind:this={inputElement}
          bind:value={input}
          on:keydown={handleKeyDown}
          on:input={handleTextareaResize}
          placeholder={isLoading ? "Researching..." : currentPlaceholder}
          class="w-full px-4 py-3 pr-20 border border-border resize-none focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent text-foreground bg-background placeholder:text-muted-foreground transition-all duration-200 overflow-y-auto"
          class:opacity-50={isLoading}
          rows="1"
          style="min-height: 48px; max-height: 200px; border-radius: var(--radius);"
          disabled={isLoading}
        ></textarea>
        <div class="absolute right-2 bottom-2 flex items-center space-x-2">
          <button
            class="p-1 hover:bg-accent transition-colors"
            title="Research may generate inaccurate information about companies, markets, or facts. Model: Compass AI v1.3"
            style="border-radius: var(--radius);"
          >
            <Info class="w-4 h-4 text-muted-foreground" />
          </button>
          <button
            on:click={sendMessage}
            disabled={!input.trim() || isLoading}
            class="p-2 bg-primary text-primary-foreground hover:bg-primary/90 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-105 active:scale-95"
            title={isLoading ? "Researching..." : "Send message (Ctrl+Enter)"}
            style="border-radius: var(--radius);"
          >
            {#if isLoading}
              <div
                class="w-4 h-4 border-2 border-white/30 border-t-white animate-spin"
                style="border-radius: 50%;"
              ></div>
            {:else}
              <Search class="w-4 h-4" />
            {/if}
          </button>
        </div>
      </div>
    </div>
  </div>
</main>

<style>
  /* Enhanced animations and micro-interactions */
  .formatted-content {
    animation: fadeIn 0.3s ease-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Smooth hover effects for interactive elements */
  button:hover {
    transform: translateY(-1px);
  }

  button:active {
    transform: translateY(0);
  }

  /* Enhanced focus states */
  textarea:focus,
  input:focus {
    box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
  }

  /* Loading pulse animation */
  .loading-pulse {
    animation: pulse 1.5s ease-in-out infinite;
  }

  @keyframes pulse {
    0%,
    100% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
  }

  /* Smooth transitions for all interactive elements */
  * {
    transition:
      transform 0.2s ease,
      opacity 0.2s ease,
      box-shadow 0.2s ease;
  }

  /* Custom scrollbar for chat area */
  .overflow-y-auto::-webkit-scrollbar {
    width: 6px;
  }

  .overflow-y-auto::-webkit-scrollbar-track {
    background: transparent;
  }

  .overflow-y-auto::-webkit-scrollbar-thumb {
    background: hsl(var(--muted-foreground) / 0.3);
    border-radius: var(--radius);
  }

  .overflow-y-auto::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--muted-foreground) / 0.5);
  }
</style>
