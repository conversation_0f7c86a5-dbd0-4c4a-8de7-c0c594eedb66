<script lang="ts">
  import { 
    Building2, 
    Users, 
    MapPin, 
    Globe, 
    TrendingUp, 
    DollarSign,
    Calendar,
    ExternalLink,
    Star,
    AlertCircle,
    CheckCircle2
  } from "lucide-svelte"

  export let company: any
  export let showContacts = true
  export let showWebPresence = true
  export let showTechnicalAnalysis = true

  // Extract company data with fallbacks
  $: companyData = {
    name: company?.name || company?.company_name || 'Unknown Company',
    domain: company?.domain || company?.website || 'N/A',
    industry: company?.industry || 'Unknown Industry',
    employee_count: company?.employee_count || company?.employees || 'N/A',
    location: company?.location || company?.headquarters || 'N/A',
    founded_year: company?.founded_year || company?.founded || 'N/A',
    revenue: company?.revenue || company?.annual_revenue || 'N/A',
    description: company?.description || company?.short_description || 'No description available',
    confidence_score: company?.confidence_score || 0.75,
    source: company?.source || 'apollo'
  }

  // Extract contacts data
  $: contacts = company?.contacts || []
  
  // Extract web presence data
  $: webPresence = company?.web_presence || {}
  
  // Extract technical analysis
  $: technicalAnalysis = company?.technical_analysis || {}

  function getConfidenceColor(score: number) {
    if (score >= 0.8) return 'text-green-600'
    if (score >= 0.6) return 'text-yellow-600'
    return 'text-red-600'
  }

  function getConfidenceIcon(score: number) {
    if (score >= 0.8) return CheckCircle2
    if (score >= 0.6) return AlertCircle
    return AlertCircle
  }

  function formatEmployeeCount(count: any) {
    if (typeof count === 'string') return count
    if (typeof count === 'number') {
      if (count >= 1000) return `${(count / 1000).toFixed(1)}K+`
      return count.toString()
    }
    return 'N/A'
  }

  function formatRevenue(revenue: any) {
    if (typeof revenue === 'string') return revenue
    if (typeof revenue === 'number') {
      if (revenue >= 1000000) return `$${(revenue / 1000000).toFixed(1)}M`
      if (revenue >= 1000) return `$${(revenue / 1000).toFixed(1)}K`
      return `$${revenue}`
    }
    return 'N/A'
  }
</script>

<div class="border-2 border-border bg-card shadow-sm rounded-lg overflow-hidden">
  <!-- Company Header -->
  <div class="p-4 border-b border-border bg-muted/30">
    <div class="flex items-start justify-between">
      <div class="flex items-start gap-3">
        <div class="p-2 bg-primary/10 rounded-lg">
          <Building2 class="w-5 h-5 text-primary" />
        </div>
        <div>
          <h3 class="font-semibold text-lg text-foreground">{companyData.name}</h3>
          <div class="flex items-center gap-2 mt-1">
            {#if companyData.domain !== 'N/A'}
              <a 
                href="https://{companyData.domain}" 
                target="_blank" 
                rel="noopener noreferrer"
                class="text-sm text-muted-foreground hover:text-primary flex items-center gap-1"
              >
                <Globe class="w-3 h-3" />
                {companyData.domain}
                <ExternalLink class="w-3 h-3" />
              </a>
            {/if}
          </div>
        </div>
      </div>
      
      <!-- Confidence Score -->
      <div class="flex items-center gap-1 text-xs">
        <svelte:component 
          this={getConfidenceIcon(companyData.confidence_score)} 
          class="w-3 h-3 {getConfidenceColor(companyData.confidence_score)}" 
        />
        <span class="{getConfidenceColor(companyData.confidence_score)}">
          {Math.round(companyData.confidence_score * 100)}%
        </span>
      </div>
    </div>
  </div>

  <!-- Company Details -->
  <div class="p-4 space-y-3">
    <!-- Description -->
    {#if companyData.description !== 'No description available'}
      <p class="text-sm text-muted-foreground leading-relaxed">
        {companyData.description}
      </p>
    {/if}

    <!-- Key Metrics -->
    <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
      <div class="flex items-center gap-2">
        <Users class="w-4 h-4 text-muted-foreground" />
        <div>
          <div class="text-xs text-muted-foreground">Employees</div>
          <div class="text-sm font-medium">{formatEmployeeCount(companyData.employee_count)}</div>
        </div>
      </div>
      
      <div class="flex items-center gap-2">
        <MapPin class="w-4 h-4 text-muted-foreground" />
        <div>
          <div class="text-xs text-muted-foreground">Location</div>
          <div class="text-sm font-medium">{companyData.location}</div>
        </div>
      </div>
      
      <div class="flex items-center gap-2">
        <Calendar class="w-4 h-4 text-muted-foreground" />
        <div>
          <div class="text-xs text-muted-foreground">Founded</div>
          <div class="text-sm font-medium">{companyData.founded_year}</div>
        </div>
      </div>
      
      <div class="flex items-center gap-2">
        <DollarSign class="w-4 h-4 text-muted-foreground" />
        <div>
          <div class="text-xs text-muted-foreground">Revenue</div>
          <div class="text-sm font-medium">{formatRevenue(companyData.revenue)}</div>
        </div>
      </div>
    </div>

    <!-- Industry Tag -->
    <div class="flex items-center gap-2">
      <TrendingUp class="w-4 h-4 text-muted-foreground" />
      <span class="text-xs bg-accent text-accent-foreground px-2 py-1 rounded-md">
        {companyData.industry}
      </span>
    </div>
  </div>

  <!-- Contacts Section -->
  {#if showContacts && contacts.length > 0}
    <div class="border-t border-border p-4">
      <h4 class="font-medium text-sm text-foreground mb-3 flex items-center gap-2">
        <Users class="w-4 h-4" />
        Key Contacts ({contacts.length})
      </h4>
      <div class="space-y-2">
        {#each contacts.slice(0, 3) as contact}
          <div class="flex items-center justify-between p-2 bg-muted/50 rounded-md">
            <div>
              <div class="text-sm font-medium">{contact.name || `${contact.first_name} ${contact.last_name}`}</div>
              <div class="text-xs text-muted-foreground">{contact.title}</div>
            </div>
            {#if contact.linkedin_url}
              <a 
                href={contact.linkedin_url} 
                target="_blank" 
                rel="noopener noreferrer"
                class="text-xs text-primary hover:underline"
              >
                LinkedIn
              </a>
            {/if}
          </div>
        {/each}
        {#if contacts.length > 3}
          <div class="text-xs text-muted-foreground text-center py-1">
            +{contacts.length - 3} more contacts
          </div>
        {/if}
      </div>
    </div>
  {/if}

  <!-- Web Presence Section -->
  {#if showWebPresence && Object.keys(webPresence).length > 0}
    <div class="border-t border-border p-4">
      <h4 class="font-medium text-sm text-foreground mb-3 flex items-center gap-2">
        <Globe class="w-4 h-4" />
        Web Presence
      </h4>
      <div class="text-xs text-muted-foreground">
        {#if webPresence.value_propositions}
          <div class="mb-2">
            <strong>Value Props:</strong> {webPresence.value_propositions.slice(0, 2).join(', ')}
          </div>
        {/if}
        {#if webPresence.key_features}
          <div>
            <strong>Key Features:</strong> {webPresence.key_features.slice(0, 3).join(', ')}
          </div>
        {/if}
      </div>
    </div>
  {/if}

  <!-- Technical Analysis Section -->
  {#if showTechnicalAnalysis && Object.keys(technicalAnalysis).length > 0}
    <div class="border-t border-border p-4">
      <h4 class="font-medium text-sm text-foreground mb-3 flex items-center gap-2">
        <Star class="w-4 h-4" />
        Technical Analysis
      </h4>
      <div class="text-xs text-muted-foreground">
        {#if technicalAnalysis.technologies}
          <div class="mb-2">
            <strong>Tech Stack:</strong> {technicalAnalysis.technologies.slice(0, 4).join(', ')}
          </div>
        {/if}
        {#if technicalAnalysis.frameworks}
          <div>
            <strong>Frameworks:</strong> {technicalAnalysis.frameworks.slice(0, 3).join(', ')}
          </div>
        {/if}
      </div>
    </div>
  {/if}

  <!-- Source Attribution -->
  <div class="border-t border-border px-4 py-2 bg-muted/20">
    <div class="flex items-center justify-between text-xs text-muted-foreground">
      <span>Source: {companyData.source.toUpperCase()}</span>
      <span>Confidence: {Math.round(companyData.confidence_score * 100)}%</span>
    </div>
  </div>
</div>
