<script lang="ts">
  import { 
    Building2, 
    Users, 
    DollarSign, 
    MapPin, 
    Calendar, 
    ExternalLink, 
    Globe, 
    Linkedin, 
    Phone,
    Code2,
    CheckCircle2,
    Clock
  } from 'lucide-svelte'
  import { fade, scale } from 'svelte/transition'
  import { quintOut } from 'svelte/easing'

  export let company: any
  export let showContacts: boolean = true

  // Format employee count for display
  function formatEmployeeCount(employees: string | number): string {
    if (typeof employees === 'number') {
      return employees.toLocaleString()
    }
    return employees || 'Unknown'
  }

  // Format revenue for display
  function formatRevenue(revenue: string): string {
    if (!revenue || revenue === 'Unknown') return 'Not disclosed'
    return revenue
  }

  // Get confidence color based on score
  function getConfidenceColor(score: number): string {
    if (score >= 0.9) return 'text-green-600'
    if (score >= 0.7) return 'text-yellow-600'
    return 'text-red-600'
  }

  // Format confidence score as percentage
  function formatConfidence(score: number): string {
    return `${Math.round(score * 100)}%`
  }

  // Format date for display
  function formatDate(dateString: string): string {
    if (!dateString) return 'Unknown'
    try {
      return new Date(dateString).toLocaleDateString()
    } catch {
      return 'Unknown'
    }
  }
</script>

<div 
  class="card-professional p-6 space-y-6"
  in:fade={{ duration: 400, easing: quintOut }}
>
  <!-- Header -->
  <div class="flex items-start justify-between">
    <div class="flex items-start gap-4">
      <div class="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center flex-shrink-0">
        <Building2 class="w-6 h-6 text-primary" />
      </div>
      <div class="flex-1">
        <h2 class="text-xl font-bold text-foreground mb-1">{company.name}</h2>
        <p class="text-sm text-muted-foreground">{company.domain}</p>
        {#if company.description}
          <p class="text-sm text-foreground mt-2 leading-relaxed">{company.description}</p>
        {/if}
      </div>
    </div>
    
    <!-- Confidence Score -->
    {#if company.confidence_score}
      <div class="flex items-center gap-1 px-2 py-1 bg-secondary rounded-md">
        <CheckCircle2 class="w-3 h-3 {getConfidenceColor(company.confidence_score)}" />
        <span class="text-xs font-medium {getConfidenceColor(company.confidence_score)}">
          {formatConfidence(company.confidence_score)}
        </span>
      </div>
    {/if}
  </div>

  <!-- Key Metrics Grid -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
    <!-- Industry -->
    <div class="flex items-center gap-3 p-3 bg-secondary/50 rounded-lg">
      <Building2 class="w-4 h-4 text-muted-foreground flex-shrink-0" />
      <div>
        <div class="text-xs text-muted-foreground">Industry</div>
        <div class="text-sm font-medium">{company.industry || 'Unknown'}</div>
      </div>
    </div>

    <!-- Employees -->
    <div class="flex items-center gap-3 p-3 bg-secondary/50 rounded-lg">
      <Users class="w-4 h-4 text-muted-foreground flex-shrink-0" />
      <div>
        <div class="text-xs text-muted-foreground">Employees</div>
        <div class="text-sm font-medium">{formatEmployeeCount(company.employees)}</div>
      </div>
    </div>

    <!-- Revenue -->
    <div class="flex items-center gap-3 p-3 bg-secondary/50 rounded-lg">
      <DollarSign class="w-4 h-4 text-muted-foreground flex-shrink-0" />
      <div>
        <div class="text-xs text-muted-foreground">Revenue</div>
        <div class="text-sm font-medium">{formatRevenue(company.revenue)}</div>
      </div>
    </div>

    <!-- Founded -->
    <div class="flex items-center gap-3 p-3 bg-secondary/50 rounded-lg">
      <Calendar class="w-4 h-4 text-muted-foreground flex-shrink-0" />
      <div>
        <div class="text-xs text-muted-foreground">Founded</div>
        <div class="text-sm font-medium">{company.founded_year || 'Unknown'}</div>
      </div>
    </div>
  </div>

  <!-- Location & Contact Info -->
  <div class="space-y-3">
    {#if company.location || (company.headquarters && (company.headquarters.city || company.headquarters.state || company.headquarters.country))}
      <div class="flex items-center gap-2">
        <MapPin class="w-4 h-4 text-muted-foreground" />
        <span class="text-sm">
          {company.location || [company.headquarters?.city, company.headquarters?.state, company.headquarters?.country].filter(Boolean).join(', ')}
        </span>
      </div>
    {/if}

    <!-- Links -->
    <div class="flex flex-wrap gap-3">
      {#if company.website_url}
        <a 
          href={company.website_url} 
          target="_blank" 
          rel="noopener noreferrer"
          class="inline-flex items-center gap-1 text-sm text-primary hover:text-primary/80 transition-colors"
        >
          <Globe class="w-3 h-3" />
          Website
          <ExternalLink class="w-3 h-3" />
        </a>
      {/if}

      {#if company.linkedin_url}
        <a 
          href={company.linkedin_url} 
          target="_blank" 
          rel="noopener noreferrer"
          class="inline-flex items-center gap-1 text-sm text-primary hover:text-primary/80 transition-colors"
        >
          <Linkedin class="w-3 h-3" />
          LinkedIn
          <ExternalLink class="w-3 h-3" />
        </a>
      {/if}

      {#if company.phone}
        <div class="inline-flex items-center gap-1 text-sm text-muted-foreground">
          <Phone class="w-3 h-3" />
          {company.phone}
        </div>
      {/if}
    </div>
  </div>

  <!-- Technologies -->
  {#if company.technologies && company.technologies.length > 0}
    <div class="space-y-2">
      <div class="flex items-center gap-2">
        <Code2 class="w-4 h-4 text-muted-foreground" />
        <span class="text-sm font-medium">Technology Stack</span>
      </div>
      <div class="flex flex-wrap gap-1">
        {#each company.technologies.slice(0, 8) as tech}
          <span class="inline-flex items-center px-2 py-1 bg-primary/10 text-primary text-xs rounded-md">
            {tech}
          </span>
        {/each}
        {#if company.technologies.length > 8}
          <span class="inline-flex items-center px-2 py-1 bg-secondary text-secondary-foreground text-xs rounded-md">
            +{company.technologies.length - 8} more
          </span>
        {/if}
      </div>
    </div>
  {/if}

  <!-- Key Contacts Preview -->
  {#if showContacts && company.contacts && company.contacts.length > 0}
    <div class="space-y-3 pt-4 border-t border-border">
      <h3 class="text-sm font-semibold flex items-center gap-2">
        <Users class="w-4 h-4" />
        Key Contacts ({company.contacts.length})
      </h3>
      <div class="space-y-2">
        {#each company.contacts.slice(0, 3) as contact}
          <div class="flex items-center justify-between p-2 bg-secondary/30 rounded-md">
            <div>
              <div class="text-sm font-medium">{contact.name}</div>
              <div class="text-xs text-muted-foreground">{contact.title}</div>
            </div>
            {#if contact.linkedin_url}
              <a 
                href={contact.linkedin_url} 
                target="_blank" 
                rel="noopener noreferrer"
                class="text-primary hover:text-primary/80 transition-colors"
              >
                <Linkedin class="w-4 h-4" />
              </a>
            {/if}
          </div>
        {/each}
        {#if company.contacts.length > 3}
          <div class="text-xs text-muted-foreground text-center py-1">
            +{company.contacts.length - 3} more contacts
          </div>
        {/if}
      </div>
    </div>
  {/if}

  <!-- Data Attribution -->
  <div class="flex items-center justify-between pt-4 border-t border-border text-xs text-muted-foreground">
    <div class="flex items-center gap-1">
      <Clock class="w-3 h-3" />
      <span>Updated {formatDate(company.last_updated)}</span>
    </div>
    {#if company.data_sources && company.data_sources.length > 0}
      <div class="flex items-center gap-1">
        <span>Sources:</span>
        <span class="font-medium">{company.data_sources.join(', ')}</span>
      </div>
    {/if}
  </div>
</div>
