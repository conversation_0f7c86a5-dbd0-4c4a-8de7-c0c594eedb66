<script lang="ts">
  import { 
    Globe, 
    Target, 
    MessageSquare, 
    TrendingUp, 
    ExternalLink,
    CheckCircle2,
    Clock,
    ChevronDown,
    ChevronUp,
    Eye,
    Search,
    FileText
  } from 'lucide-svelte'
  import { fade, fly, slide } from 'svelte/transition'
  import { quintOut } from 'svelte/easing'

  export let webPresence: any = {}
  export let companyName: string = 'Company'

  let expandedSections: Set<string> = new Set()

  // Toggle section expansion
  function toggleSection(sectionKey: string) {
    if (expandedSections.has(sectionKey)) {
      expandedSections.delete(sectionKey)
    } else {
      expandedSections.add(sectionKey)
    }
    expandedSections = expandedSections
  }

  // Get confidence color based on score
  function getConfidenceColor(score: number): string {
    if (score >= 0.9) return 'text-green-600'
    if (score >= 0.7) return 'text-yellow-600'
    return 'text-red-600'
  }

  // Format confidence score as percentage
  function formatConfidence(score: number): string {
    return `${Math.round(score * 100)}%`
  }

  // Format date for display
  function formatDate(dateString: string): string {
    if (!dateString) return 'Unknown'
    try {
      return new Date(dateString).toLocaleDateString()
    } catch {
      return 'Unknown'
    }
  }

  // Get source icon
  function getSourceIcon(source: string) {
    if (source.includes('Scrape')) return Eye
    if (source.includes('Extract')) return FileText
    if (source.includes('Search')) return Search
    return Globe
  }

  // Web presence sections configuration
  $: webPresenceSections = [
    {
      key: 'landing_page_analysis',
      title: 'Landing Page Analysis',
      icon: Eye,
      data: webPresence.landing_page_analysis,
      description: 'Value propositions, messaging, and conversion strategy'
    },
    {
      key: 'content_strategy',
      title: 'Content Strategy',
      icon: FileText,
      data: webPresence.content_strategy,
      description: 'Blog topics, SEO focus, and content patterns'
    },
    {
      key: 'market_positioning',
      title: 'Market Positioning',
      icon: Target,
      data: webPresence.market_positioning,
      description: 'Competitive mentions, trends, and thought leadership'
    }
  ].filter(section => section.data && Object.keys(section.data).length > 0)
</script>

<div 
  class="card-professional p-6 space-y-6"
  in:fade={{ duration: 400, easing: quintOut }}
>
  <!-- Header -->
  <div class="flex items-center justify-between">
    <div class="flex items-center gap-3">
      <div class="w-10 h-10 bg-blue-500/10 rounded-lg flex items-center justify-center">
        <Globe class="w-5 h-5 text-blue-500" />
      </div>
      <div>
        <h2 class="text-lg font-bold text-foreground">Web Presence Analysis</h2>
        <p class="text-sm text-muted-foreground">
          Digital footprint and online strategy for {companyName}
        </p>
      </div>
    </div>
    
    <div class="flex items-center gap-1 px-2 py-1 bg-secondary rounded-md">
      <TrendingUp class="w-3 h-3 text-muted-foreground" />
      <span class="text-xs font-medium">Firecrawl Analysis</span>
    </div>
  </div>

  <!-- Web Presence Sections -->
  {#if webPresenceSections.length > 0}
    <div class="space-y-4">
      {#each webPresenceSections as section, index (section.key)}
        <div 
          class="border border-border rounded-lg overflow-hidden"
          in:fly={{ y: 20, duration: 300, delay: index * 100, easing: quintOut }}
        >
          <!-- Section Header -->
          <button
            class="w-full flex items-center justify-between p-4 hover:bg-secondary/50 transition-colors"
            on:click={() => toggleSection(section.key)}
          >
            <div class="flex items-center gap-3">
              <svelte:component this={section.icon} class="w-4 h-4 text-muted-foreground" />
              <div class="text-left">
                <h3 class="font-medium text-foreground">{section.title}</h3>
                <p class="text-xs text-muted-foreground">{section.description}</p>
              </div>
            </div>
            
            <div class="flex items-center gap-2">
              {#if section.data.confidence_score}
                <div class="flex items-center gap-1">
                  <CheckCircle2 class="w-3 h-3 {getConfidenceColor(section.data.confidence_score)}" />
                  <span class="text-xs {getConfidenceColor(section.data.confidence_score)}">
                    {formatConfidence(section.data.confidence_score)}
                  </span>
                </div>
              {/if}
              
              {#if expandedSections.has(section.key)}
                <ChevronUp class="w-4 h-4 text-muted-foreground" />
              {:else}
                <ChevronDown class="w-4 h-4 text-muted-foreground" />
              {/if}
            </div>
          </button>

          <!-- Section Content -->
          {#if expandedSections.has(section.key)}
            <div 
              class="px-4 pb-4 border-t border-border bg-secondary/20"
              in:slide={{ duration: 300, easing: quintOut }}
            >
              <div class="space-y-4 mt-4">
                
                <!-- Landing Page Analysis Content -->
                {#if section.key === 'landing_page_analysis'}
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {#if section.data.value_propositions && section.data.value_propositions.length > 0}
                      <div>
                        <h4 class="text-sm font-medium text-foreground mb-2">Value Propositions</h4>
                        <ul class="space-y-1">
                          {#each section.data.value_propositions as proposition}
                            <li class="text-xs text-muted-foreground flex items-start gap-2">
                              <Target class="w-3 h-3 mt-0.5 flex-shrink-0" />
                              <span>{proposition}</span>
                            </li>
                          {/each}
                        </ul>
                      </div>
                    {/if}
                    
                    {#if section.data.call_to_actions && section.data.call_to_actions.length > 0}
                      <div>
                        <h4 class="text-sm font-medium text-foreground mb-2">Call-to-Actions</h4>
                        <ul class="space-y-1">
                          {#each section.data.call_to_actions as cta}
                            <li class="text-xs text-muted-foreground flex items-start gap-2">
                              <MessageSquare class="w-3 h-3 mt-0.5 flex-shrink-0" />
                              <span>{cta}</span>
                            </li>
                          {/each}
                        </ul>
                      </div>
                    {/if}
                  </div>
                  
                  {#if section.data.pricing_strategy}
                    <div>
                      <h4 class="text-sm font-medium text-foreground mb-2">Pricing Strategy</h4>
                      <p class="text-xs text-muted-foreground">{section.data.pricing_strategy}</p>
                    </div>
                  {/if}
                  
                  {#if section.data.target_messaging}
                    <div>
                      <h4 class="text-sm font-medium text-foreground mb-2">Target Messaging</h4>
                      <p class="text-xs text-muted-foreground">{section.data.target_messaging}</p>
                    </div>
                  {/if}
                {/if}

                <!-- Content Strategy Content -->
                {#if section.key === 'content_strategy'}
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {#if section.data.blog_topics && section.data.blog_topics.length > 0}
                      <div>
                        <h4 class="text-sm font-medium text-foreground mb-2">Blog Topics</h4>
                        <div class="flex flex-wrap gap-1">
                          {#each section.data.blog_topics as topic}
                            <span class="px-2 py-1 bg-primary/10 text-primary text-xs rounded">{topic}</span>
                          {/each}
                        </div>
                      </div>
                    {/if}
                    
                    {#if section.data.seo_focus && section.data.seo_focus.length > 0}
                      <div>
                        <h4 class="text-sm font-medium text-foreground mb-2">SEO Focus</h4>
                        <div class="flex flex-wrap gap-1">
                          {#each section.data.seo_focus as keyword}
                            <span class="px-2 py-1 bg-green-500/10 text-green-600 text-xs rounded">{keyword}</span>
                          {/each}
                        </div>
                      </div>
                    {/if}
                  </div>
                  
                  {#if section.data.content_frequency}
                    <div>
                      <h4 class="text-sm font-medium text-foreground mb-2">Content Frequency</h4>
                      <p class="text-xs text-muted-foreground">{section.data.content_frequency}</p>
                    </div>
                  {/if}
                  
                  {#if section.data.content_types && section.data.content_types.length > 0}
                    <div>
                      <h4 class="text-sm font-medium text-foreground mb-2">Content Types</h4>
                      <div class="flex flex-wrap gap-1">
                        {#each section.data.content_types as type}
                          <span class="px-2 py-1 bg-blue-500/10 text-blue-600 text-xs rounded">{type}</span>
                        {/each}
                      </div>
                    </div>
                  {/if}
                {/if}

                <!-- Market Positioning Content -->
                {#if section.key === 'market_positioning'}
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {#if section.data.competitive_mentions && section.data.competitive_mentions.length > 0}
                      <div>
                        <h4 class="text-sm font-medium text-foreground mb-2">Competitive Mentions</h4>
                        <ul class="space-y-1">
                          {#each section.data.competitive_mentions as mention}
                            <li class="text-xs text-muted-foreground">• {mention}</li>
                          {/each}
                        </ul>
                      </div>
                    {/if}
                    
                    {#if section.data.industry_trends && section.data.industry_trends.length > 0}
                      <div>
                        <h4 class="text-sm font-medium text-foreground mb-2">Industry Trends</h4>
                        <ul class="space-y-1">
                          {#each section.data.industry_trends as trend}
                            <li class="text-xs text-muted-foreground">• {trend}</li>
                          {/each}
                        </ul>
                      </div>
                    {/if}
                  </div>
                  
                  {#if section.data.thought_leadership && section.data.thought_leadership.length > 0}
                    <div>
                      <h4 class="text-sm font-medium text-foreground mb-2">Thought Leadership</h4>
                      <ul class="space-y-1">
                        {#each section.data.thought_leadership as leadership}
                          <li class="text-xs text-muted-foreground">• {leadership}</li>
                        {/each}
                      </ul>
                    </div>
                  {/if}
                  
                  {#if section.data.brand_messaging && section.data.brand_messaging.length > 0}
                    <div>
                      <h4 class="text-sm font-medium text-foreground mb-2">Brand Messaging</h4>
                      <ul class="space-y-1">
                        {#each section.data.brand_messaging as message}
                          <li class="text-xs text-muted-foreground">• {message}</li>
                        {/each}
                      </ul>
                    </div>
                  {/if}
                {/if}

                <!-- Data Attribution -->
                <div class="flex items-center justify-between pt-2 border-t border-border text-xs text-muted-foreground">
                  <div class="flex items-center gap-1">
                    <svelte:component this={getSourceIcon(section.data.source)} class="w-3 h-3" />
                    <span>{section.data.source}</span>
                  </div>
                  {#if section.data.last_updated}
                    <div class="flex items-center gap-1">
                      <Clock class="w-3 h-3" />
                      <span>Updated {formatDate(section.data.last_updated)}</span>
                    </div>
                  {/if}
                </div>
              </div>
            </div>
          {/if}
        </div>
      {/each}
    </div>
  {:else}
    <!-- No Web Presence Data -->
    <div class="text-center py-8 text-muted-foreground">
      <Globe class="w-12 h-12 mx-auto mb-3 opacity-50" />
      <h3 class="font-medium mb-1">No Web Presence Data</h3>
      <p class="text-sm">Web analysis data will appear here when available.</p>
    </div>
  {/if}
</div>
