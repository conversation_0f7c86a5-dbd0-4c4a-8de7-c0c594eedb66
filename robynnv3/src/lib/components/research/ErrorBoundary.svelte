<script lang="ts">
  import { onMount } from 'svelte'
  import { AlertTriangle, RefreshCw, Home } from 'lucide-svelte'

  export let fallback: 'minimal' | 'full' = 'full'
  export let onRetry: (() => void) | null = null
  export let error: Error | null = null

  let hasError = false
  let errorMessage = ''
  let errorStack = ''

  // Handle errors passed as props
  $: if (error) {
    hasError = true
    errorMessage = error.message
    errorStack = error.stack || ''
  }

  function handleRetry() {
    hasError = false
    error = null
    errorMessage = ''
    errorStack = ''
    if (onRetry) {
      onRetry()
    }
  }

  function goHome() {
    window.location.href = '/'
  }

  // Global error handler for unhandled promise rejections
  onMount(() => {
    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      console.error('Unhandled promise rejection:', event.reason)
      hasError = true
      errorMessage = event.reason?.message || 'An unexpected error occurred'
      errorStack = event.reason?.stack || ''
    }

    window.addEventListener('unhandledrejection', handleUnhandledRejection)

    return () => {
      window.removeEventListener('unhandledrejection', handleUnhandledRejection)
    }
  })
</script>

{#if hasError}
  {#if fallback === 'minimal'}
    <!-- Minimal Error Display -->
    <div class="flex items-center gap-2 p-3 bg-destructive/10 border border-destructive/20 rounded-lg">
      <AlertTriangle class="w-4 h-4 text-destructive flex-shrink-0" />
      <span class="text-sm text-destructive">Something went wrong</span>
      {#if onRetry}
        <button 
          on:click={handleRetry}
          class="ml-auto text-xs text-destructive hover:text-destructive/80 underline"
        >
          Retry
        </button>
      {/if}
    </div>
  {:else}
    <!-- Full Error Display -->
    <div class="flex flex-col items-center justify-center min-h-[400px] p-8 text-center">
      <div class="w-16 h-16 bg-destructive/10 rounded-full flex items-center justify-center mb-4">
        <AlertTriangle class="w-8 h-8 text-destructive" />
      </div>
      
      <h2 class="text-xl font-semibold text-foreground mb-2">
        Oops! Something went wrong
      </h2>
      
      <p class="text-muted-foreground mb-6 max-w-md">
        We encountered an unexpected error. This has been logged and our team will investigate.
      </p>

      {#if errorMessage}
        <details class="mb-6 w-full max-w-md">
          <summary class="cursor-pointer text-sm text-muted-foreground hover:text-foreground">
            Error Details
          </summary>
          <div class="mt-2 p-3 bg-muted rounded-lg text-left">
            <p class="text-xs font-mono text-destructive break-all">
              {errorMessage}
            </p>
            {#if errorStack}
              <pre class="text-xs text-muted-foreground mt-2 overflow-auto max-h-32">
                {errorStack}
              </pre>
            {/if}
          </div>
        </details>
      {/if}

      <div class="flex gap-3">
        {#if onRetry}
          <button 
            on:click={handleRetry}
            class="btn-professional px-4 py-2 flex items-center gap-2"
          >
            <RefreshCw class="w-4 h-4" />
            Try Again
          </button>
        {/if}
        
        <button 
          on:click={goHome}
          class="btn-secondary px-4 py-2 flex items-center gap-2"
        >
          <Home class="w-4 h-4" />
          Go Home
        </button>
      </div>
    </div>
  {/if}
{:else}
  <!-- Render children when no error -->
  <slot />
{/if}
