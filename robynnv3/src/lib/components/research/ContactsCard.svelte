<script lang="ts">
  import { 
    Users, 
    Mail, 
    Linkedin, 
    Phone, 
    MapPin, 
    Building2,
    CheckCircle2,
    Copy,
    ExternalLink,
    Filter,
    Search,
    Download
  } from 'lucide-svelte'
  import { fade, fly } from 'svelte/transition'
  import { quintOut } from 'svelte/easing'

  export let targetCompany: any = null
  export let competitors: any[] = []

  // Combine all contacts from target company and competitors
  $: allContacts = [
    ...(targetCompany?.contacts || []).map(contact => ({
      ...contact,
      company_name: targetCompany.name,
      company_domain: targetCompany.domain,
      is_target: true
    })),
    ...competitors.flatMap(comp => 
      (comp.contacts || []).map(contact => ({
        ...contact,
        company_name: comp.name,
        company_domain: comp.domain,
        is_target: false
      }))
    )
  ]

  // Filter and search state
  let searchTerm = ''
  let selectedSeniority = 'all'
  let selectedCompany = 'all'
  let showTargetOnly = false

  // Get unique seniority levels
  $: seniorityLevels = [...new Set(allContacts.map(c => c.seniority).filter(Boolean))]
  
  // Get unique companies
  $: companies = [...new Set(allContacts.map(c => c.company_name).filter(Boolean))]

  // Filter contacts based on search and filters
  $: filteredContacts = allContacts.filter(contact => {
    const matchesSearch = !searchTerm || 
      contact.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      contact.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      contact.company_name?.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesSeniority = selectedSeniority === 'all' || contact.seniority === selectedSeniority
    const matchesCompany = selectedCompany === 'all' || contact.company_name === selectedCompany
    const matchesTarget = !showTargetOnly || contact.is_target
    
    return matchesSearch && matchesSeniority && matchesCompany && matchesTarget
  })

  // Get confidence color based on score
  function getConfidenceColor(score: number): string {
    if (score >= 0.9) return 'text-green-600'
    if (score >= 0.7) return 'text-yellow-600'
    return 'text-red-600'
  }

  // Format confidence score as percentage
  function formatConfidence(score: number): string {
    return `${Math.round(score * 100)}%`
  }

  // Get seniority badge color
  function getSeniorityColor(seniority: string): string {
    switch (seniority?.toLowerCase()) {
      case 'c_suite': return 'bg-purple-100 text-purple-800'
      case 'vp': return 'bg-blue-100 text-blue-800'
      case 'director': return 'bg-green-100 text-green-800'
      case 'manager': return 'bg-yellow-100 text-yellow-800'
      case 'senior': return 'bg-orange-100 text-orange-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  // Copy contact info to clipboard
  async function copyContactInfo(contact: any) {
    const info = `${contact.name}\n${contact.title}\n${contact.company_name}\n${contact.email || 'No email'}\n${contact.linkedin_url || 'No LinkedIn'}`
    try {
      await navigator.clipboard.writeText(info)
      // Could add a toast notification here
    } catch (err) {
      console.error('Failed to copy contact info:', err)
    }
  }

  // Export contacts as CSV
  function exportContacts() {
    const csvContent = [
      ['Name', 'Title', 'Company', 'Email', 'LinkedIn', 'Phone', 'Seniority', 'Department', 'Confidence'].join(','),
      ...filteredContacts.map(contact => [
        contact.name || '',
        contact.title || '',
        contact.company_name || '',
        contact.email || '',
        contact.linkedin_url || '',
        contact.phone || '',
        contact.seniority || '',
        contact.department || '',
        contact.confidence_score ? formatConfidence(contact.confidence_score) : ''
      ].map(field => `"${field}"`).join(','))
    ].join('\n')

    const blob = new Blob([csvContent], { type: 'text/csv' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `contacts-${new Date().toISOString().split('T')[0]}.csv`
    a.click()
    URL.revokeObjectURL(url)
  }
</script>

<div 
  class="card-professional p-6 space-y-6"
  in:fade={{ duration: 400, easing: quintOut }}
>
  <!-- Header -->
  <div class="flex items-center justify-between">
    <div class="flex items-center gap-3">
      <div class="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
        <Users class="w-5 h-5 text-primary" />
      </div>
      <div>
        <h2 class="text-lg font-bold text-foreground">Key Contacts</h2>
        <p class="text-sm text-muted-foreground">
          {filteredContacts.length} of {allContacts.length} contacts
        </p>
      </div>
    </div>
    
    <button
      on:click={exportContacts}
      class="inline-flex items-center gap-2 px-3 py-1.5 bg-primary text-primary-foreground text-sm rounded-md hover:bg-primary/90 transition-colors"
      disabled={filteredContacts.length === 0}
    >
      <Download class="w-3 h-3" />
      Export CSV
    </button>
  </div>

  <!-- Filters -->
  <div class="space-y-4 p-4 bg-secondary/30 rounded-lg">
    <!-- Search -->
    <div class="relative">
      <Search class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
      <input
        type="text"
        placeholder="Search contacts by name, title, or company..."
        bind:value={searchTerm}
        class="w-full pl-10 pr-4 py-2 bg-background border border-border rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
      />
    </div>

    <!-- Filter Controls -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-3">
      <select 
        bind:value={selectedSeniority}
        class="px-3 py-2 bg-background border border-border rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary"
      >
        <option value="all">All Seniority Levels</option>
        {#each seniorityLevels as level}
          <option value={level}>{level}</option>
        {/each}
      </select>

      <select 
        bind:value={selectedCompany}
        class="px-3 py-2 bg-background border border-border rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary"
      >
        <option value="all">All Companies</option>
        {#each companies as company}
          <option value={company}>{company}</option>
        {/each}
      </select>

      <label class="flex items-center gap-2 px-3 py-2">
        <input 
          type="checkbox" 
          bind:checked={showTargetOnly}
          class="rounded border-border focus:ring-primary"
        />
        <span class="text-sm">Target company only</span>
      </label>

      <div class="flex items-center gap-1 px-2 py-1 bg-secondary rounded-md">
        <Filter class="w-3 h-3 text-muted-foreground" />
        <span class="text-xs font-medium">{filteredContacts.length} results</span>
      </div>
    </div>
  </div>

  <!-- Contacts Grid -->
  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
    {#each filteredContacts as contact, index (contact.email || contact.name + contact.company_name)}
      <div 
        class="border border-border rounded-lg p-4 hover:shadow-md transition-all duration-200 {contact.is_target ? 'bg-primary/5 border-primary/20' : ''}"
        in:fly={{ y: 20, duration: 300, delay: index * 50, easing: quintOut }}
      >
        <!-- Contact Header -->
        <div class="flex items-start justify-between mb-3">
          <div class="flex-1 min-w-0">
            <div class="flex items-center gap-2 mb-1">
              <h3 class="font-semibold text-foreground truncate">{contact.name}</h3>
              {#if contact.is_target}
                <span class="px-1.5 py-0.5 bg-primary/20 text-primary text-xs rounded">Target</span>
              {/if}
            </div>
            <p class="text-sm text-muted-foreground mb-1">{contact.title}</p>
            <div class="flex items-center gap-1 text-xs text-muted-foreground">
              <Building2 class="w-3 h-3" />
              <span>{contact.company_name}</span>
            </div>
          </div>
          
          {#if contact.confidence_score}
            <div class="flex items-center gap-1">
              <CheckCircle2 class="w-3 h-3 {getConfidenceColor(contact.confidence_score)}" />
              <span class="text-xs {getConfidenceColor(contact.confidence_score)}">
                {formatConfidence(contact.confidence_score)}
              </span>
            </div>
          {/if}
        </div>

        <!-- Contact Details -->
        <div class="space-y-2 mb-3">
          {#if contact.email}
            <div class="flex items-center gap-2 text-sm">
              <Mail class="w-3 h-3 text-muted-foreground" />
              <a 
                href="mailto:{contact.email}" 
                class="text-primary hover:text-primary/80 transition-colors truncate"
              >
                {contact.email}
              </a>
            </div>
          {/if}

          {#if contact.phone}
            <div class="flex items-center gap-2 text-sm">
              <Phone class="w-3 h-3 text-muted-foreground" />
              <span class="text-muted-foreground">{contact.phone}</span>
            </div>
          {/if}

          {#if contact.location}
            <div class="flex items-center gap-2 text-sm">
              <MapPin class="w-3 h-3 text-muted-foreground" />
              <span class="text-muted-foreground">{contact.location}</span>
            </div>
          {/if}
        </div>

        <!-- Tags -->
        <div class="flex flex-wrap gap-1 mb-3">
          {#if contact.seniority}
            <span class="px-2 py-1 text-xs rounded {getSeniorityColor(contact.seniority)}">
              {contact.seniority}
            </span>
          {/if}
          {#if contact.department}
            <span class="px-2 py-1 bg-secondary text-secondary-foreground text-xs rounded">
              {contact.department}
            </span>
          {/if}
        </div>

        <!-- Actions -->
        <div class="flex items-center justify-between pt-3 border-t border-border">
          <div class="flex items-center gap-2">
            {#if contact.linkedin_url}
              <a 
                href={contact.linkedin_url} 
                target="_blank" 
                rel="noopener noreferrer"
                class="inline-flex items-center gap-1 px-2 py-1 text-xs text-primary hover:text-primary/80 transition-colors"
              >
                <Linkedin class="w-3 h-3" />
                LinkedIn
                <ExternalLink class="w-2 h-2" />
              </a>
            {/if}
          </div>
          
          <button
            on:click={() => copyContactInfo(contact)}
            class="inline-flex items-center gap-1 px-2 py-1 text-xs text-muted-foreground hover:text-foreground transition-colors"
            title="Copy contact info"
          >
            <Copy class="w-3 h-3" />
            Copy
          </button>
        </div>
      </div>
    {/each}
  </div>

  {#if filteredContacts.length === 0}
    <div class="text-center py-8 text-muted-foreground">
      <Users class="w-12 h-12 mx-auto mb-3 opacity-50" />
      <p class="text-sm">No contacts found</p>
      <p class="text-xs mt-1">Try adjusting your search criteria</p>
    </div>
  {/if}
</div>
