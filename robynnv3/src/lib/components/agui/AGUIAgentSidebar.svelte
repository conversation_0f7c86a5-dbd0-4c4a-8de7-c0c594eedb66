<script lang="ts">
  import { writable } from 'svelte/store'
  import { createEventDispatcher, tick } from 'svelte'
  import {
    Bot,
    User,
    Send,
    Loader2,
    X,
    Zap,
    CheckCircle2,
    Search,
    Globe,
    Building2,
    Plus,
    Crosshair,
    UserPlus,
    File,
    Download,
    Edit3,
    Book
  } from 'lucide-svelte'
  import { slide, fade, fly, scale } from 'svelte/transition'
  import { quintOut, backOut } from 'svelte/easing'
  import ToolBadge from '../ToolBadge.svelte'
  import { ToolRegistry } from '$lib/services/tool-registry'
  import { conversationStore, sendMessage, addMessageToCanvas } from '$lib/agui/conversation-store'

  let { isOpen = $bindable(false) } = $props()
  
  const dispatch = createEventDispatcher()

  interface AGUIMessage {
    id: string
    role: 'user' | 'assistant'
    content: string
    timestamp: Date
  }

  let input = $state('')
  let messagesContainer = $state<HTMLElement>()

  // Complete tools list for Product Marketing Agent (matching actual Mastra tools)
  const aguiTools = [
    // Search & Discovery Tools
    { name: 'web_search', description: 'Web Search', icon: Search, category: 'research' },
    { name: 'exa_search', description: 'Enhanced Search', icon: Zap, category: 'research' },
    { name: 'firecrawl_search', description: 'Deep Search', icon: Globe, category: 'research' },
    
    // Company Intelligence Tools  
    { name: 'apollo_search_company', description: 'Company Intel', icon: Building2, category: 'business' },
    { name: 'apollo_find_companies', description: 'Company Discovery', icon: Crosshair, category: 'business' },
    { name: 'apollo_find_contacts', description: 'Contact Finder', icon: UserPlus, category: 'business' },
    
    // Content Analysis Tools
    { name: 'firecrawl_scrape', description: 'Page Scraper', icon: File, category: 'analysis' },
    { name: 'firecrawl_extract', description: 'Data Extractor', icon: Download, category: 'analysis' },
    { name: 'content_generation', description: 'Content Gen', icon: Edit3, category: 'creation' },
    { name: 'text_summarization', description: 'Summarizer', icon: Book, category: 'analysis' }
  ]

  // Subscribe to conversation store
  let messages = $derived($conversationStore.messages)
  let isLoading = $derived($conversationStore.isLoading)
  let toolCalls = $derived($conversationStore.toolCalls)
  
  // Function to check if a message is complete (not currently being streamed)
  function isMessageComplete(message: AGUIMessage): boolean {
    // A message is complete if:
    // 1. It's not loading AND
    // 2. The message has substantial content (more than just a few characters) AND
    // 3. It's not the most recent message being streamed
    const hasSubstantialContent = message.content && message.content.trim().length > 10
    const isLastMessage = messages[messages.length - 1]?.id === message.id
    
    // If it's not the last message, it's definitely complete
    if (!isLastMessage) {
      return hasSubstantialContent
    }
    
    // If it's the last message, only show button when not loading and has content
    return !isLoading && hasSubstantialContent
  }

  // Optimized ID generation using crypto.randomUUID when available
  function generateId(): string {
    if (typeof crypto !== 'undefined' && crypto.randomUUID) {
      return crypto.randomUUID()
    }
    return Math.random().toString(36).substring(2, 11)
  }

  async function handleSendMessage(messageText?: string) {
    const userMessage = messageText || input.trim()
    if (!userMessage || isLoading) return

    input = ''
    
    // Use AG-UI conversation manager to send message
    try {
      const endpoint = `${window.location.pathname}?stream=true`
      await sendMessage(endpoint, userMessage, {})
    } catch (error) {
      console.error('Failed to send AG-UI message:', error)
    }
  }

  function handleKeyDown(event: KeyboardEvent) {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault()
      handleSendMessage()
    }
    // Escape key to close sidebar
    if (event.key === 'Escape') {
      closeSidebar()
    }
  }

  // Debounced scroll optimization
  let scrollTimeout: ReturnType<typeof setTimeout>
  function optimizedScroll() {
    if (scrollTimeout) clearTimeout(scrollTimeout)
    scrollTimeout = setTimeout(() => {
      if (messagesContainer) {
        messagesContainer.scrollTop = messagesContainer.scrollHeight
      }
    }, 16) // ~60fps
  }

  function closeSidebar() {
    isOpen = false
    dispatch('close')
  }

  function formatContent(content: string): string {
    // Check if content looks like JSON
    const trimmedContent = content.trim()
    if (trimmedContent.startsWith('{') && trimmedContent.endsWith('}')) {
      try {
        const jsonData = JSON.parse(trimmedContent)
        
        // If it's structured research data, format it nicely
        if (jsonData.targetCompany || jsonData.metadata || jsonData.companies) {
          let formatted = ''
          
          if (jsonData.targetCompany) {
            formatted += `<strong>Company:</strong> ${jsonData.targetCompany.name}<br>`
            if (jsonData.targetCompany.description) {
              formatted += `<em>${jsonData.targetCompany.description}</em><br><br>`
            }
          }
          
          if (jsonData.metadata) {
            formatted += `<strong>Analysis Confidence:</strong> ${Math.round((jsonData.metadata.confidence_score || 0) * 100)}%<br>`
            if (jsonData.metadata.processing_time) {
              formatted += `<strong>Processing Time:</strong> ${jsonData.metadata.processing_time}<br><br>`
            }
          }
          
          if (jsonData.companies && Array.isArray(jsonData.companies)) {
            formatted += `<strong>Companies Analyzed:</strong> ${jsonData.companies.length}<br>`
            jsonData.companies.slice(0, 3).forEach((company: any) => {
              if (company.name) {
                formatted += `• ${company.name}`
                if (company.confidence_score) {
                  formatted += ` (${Math.round(company.confidence_score * 100)}% confidence)`
                }
                formatted += '<br>'
              }
            })
          }
          
          if (jsonData.market_analysis) {
            formatted += '<br><strong>Market Analysis:</strong><br>'
            if (jsonData.market_analysis.competitive_landscape) {
              const landscape = jsonData.market_analysis.competitive_landscape
              formatted += `• Competitors: ${landscape.total_competitors || 'N/A'}<br>`
              formatted += `• Market Dominance: ${Math.round((landscape.avg_dominance || 0) * 100)}%<br>`
            }
          }
          
          return formatted
        }
        
        // For other JSON, just prettify it
        return `<pre style="white-space: pre-wrap; font-family: monospace; font-size: 0.85em; background: rgba(0,0,0,0.05); padding: 8px; border-radius: 4px; overflow-x: auto;">${JSON.stringify(jsonData, null, 2)}</pre>`
      } catch (e) {
        // Not valid JSON, fall through to regular formatting
      }
    }
    
    // Basic HTML formatting for regular text
    return content
      .replace(/\n/g, '<br>')
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
  }

  // Export function to populate input field from example prompts
  export function populateInput(message: string) {
    input = message
    // Focus the input field for better UX
    const inputElement = document.getElementById('agui-message-input') as HTMLTextAreaElement
    if (inputElement) {
      inputElement.focus()
      // Move cursor to end of text
      inputElement.setSelectionRange(message.length, message.length)
    }
  }

  // Auto-scroll when messages change
  $effect(() => {
    if (messages.length > 0) {
      tick().then(() => optimizedScroll())
    }
  })
</script>

<style>
  .sidebar-container {
    width: 0;
    min-width: 0;
    overflow: hidden;
  }
  
  .sidebar-open {
    width: 400px;
    min-width: 400px;
  }
  
  .sidebar-closed {
    width: 0;
    min-width: 0;
  }

  @media (max-width: 768px) {
    .sidebar-open {
      position: fixed;
      top: 0;
      right: 0;
      height: 100vh;
      z-index: 50;
      width: 100vw;
      min-width: 100vw;
    }
  }
</style>

<!-- Sidebar Container - Responsive positioning based on screen size -->
<div
  class="sidebar-container flex flex-col bg-card border-l border-border shadow-lg transition-all duration-400 ease-out"
  class:sidebar-open={isOpen}
  class:sidebar-closed={!isOpen}
  role="complementary"
  aria-label="AG-UI Research Assistant Chat Sidebar"
  aria-live="polite"
>
  {#if isOpen}
    <!-- Header -->
    <header class="flex items-center justify-between p-4 border-b border-border bg-card">
      <div class="flex items-center gap-2">
        <Zap class="w-5 h-5 text-primary" aria-hidden="true" />
        <h2 class="linear-heading text-lg font-semibold" id="sidebar-title">Product Marketing Agent</h2>
      </div>
      <button
        onclick={closeSidebar}
        class="p-1 hover:bg-muted rounded transition-colors"
        aria-label="Close AG-UI Assistant sidebar"
      >
        <X class="w-4 h-4" />
      </button>
    </header>

    <!-- Tool Access Badges -->
    <section class="px-3 sm:px-4 py-2 border-b border-border" aria-labelledby="tools-heading">
      <h3
        id="tools-heading"
        class="text-xs text-muted-foreground mb-2"
        in:fade={{ duration: 300, delay: 200 }}
      >
        Available Research Tools
      </h3>
      <div
        class="flex flex-wrap gap-1 sm:gap-1.5"
        role="list"
        aria-label="Available research tools"
      >
        {#each aguiTools as tool, i}
          {@const IconComponent = tool.icon}
          <div
            in:scale={{ duration: 300, delay: 300 + (i * 100), easing: backOut }}
            class="transform hover:scale-105 transition-transform duration-200 flex-shrink-0"
            role="listitem"
          >
            <div class="inline-flex items-center gap-1 px-2 py-1 text-xs font-medium bg-muted rounded-md">
              <IconComponent class="w-3 h-3" />
              {tool.description}
            </div>
          </div>
        {/each}
      </div>
    </section>

    <!-- Messages -->
    <main
      bind:this={messagesContainer}
      class="flex-1 overflow-y-auto p-3 sm:p-4 space-y-3 sm:space-y-4"
      role="log"
      aria-label="Chat conversation"
      aria-live="polite"
      aria-atomic="false"
    >
      {#each messages as message}
        <div
          class="flex gap-3 {message.role === 'user' ? 'flex-row-reverse' : ''}"
          in:fly={{
            y: message.role === 'user' ? -20 : 20,
            duration: 400,
            easing: quintOut
          }}
        >
          <div class="w-8 h-8 flex-shrink-0 flex items-center justify-center rounded-full border-2 border-border bg-{message.role === 'user' ? 'primary' : 'secondary'}">
            {#if message.role === 'user'}
              <User class="w-4 h-4 text-primary-foreground" />
            {:else}
              <Bot class="w-4 h-4 text-secondary-foreground" />
            {/if}
          </div>
          <div class="flex-1 {message.role === 'user' ? 'text-right' : ''}">
            <div
              class="inline-block max-w-full p-3 rounded-lg {message.role === 'user' ? 'bg-primary text-primary-foreground' : 'bg-muted'} transform transition-all duration-200 hover:shadow-md"
              in:scale={{ duration: 300, delay: 200, easing: quintOut }}
            >
              {#if message.role === 'assistant'}
                <div class="prose prose-sm max-w-none" role="region" aria-label="Assistant response">
                  {@html formatContent(message.content)}
                </div>
                <!-- Add to Canvas Button - Only show when message is complete -->
                {#if isMessageComplete(message)}
                  <div class="mt-2 pt-2 border-t border-border/30">
                    <button
                      onclick={() => {
                        console.log('=== ADD TO CANVAS BUTTON CLICKED ===')
                        console.log('Message ID:', message.id)
                        console.log('Message content type:', typeof message.content)
                        try {
                          const result = addMessageToCanvas(message.content, `Agent Response`)
                          console.log(`Successfully added ${result} to canvas`)
                          
                          // Show visual feedback
                          const button = event.target
                          const originalText = button.textContent
                          button.textContent = '✓ Added!'
                          button.classList.add('bg-green-500/20', 'text-green-600')
                          setTimeout(() => {
                            button.textContent = originalText
                            button.classList.remove('bg-green-500/20', 'text-green-600')
                          }, 2000)
                        } catch (error) {
                          console.error('Failed to add to canvas:', error)
                        }
                      }}
                      class="inline-flex items-center gap-1 px-2 py-1 text-xs bg-primary/10 hover:bg-primary/20 text-primary rounded transition-colors"
                      title="Add this response to canvas"
                    >
                      <Plus class="w-3 h-3" />
                      Add to Canvas
                    </button>
                  </div>
                {/if}
              {:else}
                <p class="text-sm">{message.content}</p>
              {/if}
            </div>
            <div
              class="text-xs text-muted-foreground mt-1 {message.role === 'user' ? 'text-right' : ''}"
              in:fade={{ duration: 300, delay: 400 }}
              aria-label="Message sent at {message.timestamp.toLocaleTimeString()}"
            >
              {message.timestamp.toLocaleTimeString()}
            </div>
          </div>
        </div>
      {/each}

      <!-- Tool Calls Display -->
      {#if toolCalls.length > 0}
        <div
          class="bg-card border border-border rounded-lg p-4 shadow-sm"
          in:slide={{ duration: 300, delay: 200, axis: 'y' }}
          role="status"
          aria-label="Tool execution progress"
        >
          <div class="flex items-center gap-2 mb-3">
            <div class="w-2 h-2 bg-primary rounded-full animate-pulse"></div>
            <h4 class="font-medium text-sm">Tool Execution</h4>
          </div>

          <div class="space-y-2">
            {#each toolCalls as toolCall, i}
              <div
                class="flex items-center gap-3 p-3 rounded-md border border-border bg-card/50 transition-all duration-200 hover:bg-card"
                in:fly={{ x: -20, duration: 300, delay: 200 + (i * 100), easing: quintOut }}
              >
                <div class="flex-shrink-0">
                  {#if toolCall.status === 'completed'}
                    <div in:scale={{ duration: 400, easing: backOut }}>
                      <CheckCircle2
                        class="w-4 h-4 text-green-500 transition-all duration-300"
                        aria-label="Tool completed"
                      />
                    </div>
                  {:else if toolCall.status === 'pending'}
                    <div in:scale={{ duration: 300, easing: backOut }}>
                      <Loader2
                        class="w-4 h-4 animate-spin text-primary"
                        aria-label="Tool in progress"
                      />
                    </div>
                  {:else}
                    <div
                      class="w-4 h-4 border-2 border-red-500 rounded-full"
                      aria-label="Tool error"
                    ></div>
                  {/if}
                </div>
                
                <div class="flex-1 min-w-0">
                  <div class="font-medium text-sm {toolCall.status === 'completed' ? 'text-muted-foreground' : 'text-foreground'}">
                    {toolCall.name}
                  </div>
                  {#if toolCall.error}
                    <div class="text-xs text-red-500 mt-1 truncate">
                      Error: {toolCall.error}
                    </div>
                  {:else if Object.keys(toolCall.args).length > 0}
                    <div class="text-xs text-muted-foreground mt-1 truncate">
                      {JSON.stringify(toolCall.args).substring(0, 50)}...
                    </div>
                  {/if}
                </div>
              </div>
            {/each}
          </div>
        </div>
      {/if}

      <!-- Loading indicator -->
      {#if isLoading && toolCalls.length === 0}
        <div
          class="bg-card border border-border rounded-lg p-4 shadow-sm"
          in:slide={{ duration: 300, delay: 200, axis: 'y' }}
          role="status"
          aria-label="Processing request"
        >
          <div class="flex items-center gap-2">
            <Loader2 class="w-4 h-4 animate-spin text-primary" />
            <span class="text-sm text-muted-foreground">Processing your request...</span>
          </div>
        </div>
      {/if}
    </main>

    <!-- Input -->
    <footer
      class="p-3 sm:p-4 border-t border-border"
      in:slide={{ duration: 300, delay: 400, axis: 'y' }}
    >
      <form onsubmit={(e) => { e.preventDefault(); handleSendMessage(); }} class="flex gap-2">
        <label for="agui-message-input" class="sr-only">Enter your research query</label>
        <textarea
          id="agui-message-input"
          bind:value={input}
          onkeydown={handleKeyDown}
          placeholder="Ask me anything... I'll research it for you with real-time streaming!"
          class="flex-1 resize-none border border-input bg-background rounded-md px-3 py-2 text-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 transition-all duration-200 hover:border-primary/30 min-h-[44px] disabled:cursor-not-allowed disabled:opacity-50"
          rows="2"
          disabled={isLoading}
          aria-describedby="input-help"
          aria-label="Message input"
        ></textarea>
        <div id="input-help" class="sr-only">
          Press Enter to send message, Shift+Enter for new line
        </div>
        <button
          type="submit"
          disabled={!input.trim() || isLoading}
          class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-3 flex-shrink-0 min-w-[44px]"
          aria-label="{isLoading ? 'Processing request...' : 'Send message'}"
        >
          {#if isLoading}
            <Loader2 class="w-4 h-4 animate-spin" />
          {:else}
            <Send class="w-4 h-4" />
          {/if}
        </button>
      </form>
    </footer>
  {/if}
</div>
