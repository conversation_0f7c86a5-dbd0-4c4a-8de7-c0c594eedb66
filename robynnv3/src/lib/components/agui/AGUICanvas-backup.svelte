<script lang="ts">
  import { createEventDispatcher } from 'svelte'
  import { 
    Zap, 
    Search, 
    Building2, 
    Globe, 
    MessageSquare, 
    TrendingUp,
    FileSearch,
    BarChart3,
    Users,
    Lightbulb,
    Send,
    Plus,
    X
  } from 'lucide-svelte'
  import { conversationStore } from '$lib/agui/conversation-store'
  import { canvasStore, formatForPublishing, removeCanvasItem, clearCanvas } from '$lib/agui/canvas-store'
  import ResearchDisplay from '$lib/components/research/ResearchDisplay.svelte'
  import PublishingModal from '$lib/components/publishing/PublishingModal.svelte'

  const dispatch = createEventDispatcher()

  // Subscribe to conversation state
  let messages = $derived($conversationStore.messages)
  let isLoading = $derived($conversationStore.isLoading)
  let currentState = $derived($conversationStore.state)
  
  // Subscribe to canvas state
  let canvasItems = $derived($canvasStore.items)
  let hasResearchData = $derived($canvasStore.hasResearchData)
  let isPublishable = $derived($canvasStore.isPublishable)
  
  // Get research data for publishing
  let researchItem = $derived(canvasItems.find(item => item.type === 'research'))
  let publishableData = $derived(researchItem && researchItem.type === 'research' ? formatForPublishing(canvasItems) : null)
  
  // Publishing modal state
  let showPublishingModal = false

  // Example prompts for different use cases
  const examplePrompts = [
    {
      category: "Company Research",
      icon: Building2,
      color: "bg-blue-500/10 hover:bg-blue-500/20 border-blue-500/20",
      iconColor: "text-blue-500",
      prompts: [
        "Research Stripe and analyze their business model",
        "Find information about OpenAI's latest products",
        "Compare Notion vs Monday.com features and pricing"
      ]
    },
    {
      category: "Market Analysis", 
      icon: TrendingUp,
      color: "bg-green-500/10 hover:bg-green-500/20 border-green-500/20",
      iconColor: "text-green-500",
      prompts: [
        "Analyze the AI automation market trends",
        "Research SaaS pricing strategies in 2024",
        "Find competitors in the productivity tools space"
      ]
    },
    {
      category: "Technology Research",
      icon: Search,
      color: "bg-purple-500/10 hover:bg-purple-500/20 border-purple-500/20", 
      iconColor: "text-purple-500",
      prompts: [
        "Research latest developments in machine learning",
        "Find information about serverless architecture benefits",
        "Compare React vs Vue.js for enterprise applications"
      ]
    },
    {
      category: "Industry Insights",
      icon: Lightbulb,
      color: "bg-orange-500/10 hover:bg-orange-500/20 border-orange-500/20",
      iconColor: "text-orange-500", 
      prompts: [
        "Research fintech innovation trends",
        "Analyze the future of remote work tools",
        "Find case studies of successful digital transformations"
      ]
    }
  ]

  function handlePromptClick(prompt: string) {
    dispatch('sendMessage', { message: prompt })
  }
  
  function openPublishingModal() {
    if (publishableData) {
      showPublishingModal = true
    }
  }
  
  function handleClearCanvas() {
    clearCanvas()
  }

  // Get recent conversation insights
  let conversationInsights = $derived(messages.slice(-3).filter(m => m.role === 'assistant'))
</script>

<!-- Main Canvas Area -->
<div class="flex-1 bg-gradient-to-br from-background via-background to-muted/20 overflow-y-auto relative">
  <div class="max-w-6xl mx-auto p-6 space-y-8">
    
    <!-- Canvas Content (Dynamic based on canvas state) -->
    {#if hasResearchData && researchItem && researchItem.type === 'research'}
      <!-- Research Data Display -->
      <div class="space-y-6">
        <!-- Canvas Header with Actions -->
        <div class="bg-card border border-border rounded-xl p-4">
          <div class="flex items-center justify-between">
            <div>
              <h2 class="text-xl font-bold text-foreground">Product Marketing Research</h2>
              <p class="text-sm text-muted-foreground">Live research canvas • {canvasItems.length} items</p>
            </div>
            <div class="flex items-center gap-2">
              <button
                on:click={handleClearCanvas}
                class="inline-flex items-center gap-2 px-3 py-2 text-sm text-muted-foreground hover:text-foreground border border-border rounded-md hover:bg-secondary transition-colors"
                title="Clear canvas"
              >
                <X class="w-4 h-4" />
                Clear
              </button>
            </div>
          </div>
        </div>
        
        <!-- Research Display Component -->
        <ResearchDisplay researchData={researchItem.data} />
        
        <!-- Additional Canvas Items -->
        {#each canvasItems.filter(item => item.type !== 'research') as item}
          <div class="bg-card border border-border rounded-xl p-6 relative group">
            <button
              on:click={() => removeCanvasItem(item.id)}
              class="absolute top-3 right-3 p-1 opacity-0 group-hover:opacity-100 transition-opacity text-muted-foreground hover:text-destructive"
              title="Remove item"
            >
              <X class="w-4 h-4" />
            </button>
            
            {#if item.type === 'note'}
              <div>
                {#if item.title}
                  <h3 class="font-semibold mb-3">{item.title}</h3>
                {/if}
                <div class="prose prose-sm dark:prose-invert max-w-none">
                  {@html item.markdown.replace(/\n/g, '<br>')}
                </div>
                <div class="text-xs text-muted-foreground mt-3">
                  Added {item.timestamp.toLocaleString()}
                </div>
              </div>
            {:else if item.type === 'tool'}
              <div>
                <h3 class="font-semibold mb-3 flex items-center gap-2">
                  <BarChart3 class="w-4 h-4" />
                  Tool Result: {item.name}
                </h3>
                <div class="bg-muted/50 rounded-lg p-4 font-mono text-sm overflow-x-auto">
                  <pre>{JSON.stringify(item.result, null, 2)}</pre>
                </div>
                <div class="text-xs text-muted-foreground mt-3">
                  Executed {item.timestamp.toLocaleString()}
                </div>
              </div>
            {/if}
          </div>
        {/each}
      </div>
    {:else}
    
    <!-- Hero Section -->
    <div class="text-center space-y-4 py-8">
      <div class="inline-flex items-center justify-center w-16 h-16 bg-primary/10 rounded-2xl mb-4">
        <Zap class="w-8 h-8 text-primary" />
      </div>
      <h1 class="text-4xl font-bold bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent">
        Product Marketing Research Agent
      </h1>
      <p class="text-lg text-muted-foreground max-w-2xl mx-auto leading-relaxed">
        Experience the power of real-time AI research with advanced streaming capabilities. 
        Get comprehensive insights with live progress updates and tool execution visibility.
      </p>
    </div>

    <!-- Features Grid -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
      <div class="bg-card border border-border rounded-xl p-6 hover:shadow-lg transition-all duration-300">
        <div class="flex items-center gap-3 mb-3">
          <div class="w-10 h-10 bg-blue-500/10 rounded-lg flex items-center justify-center">
            <MessageSquare class="w-5 h-5 text-blue-500" />
          </div>
          <h3 class="font-semibold">Real-time Streaming</h3>
        </div>
        <p class="text-sm text-muted-foreground">
          Watch responses stream in real-time with token-level updates and live progress indicators.
        </p>
      </div>

      <div class="bg-card border border-border rounded-xl p-6 hover:shadow-lg transition-all duration-300">
        <div class="flex items-center gap-3 mb-3">
          <div class="w-10 h-10 bg-green-500/10 rounded-lg flex items-center justify-center">
            <BarChart3 class="w-5 h-5 text-green-500" />
          </div>
          <h3 class="font-semibold">Tool Execution</h3>
        </div>
        <p class="text-sm text-muted-foreground">
          Monitor tool calls as they happen with detailed progress and result visualization.
        </p>
      </div>

      <div class="bg-card border border-border rounded-xl p-6 hover:shadow-lg transition-all duration-300">
        <div class="flex items-center gap-3 mb-3">
          <div class="w-10 h-10 bg-purple-500/10 rounded-lg flex items-center justify-center">
            <Users class="w-5 h-5 text-purple-500" />
          </div>
          <h3 class="font-semibold">Interactive UI</h3>
        </div>
        <p class="text-sm text-muted-foreground">
          Engage with rich, interactive responses and get actionable insights from your research.
        </p>
      </div>
    </div>

    <!-- Current State Display -->
    {#if Object.keys(currentState).length > 0}
      <div class="bg-card border border-border rounded-xl p-6">
        <h3 class="font-semibold mb-4 flex items-center gap-2">
          <BarChart3 class="w-5 h-5 text-primary" />
          Current Session State
        </h3>
        <div class="bg-muted/50 rounded-lg p-4 font-mono text-sm overflow-x-auto">
          <pre>{JSON.stringify(currentState, null, 2)}</pre>
        </div>
      </div>
    {/if}

    <!-- Recent Insights -->
    {#if conversationInsights.length > 0}
      <div class="bg-card border border-border rounded-xl p-6">
        <h3 class="font-semibold mb-4 flex items-center gap-2">
          <Lightbulb class="w-5 h-5 text-orange-500" />
          Recent Research Insights
        </h3>
        <div class="space-y-3">
          {#each conversationInsights as insight, i}
            <div class="bg-muted/50 rounded-lg p-4 border-l-4 border-primary/30">
              <div class="text-sm">
                {insight.content.substring(0, 200)}...
              </div>
              <div class="text-xs text-muted-foreground mt-2">
                {insight.timestamp.toLocaleString()}
              </div>
            </div>
          {/each}
        </div>
      </div>
    {/if}

    <!-- Example Prompts -->
    <div class="space-y-6">
      <div class="text-center">
        <h2 class="text-2xl font-bold mb-2">Try These Research Examples</h2>
        <p class="text-muted-foreground">
          Click any prompt below to start a research session with real-time streaming
        </p>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {#each examplePrompts as category}
          {@const IconComponent = category.icon}
          <div class="bg-card border border-border rounded-xl p-6 hover:shadow-lg transition-all duration-300">
            <div class="flex items-center gap-3 mb-4">
              <div class="w-10 h-10 {category.color} rounded-lg flex items-center justify-center border">
                <IconComponent class="w-5 h-5 {category.iconColor}" />
              </div>
              <h3 class="font-semibold text-lg">{category.category}</h3>
            </div>
            
            <div class="space-y-2">
              {#each category.prompts as prompt}
                <button
                  onclick={() => handlePromptClick(prompt)}
                  disabled={isLoading}
                  class="w-full text-left p-3 rounded-lg border border-border hover:bg-muted/50 hover:border-primary/30 transition-all duration-200 text-sm disabled:opacity-50 disabled:cursor-not-allowed group"
                >
                  <div class="flex items-start gap-2">
                    <Search class="w-4 h-4 text-muted-foreground group-hover:text-primary transition-colors mt-0.5 flex-shrink-0" />
                    <span class="group-hover:text-foreground transition-colors">
                      {prompt}
                    </span>
                  </div>
                </button>
              {/each}
            </div>
          </div>
        {/each}
      </div>
    </div>

    <!-- Loading State -->
    {#if isLoading}
      <div class="bg-card border border-border rounded-xl p-6">
        <div class="flex items-center gap-3">
          <div class="w-6 h-6 border-2 border-primary border-t-transparent rounded-full animate-spin"></div>
          <span class="font-medium">Research in progress...</span>
        </div>
        <p class="text-sm text-muted-foreground mt-2">
          Your request is being processed with real-time streaming. Check the sidebar for live updates!
        </p>
      </div>
    {/if}

    <!-- Getting Started Guide -->
    {#if messages.length === 0 && !isLoading}
      <div class="bg-gradient-to-r from-primary/5 to-primary/10 border border-primary/20 rounded-xl p-6">
        <h3 class="font-semibold mb-4 flex items-center gap-2">
          <Zap class="w-5 h-5 text-primary" />
          Getting Started with AG-UI
        </h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <h4 class="font-medium mb-2">Real-time Features:</h4>
            <ul class="space-y-1 text-muted-foreground">
              <li>• Token-level response streaming</li>
              <li>• Live tool execution progress</li>
              <li>• Interactive state management</li>
              <li>• Rich error handling</li>
            </ul>
          </div>
          <div>
            <h4 class="font-medium mb-2">Available Tools:</h4>
            <ul class="space-y-1 text-muted-foreground">
              <li>• Web Search for general research</li>
              <li>• Enhanced Exa Search with AI</li>
              <li>• Apollo Company Intelligence</li>
              <li>• Real-time progress tracking</li>
            </ul>
          </div>
        </div>
        <div class="mt-4 p-3 bg-primary/5 rounded-lg border border-primary/20">
          <p class="text-sm text-primary/80">
            <strong>Tip:</strong> Open the sidebar to see real-time streaming in action! 
            Messages and tool executions will appear with live progress updates.
          </p>
        </div>
      </div>
    {/if}
    {/if} <!-- End else block for hero section -->
  </div>
  
  <!-- Floating Publish Button -->
  {#if isPublishable && publishableData}
    <button
      on:click={openPublishingModal}
      class="fixed bottom-6 right-6 z-40 bg-primary text-primary-foreground p-4 rounded-full shadow-lg hover:bg-primary/90 transition-all duration-200 hover:scale-105"
      title="Publish Research Report"
    >
      <Send class="w-6 h-6" />
    </button>
  {/if}
</div>

<!-- Publishing Modal -->
{#if publishableData}
  <PublishingModal
    bind:isOpen={showPublishingModal}
    researchData={publishableData}
    title="Publish Product Marketing Research"
    on:close={() => showPublishingModal = false}
  />
{/if}
