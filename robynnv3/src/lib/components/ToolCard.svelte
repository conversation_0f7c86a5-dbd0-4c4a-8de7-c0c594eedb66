<script lang="ts">
  import type { ToolInfo } from '$lib/services/tool-registry';
  import { createEventDispatcher } from 'svelte';
  
  export let tool: ToolInfo;
  
  const dispatch = createEventDispatcher<{
    toolClick: ToolInfo;
  }>();
  
  const categoryColors = {
    content: 'bg-blue-100 text-blue-800 border-blue-200',
    research: 'bg-green-100 text-green-800 border-green-200', 
    seo: 'bg-purple-100 text-purple-800 border-purple-200',
    apollo: 'bg-orange-100 text-orange-800 border-orange-200'
  };

  const categoryLabels = {
    content: 'Content',
    research: 'Research',
    seo: 'SEO',
    apollo: 'Apollo'
  };

  function handleClick() {
    dispatch('toolClick', tool);
  }
</script>

<button
  class="card-professional p-6 transition-all duration-200 hover-transform block w-full text-left"
  on:click={handleClick}
>
  <div class="space-y-4">
    <div class="flex items-center gap-3">
      <div class="w-12 h-12 bg-primary text-primary-foreground border border-border shadow-soft-sm flex items-center justify-center">
        <span class="text-xl">{tool.icon}</span>
      </div>
      <div class="flex-1">
        <h3 class="text-lg font-semibold text-foreground">{tool.name}</h3>
        <div class="flex items-center gap-2 mt-1">
          <span class="px-2 py-1 text-xs font-medium border {categoryColors[tool.category]}">
            {categoryLabels[tool.category]}
          </span>
          {#if tool.status === 'beta'}
            <span class="bg-yellow-100 text-yellow-800 border-yellow-200 px-2 py-1 text-xs font-medium border">
              BETA
            </span>
          {:else if tool.status === 'deprecated'}
            <span class="bg-red-100 text-red-800 border-red-200 px-2 py-1 text-xs font-medium border">
              DEPRECATED
            </span>
          {/if}
        </div>
      </div>
    </div>
    
    <p class="text-sm text-muted-foreground font-medium">{tool.description}</p>
    
    <div class="flex items-center gap-2 text-xs text-muted-foreground">
      <span class="font-medium">Used by:</span>
      {#each tool.usedBy.slice(0, 2) as agent, index}
        <span class="bg-muted text-muted-foreground px-2 py-1 border border-border">
          {agent.name}
        </span>
        {#if index === 0 && tool.usedBy.length > 1}
          <span class="text-muted-foreground">•</span>
        {/if}
      {/each}
      {#if tool.usedBy.length > 2}
        <span class="text-muted-foreground">+{tool.usedBy.length - 2} more</span>
      {/if}
    </div>
  </div>
</button>
