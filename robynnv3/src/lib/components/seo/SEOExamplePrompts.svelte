<script lang="ts">
  import { createEventDispatcher } from 'svelte'
  import {
    Search,
    TrendingUp,
    Target,
    BarChart,
    Lightbulb,
    Users
  } from 'lucide-svelte'

  const dispatch = createEventDispatcher()

  const examplePrompts = [
    {
      id: 'keyword-research',
      title: 'Keyword Research & Analysis',
      description: 'Comprehensive keyword research with search volume, difficulty, and opportunity analysis',
      icon: Search,
      prompt: 'Perform keyword research for "sustainable fashion" including search volume, difficulty, and related keywords',
      category: 'Keyword Research'
    },
    {
      id: 'competitor-analysis',
      title: 'Competitor SEO Analysis',
      description: 'Analyze competitor keywords, gaps, and opportunities in your niche',
      icon: TrendingUp,
      prompt: 'Analyze competitor keywords for the fitness industry, focusing on workout equipment',
      category: 'Competitive Intelligence'
    },
    {
      id: 'content-optimization',
      title: 'Content Optimization',
      description: 'Optimize content strategy with high-value, low-competition keywords',
      icon: Target,
      prompt: 'Find content optimization opportunities for a SaaS project management tool',
      category: 'Content Strategy'
    },
    {
      id: 'local-seo',
      title: 'Local SEO Research',
      description: 'Location-based keyword research and local search optimization',
      icon: Users,
      prompt: 'Research local SEO keywords for a dental practice in Austin, Texas',
      category: 'Local SEO'
    },
    {
      id: 'long-tail-discovery',
      title: 'Long-tail Keyword Discovery',
      description: 'Discover high-converting long-tail keywords with lower competition',
      icon: Lightbulb,
      prompt: 'Find long-tail keywords for "organic dog food" with commercial intent',
      category: 'Long-tail Strategy'
    },
    {
      id: 'technical-seo',
      title: 'Technical SEO Analysis',
      description: 'Technical SEO keyword research and optimization opportunities',
      icon: BarChart,
      prompt: 'Analyze technical SEO keywords for a web development agency',
      category: 'Technical SEO'
    }
  ]

  function selectPrompt(prompt: typeof examplePrompts[0]) {
    dispatch('selectPrompt', {
      message: prompt.prompt,
      title: prompt.title
    })
  }
</script>

<div class="max-w-4xl mx-auto">
  <!-- Header -->
  <div class="text-center mb-12">
    <h1 class="text-3xl font-bold mb-4">SEO Strategist Agent</h1>
    <p class="text-lg text-muted-foreground mb-2">
      Advanced keyword research and SEO analysis powered by AI
    </p>
    <p class="text-sm text-muted-foreground">
      Powered by DataForSEO API, web search intelligence, and competitive analysis
    </p>
  </div>

  <!-- Example Prompts Grid -->
  <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
    {#each examplePrompts as prompt}
      <button 
        on:click={() => selectPrompt(prompt)}
        class="card-professional p-6 text-left hover-transform transition-all duration-200 group"
      >
        <div class="flex items-start gap-4">
          <div class="flex-shrink-0 w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center group-hover:bg-primary/20 transition-colors">
            <svelte:component this={prompt.icon} class="w-6 h-6 text-primary" />
          </div>
          
          <div class="flex-1 min-w-0">
            <div class="flex items-center gap-2 mb-2">
              <h3 class="font-semibold text-lg">{prompt.title}</h3>
              <span class="text-xs px-2 py-1 bg-muted text-muted-foreground rounded">
                {prompt.category}
              </span>
            </div>
            
            <p class="text-sm text-muted-foreground mb-4 leading-relaxed">
              {prompt.description}
            </p>
            
            <div class="bg-muted/50 rounded-lg p-3 border-l-4 border-primary">
              <div class="text-xs text-muted-foreground mb-1">Example:</div>
              <div class="text-sm font-medium text-primary">"{prompt.prompt}"</div>
            </div>
          </div>
        </div>
      </button>
    {/each}
  </div>

  <!-- Getting Started Tips -->
  <div class="bg-card border border-border rounded-lg p-6">
    <h3 class="font-semibold mb-3">Getting Started with SEO Analysis</h3>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
      <div class="flex items-start gap-2">
        <div class="w-6 h-6 bg-primary/10 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
          <span class="text-xs font-bold text-primary">1</span>
        </div>
        <div>
          <div class="font-medium mb-1">Enter Keywords</div>
          <div class="text-muted-foreground">Provide seed keywords or topics for analysis</div>
        </div>
      </div>
      
      <div class="flex items-start gap-2">
        <div class="w-6 h-6 bg-primary/10 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
          <span class="text-xs font-bold text-primary">2</span>
        </div>
        <div>
          <div class="font-medium mb-1">Choose Analysis Type</div>
          <div class="text-muted-foreground">Select chat, niche discovery, or gap analysis</div>
        </div>
      </div>
      
      <div class="flex items-start gap-2">
        <div class="w-6 h-6 bg-primary/10 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
          <span class="text-xs font-bold text-primary">3</span>
        </div>
        <div>
          <div class="font-medium mb-1">Get Insights</div>
          <div class="text-muted-foreground">Receive detailed SEO analysis and recommendations</div>
        </div>
      </div>
    </div>
  </div>

  <!-- Features Overview -->
  <div class="mt-8 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
    <div class="bg-card border border-border rounded-lg p-4">
      <div class="flex items-center gap-2 mb-2">
        <Search class="w-5 h-5 text-primary" />
        <h4 class="font-medium">Keyword Research</h4>
      </div>
      <p class="text-sm text-muted-foreground">
        Get search volume, difficulty scores, and related keywords for any topic
      </p>
    </div>
    
    <div class="bg-card border border-border rounded-lg p-4">
      <div class="flex items-center gap-2 mb-2">
        <TrendingUp class="w-5 h-5 text-primary" />
        <h4 class="font-medium">Competitor Analysis</h4>
      </div>
      <p class="text-sm text-muted-foreground">
        Discover keyword gaps and opportunities compared to competitors
      </p>
    </div>
    
    <div class="bg-card border border-border rounded-lg p-4">
      <div class="flex items-center gap-2 mb-2">
        <Target class="w-5 h-5 text-primary" />
        <h4 class="font-medium">Niche Discovery</h4>
      </div>
      <p class="text-sm text-muted-foreground">
        Find untapped keyword opportunities in your industry niche
      </p>
    </div>
  </div>
</div>
