<script lang="ts">
  import { createEventDispatcher, afterUpdate } from 'svelte'
  import { Search, Building, FileText, BarChart } from 'lucide-svelte'
  import SEOResultsDisplay from './SEOResultsDisplay.svelte'

  export let leftSidebarCollapsed: boolean = false
  export let rightSidebarCollapsed: boolean = false
  export let messages: any[] = []
  export let showExamples: boolean = true

  const dispatch = createEventDispatcher()

  // Debug logging for conditional rendering
  $: {
    console.log("🎯 SEOCanvas - showExamples:", showExamples, "messages.length:", messages.length)
    console.log("🎯 SEOCanvas - Should show examples:", showExamples && messages.length === 0)
    console.log("🎯 SEOCanvas - Should show results:", !showExamples || messages.length > 0)
  }

  let canvasContainer: HTMLElement
  let shouldAutoScroll = true

  // Auto-scroll to bottom when new content appears
  afterUpdate(() => {
    if (shouldAutoScroll && canvasContainer) {
      canvasContainer.scrollTop = canvasContainer.scrollHeight
    }
  })

  function handleScroll() {
    if (!canvasContainer) return

    const { scrollTop, scrollHeight, clientHeight } = canvasContainer
    const isAtBottom = scrollTop + clientHeight >= scrollHeight - 10
    shouldAutoScroll = isAtBottom
  }

  function handleCompanyAnalysis() {
    dispatch('selectExample', {
      message: "Analyze my company: TechStartup Inc. (techstartup.com) - we provide AI-powered business automation tools for small businesses"
    })
  }

  function handleKeywordStrategy() {
    dispatch('selectExample', {
      message: "Develop a comprehensive keyword strategy for AI automation tools targeting small businesses"
    })
  }

  function handleCompetitorGapAnalysis() {
    dispatch('selectExample', {
      message: "Analyze keyword gaps between my domain techstartup.com and competitors zapier.com, monday.com"
    })
  }

  function handleContentStrategy() {
    dispatch('selectExample', {
      message: "Generate content strategy recommendations based on keyword research for AI automation business"
    })
  }

  // Compute dynamic width based on sidebar states
  $: canvasWidth = (() => {
    let width = 'calc(100vw'
    if (!leftSidebarCollapsed) width += ' - 256px' // 16rem = 256px for left sidebar
    if (!rightSidebarCollapsed) width += ' - 400px' // 400px for right sidebar
    return width + ')'
  })()
</script>

<div
  class="flex-1 flex flex-col h-screen overflow-hidden bg-background transition-all duration-400 ease-out"
  style="width: {canvasWidth};"
  bind:this={canvasContainer}
  onscroll={handleScroll}
>
  <!-- Main Content Area -->
  <div class="flex-1 overflow-y-auto p-4 md:p-8">
    {#if showExamples && messages.length === 0}
      <!-- Example Prompts - Enhanced 4-Phase SEO Workflow -->
      <div class="max-w-4xl mx-auto">
        <!-- SEO Agent Title -->
        <div class="text-center mb-12">
          <h1 class="text-4xl font-bold text-foreground mb-4">SEO Agent</h1>
          <p class="text-lg text-muted-foreground">
            Optimize your search engine presence with AI-powered SEO insights
          </p>
        </div>

        <div class="space-y-6">
          <div class="text-center mb-8">
            <h2 class="text-xl font-semibold text-foreground mb-2">Enhanced SEO Workflow</h2>
            <p class="text-sm text-muted-foreground">
              Complete 4-phase SEO analysis: Company Intelligence → Keyword Strategy → Gap Analysis → Content Strategy
            </p>
          </div>

          <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-4">
          <!-- Phase 1: Company Intelligence -->
          <button
            class="bg-card border border-border rounded-lg p-5 shadow-sm hover:shadow-md transition-shadow cursor-pointer group text-left w-full"
            onclick={handleCompanyAnalysis}
          >
            <div class="flex items-center justify-center w-10 h-10 bg-blue-500/10 rounded-lg mb-3 group-hover:bg-blue-500/20 transition-colors">
              <Building class="w-5 h-5 text-blue-500" />
            </div>
            <div class="text-xs text-blue-500 font-medium mb-1">PHASE 1</div>
            <h3 class="text-base font-semibold mb-2">Company Intelligence</h3>
            <p class="text-muted-foreground text-xs">
              Analyze company profile, competitors, and market positioning
            </p>
          </button>

          <!-- Phase 2: Keyword Strategy -->
          <button
            class="bg-card border border-border rounded-lg p-5 shadow-sm hover:shadow-md transition-shadow cursor-pointer group text-left w-full"
            onclick={handleKeywordStrategy}
          >
            <div class="flex items-center justify-center w-10 h-10 bg-green-500/10 rounded-lg mb-3 group-hover:bg-green-500/20 transition-colors">
              <Search class="w-5 h-5 text-green-500" />
            </div>
            <div class="text-xs text-green-500 font-medium mb-1">PHASE 2</div>
            <h3 class="text-base font-semibold mb-2">Keyword Strategy</h3>
            <p class="text-muted-foreground text-xs">
              Research high-value keywords with commercial intent
            </p>
          </button>

          <!-- Phase 3: Gap Analysis -->
          <button
            class="bg-card border border-border rounded-lg p-5 shadow-sm hover:shadow-md transition-shadow cursor-pointer group text-left w-full"
            onclick={handleCompetitorGapAnalysis}
          >
            <div class="flex items-center justify-center w-10 h-10 bg-orange-500/10 rounded-lg mb-3 group-hover:bg-orange-500/20 transition-colors">
              <BarChart class="w-5 h-5 text-orange-500" />
            </div>
            <div class="text-xs text-orange-500 font-medium mb-1">PHASE 3</div>
            <h3 class="text-base font-semibold mb-2">Gap Analysis</h3>
            <p class="text-muted-foreground text-xs">
              Identify keyword opportunities vs competitors
            </p>
          </button>

          <!-- Phase 4: Content Strategy -->
          <button
            class="bg-card border border-border rounded-lg p-5 shadow-sm hover:shadow-md transition-shadow cursor-pointer group text-left w-full"
            onclick={handleContentStrategy}
          >
            <div class="flex items-center justify-center w-10 h-10 bg-purple-500/10 rounded-lg mb-3 group-hover:bg-purple-500/20 transition-colors">
              <FileText class="w-5 h-5 text-purple-500" />
            </div>
            <div class="text-xs text-purple-500 font-medium mb-1">PHASE 4</div>
            <h3 class="text-base font-semibold mb-2">Content Strategy</h3>
            <p class="text-muted-foreground text-xs">
              Generate actionable content recommendations
            </p>
          </button>
          </div>
        </div>
      </div>
    {:else}
      <!-- SEO Results Display -->
      <div class="max-w-4xl mx-auto space-y-6">
        {#if messages.length > 0}
          <div class="space-y-4">
            {#each messages as message}
              {#if message.role === 'assistant'}
                <div class="bg-card border border-border rounded-lg p-6 shadow-sm">
                  <div class="prose prose-sm max-w-none">
                    {@html message.content}
                  </div>

                  {#if message.data}
                    <!-- SEO Results Display -->
                    <div class="mt-6">
                      <SEOResultsDisplay seoData={message.data} />
                    </div>
                  {/if}
                </div>
              {/if}
            {/each}
          </div>
        {/if}
      </div>
    {/if}
  </div>
</div>
