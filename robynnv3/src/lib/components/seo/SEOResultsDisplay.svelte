<script lang="ts">
  import { fade, fly } from 'svelte/transition'
  import { quintOut } from 'svelte/easing'
  import {
    Download,
    Share2,
    Bar<PERSON>hart,
    TrendingUp,
    Target,
    Search,
    AlertCircle,
    CheckCircle2,
    Building,
    FileText,
    ExternalLink,
    Users,
    Briefcase,
    Edit3,
    X
  } from 'lucide-svelte'

  export let seoData: any = {}

  // Extract data from seoData
  $: keywords = seoData.keywords || []
  $: analysis = seoData.analysis || {}
  $: metadata = seoData.metadata || {}
  $: recommendations = seoData.recommendations || []
  $: companyProfile = seoData.companyProfile || null
  $: contentStrategy = seoData.contentStrategy || null

  // Analysis status indicators
  $: isPartialResult = metadata?.isPartial || false
  $: isFallbackResult = metadata?.isFallback || false
  $: analysisTimestamp = metadata?.timestamp || null

  // Modal state for Generate Outline
  let showOutlineModal = false
  let selectedRecommendations: string[] = []

  // Parse recommendations into actionable items
  $: parsedRecommendations = parseRecommendationsText(recommendations)

  function formatNumber(num: number): string {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M'
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K'
    }
    return num.toString()
  }

  function getDifficultyColor(difficulty: number): string {
    if (difficulty <= 30) return 'text-green-600'
    if (difficulty <= 60) return 'text-yellow-600'
    return 'text-red-600'
  }

  function getDifficultyLabel(difficulty: number): string {
    if (difficulty <= 30) return 'Easy'
    if (difficulty <= 60) return 'Medium'
    return 'Hard'
  }

  function handleGenerateOutline(recommendation: any) {
    // Create a detailed prompt for outline generation
    const outlinePrompt = `Generate a comprehensive content outline for: "${recommendation.content_title}"

Content Angle: ${recommendation.content_angle}
Target Keywords: ${recommendation.target_keywords.join(', ')}
Expected Impact: ${recommendation.expected_impact}

Please create a detailed article outline with:
1. Compelling headline variations
2. Introduction hook
3. Main sections with subheadings
4. Key points to cover in each section
5. Call-to-action suggestions
6. SEO optimization tips`

    // For now, show an alert with the prompt (can be replaced with actual outline generation)
    alert(`Outline generation requested for: "${recommendation.content_title}"\n\nThis will be integrated with the content generation system.\n\nPrompt: ${outlinePrompt}`)
  }

  function getCompetitionColor(competition: string): string {
    if (!competition) return 'text-muted-foreground'

    switch (competition.toLowerCase()) {
      case 'low': return 'text-green-600'
      case 'medium': return 'text-yellow-600'
      case 'high': return 'text-red-600'
      default: return 'text-muted-foreground'
    }
  }

  function exportData() {
    const exportData = {
      keywords,
      analysis,
      metadata: {
        ...metadata,
        exportDate: new Date().toISOString(),
        exportVersion: '1.0-seo-strategist'
      }
    }

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `seo-analysis-${new Date().toISOString().split('T')[0]}.json`
    a.click()
    URL.revokeObjectURL(url)
  }

  function formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString()
  }

  // Parse recommendations text into actionable items
  function parseRecommendationsText(text: string): Array<{id: string, title: string, description: string}> {
    if (!text || typeof text !== 'string') return []

    const items: Array<{id: string, title: string, description: string}> = []
    const lines = text.split('\n').filter(line => line.trim())

    let currentItem: {id: string, title: string, description: string} | null = null

    for (const line of lines) {
      const trimmedLine = line.trim()

      // Look for bullet points, numbered items, or bold text that could be recommendations
      if (trimmedLine.match(/^[-*•]\s*\*\*.*\*\*/) || trimmedLine.match(/^\d+\.\s*\*\*.*\*\*/)) {
        // Save previous item if exists
        if (currentItem) {
          items.push(currentItem)
        }

        // Extract title from bold text
        const titleMatch = trimmedLine.match(/\*\*(.*?)\*\*/)
        const title = titleMatch ? titleMatch[1] : trimmedLine.replace(/^[-*•\d.\s]*/, '')

        currentItem = {
          id: `rec-${items.length}`,
          title: title,
          description: trimmedLine.replace(/\*\*(.*?)\*\*/, '').replace(/^[-*•\d.\s]*/, '').trim()
        }
      } else if (currentItem && trimmedLine.startsWith('-')) {
        // Add to current item's description
        currentItem.description += ' ' + trimmedLine.replace(/^-\s*/, '')
      }
    }

    // Add the last item
    if (currentItem) {
      items.push(currentItem)
    }

    // If no structured items found, create some default ones
    if (items.length === 0 && text.trim()) {
      items.push(
        {
          id: 'rec-0',
          title: 'SEO Strategy Implementation',
          description: 'Implement comprehensive SEO strategy based on analysis'
        },
        {
          id: 'rec-1',
          title: 'Content Optimization',
          description: 'Optimize existing content for target keywords'
        },
        {
          id: 'rec-2',
          title: 'Technical SEO Improvements',
          description: 'Address technical SEO issues and improvements'
        }
      )
    }

    return items
  }

  // Modal functions
  function openOutlineModal() {
    showOutlineModal = true
    selectedRecommendations = []
  }

  function closeOutlineModal() {
    showOutlineModal = false
    selectedRecommendations = []
  }

  function toggleRecommendation(id: string) {
    if (selectedRecommendations.includes(id)) {
      selectedRecommendations = selectedRecommendations.filter(recId => recId !== id)
    } else {
      selectedRecommendations = [...selectedRecommendations, id]
    }
  }

  function createOutline() {
    // Placeholder functionality - just close modal for now
    console.log('Creating outline for recommendations:', selectedRecommendations)
    closeOutlineModal()
  }

  function handleRefineAnalysis() {
    console.log("Refining analysis...")
    // TODO: Implement analysis refinement
  }

  function handleRetryAnalysis() {
    console.log("Retrying analysis...")
    // TODO: Implement analysis retry
  }
</script>

{#if Object.keys(seoData).length > 0}
  <div
    class="space-y-6"
    in:fade={{ duration: 600, easing: quintOut }}
  >
    <!-- Analysis Status Indicator -->
    {#if isPartialResult || isFallbackResult}
      <div
        class="card-professional p-4 border-l-4 {isFallbackResult ? 'border-orange-500 bg-orange-50' : 'border-blue-500 bg-blue-50'}"
        in:fly={{ y: -20, duration: 300, easing: quintOut }}
      >
        <div class="flex items-start justify-between">
          <div class="flex items-center gap-3">
            {#if isFallbackResult}
              <AlertCircle class="w-5 h-5 text-orange-600" />
              <div>
                <h3 class="font-medium text-orange-800">Fallback Analysis Results</h3>
                <p class="text-sm text-orange-700 mt-1">
                  Primary analysis timed out. These results were generated using fallback methods and may be limited.
                </p>
              </div>
            {:else if isPartialResult}
              <AlertCircle class="w-5 h-5 text-blue-600" />
              <div>
                <h3 class="font-medium text-blue-800">Partial Analysis Results</h3>
                <p class="text-sm text-blue-700 mt-1">
                  Analysis was interrupted. Some tools completed successfully, but results may be incomplete.
                </p>
              </div>
            {/if}
          </div>

          <div class="flex gap-2">
            <button
              onclick={handleRefineAnalysis}
              class="px-3 py-1.5 text-sm bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
            >
              Refine Analysis
            </button>
            <button
              onclick={handleRetryAnalysis}
              class="px-3 py-1.5 text-sm bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
            >
              Retry Full Analysis
            </button>
          </div>
        </div>

        {#if analysisTimestamp}
          <p class="text-xs text-muted-foreground mt-2">
            Generated: {new Date(analysisTimestamp).toLocaleString()}
          </p>
        {/if}
      </div>
    {/if}

    <!-- Analysis Header -->
    <div 
      class="card-professional p-6"
      in:fly={{ y: -20, duration: 400, easing: quintOut }}
    >
      <div class="flex items-start justify-between">
        <div class="flex-1">
          <h2 class="text-xl font-bold text-foreground mb-2">
            SEO Analysis Results
          </h2>
          <div class="flex flex-wrap items-center gap-4 text-sm text-muted-foreground">
            {#if metadata.analysisDate}
              <div class="flex items-center gap-1">
                <BarChart class="w-4 h-4" />
                <span>Generated {formatDate(metadata.analysisDate)}</span>
              </div>
            {/if}
            {#if keywords.length > 0}
              <div class="flex items-center gap-1">
                <Search class="w-4 h-4" />
                <span>{keywords.length} keywords analyzed</span>
              </div>
            {/if}
          </div>
        </div>
        
        <div class="flex items-center gap-2">
          <button
            onclick={exportData}
            class="inline-flex items-center gap-2 px-3 py-2 text-sm font-medium bg-secondary text-secondary-foreground hover:bg-secondary/80 border border-border rounded-md transition-colors"
          >
            <Download class="w-4 h-4" />
            Export
          </button>
        </div>
      </div>
    </div>

    <!-- Company Profile Card -->
    {#if companyProfile}
      <div
        class="card-professional p-6"
        in:fly={{ y: -20, duration: 400, delay: 50, easing: quintOut }}
      >
        <div class="flex items-center gap-2 mb-4">
          <Building class="w-5 h-5 text-primary" />
          <h3 class="text-lg font-semibold">Company Intelligence</h3>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Company Overview -->
          <div class="space-y-4">
            <div>
              <h4 class="font-medium text-sm text-muted-foreground mb-2">Company Overview</h4>
              <p class="text-sm">{companyProfile.overview}</p>
            </div>

            {#if companyProfile.domain}
              <div>
                <h4 class="font-medium text-sm text-muted-foreground mb-2">Domain</h4>
                <a
                  href="https://{companyProfile.domain}"
                  target="_blank"
                  class="text-sm text-primary hover:underline flex items-center gap-1"
                >
                  {companyProfile.domain}
                  <ExternalLink class="w-3 h-3" />
                </a>
              </div>
            {/if}

            <div>
              <h4 class="font-medium text-sm text-muted-foreground mb-2">Business Model</h4>
              <span class="inline-flex items-center px-2 py-1 bg-secondary text-secondary-foreground text-xs rounded">
                {companyProfile.business_model}
              </span>
            </div>
          </div>

          <!-- Market Intelligence -->
          <div class="space-y-4">
            {#if companyProfile.products_services.length > 0}
              <div>
                <h4 class="font-medium text-sm text-muted-foreground mb-2">Products & Services</h4>
                <div class="flex flex-wrap gap-1">
                  {#each companyProfile.products_services.slice(0, 4) as item}
                    <span class="px-2 py-1 bg-muted text-muted-foreground text-xs rounded">
                      {item}
                    </span>
                  {/each}
                  {#if companyProfile.products_services.length > 4}
                    <span class="px-2 py-1 bg-muted text-muted-foreground text-xs rounded">
                      +{companyProfile.products_services.length - 4} more
                    </span>
                  {/if}
                </div>
              </div>
            {/if}

            {#if companyProfile.competitors.length > 0}
              <div>
                <h4 class="font-medium text-sm text-muted-foreground mb-2">Key Competitors</h4>
                <div class="space-y-1">
                  {#each companyProfile.competitors as competitor}
                    <div class="text-sm flex items-center gap-2">
                      <Briefcase class="w-3 h-3 text-muted-foreground" />
                      {competitor}
                    </div>
                  {/each}
                </div>
              </div>
            {/if}

            <div>
              <h4 class="font-medium text-sm text-muted-foreground mb-2">Target Audience</h4>
              <div class="flex items-center gap-2 text-sm">
                <Users class="w-4 h-4 text-muted-foreground" />
                <span>{companyProfile.target_audience}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    {/if}

    <!-- Keywords Table -->
    {#if keywords.length > 0}
      <div 
        class="card-professional p-6"
        in:fly={{ y: 20, duration: 400, delay: 100, easing: quintOut }}
      >
        <div class="flex items-center gap-2 mb-4">
          <Target class="w-5 h-5 text-primary" />
          <h3 class="text-lg font-semibold">Keyword Analysis</h3>
        </div>
        
        <div class="overflow-x-auto">
          <table class="w-full text-sm">
            <thead>
              <tr class="border-b border-border">
                <th class="text-left py-3 px-2 font-medium text-muted-foreground">Keyword</th>
                <th class="text-right py-3 px-2 font-medium text-muted-foreground">Volume</th>
                <th class="text-right py-3 px-2 font-medium text-muted-foreground">Difficulty</th>
                <th class="text-center py-3 px-2 font-medium text-muted-foreground">Competition</th>
                <th class="text-right py-3 px-2 font-medium text-muted-foreground">CPC</th>
                {#if keywords.some(k => k.opportunity_score)}
                  <th class="text-right py-3 px-2 font-medium text-muted-foreground">Opportunity</th>
                {/if}
              </tr>
            </thead>
            <tbody>
              {#each keywords as keyword, i}
                <tr 
                  class="border-b border-border/50 hover:bg-muted/50 transition-colors"
                  in:fly={{ x: -20, duration: 300, delay: 150 + (i * 50), easing: quintOut }}
                >
                  <td class="py-3 px-2 font-medium">{keyword.keyword}</td>
                  <td class="py-3 px-2 text-right">{formatNumber(keyword.search_volume)}</td>
                  <td class="py-3 px-2 text-right">
                    <span class="{getDifficultyColor(keyword.difficulty)}">
                      {keyword.difficulty} ({getDifficultyLabel(keyword.difficulty)})
                    </span>
                  </td>
                  <td class="py-3 px-2 text-center">
                    <span class="{getCompetitionColor(keyword.competition)} capitalize">
                      {keyword.competition || 'N/A'}
                    </span>
                  </td>
                  <td class="py-3 px-2 text-right">${keyword.cpc.toFixed(2)}</td>
                  {#if keyword.opportunity_score}
                    <td class="py-3 px-2 text-right">
                      <span class="text-primary font-medium">{keyword.opportunity_score}/100</span>
                    </td>
                  {/if}
                </tr>
              {/each}
            </tbody>
          </table>
        </div>
      </div>
    {/if}

    <!-- Analysis Summary -->
    {#if Object.keys(analysis).length > 0}
      <div 
        class="card-professional p-6"
        in:fly={{ y: 20, duration: 400, delay: 200, easing: quintOut }}
      >
        <div class="flex items-center gap-2 mb-4">
          <TrendingUp class="w-5 h-5 text-primary" />
          <h3 class="text-lg font-semibold">Analysis Summary</h3>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          {#if analysis.totalVolume}
            <div class="bg-muted/50 rounded-lg p-4">
              <div class="text-2xl font-bold text-primary">{formatNumber(analysis.totalVolume)}</div>
              <div class="text-sm text-muted-foreground">Total Search Volume</div>
            </div>
          {/if}
          
          {#if analysis.avgDifficulty}
            <div class="bg-muted/50 rounded-lg p-4">
              <div class="text-2xl font-bold {getDifficultyColor(analysis.avgDifficulty)}">
                {analysis.avgDifficulty.toFixed(1)}
              </div>
              <div class="text-sm text-muted-foreground">Avg Difficulty</div>
            </div>
          {/if}
          
          {#if analysis.avgCpc}
            <div class="bg-muted/50 rounded-lg p-4">
              <div class="text-2xl font-bold text-primary">${analysis.avgCpc.toFixed(2)}</div>
              <div class="text-sm text-muted-foreground">Avg CPC</div>
            </div>
          {/if}
          
          {#if analysis.opportunityCount}
            <div class="bg-muted/50 rounded-lg p-4">
              <div class="text-2xl font-bold text-green-600">{analysis.opportunityCount}</div>
              <div class="text-sm text-muted-foreground">Opportunities</div>
            </div>
          {/if}
        </div>

        {#if analysis.insights}
          <div class="prose prose-sm max-w-none">
            <h4 class="text-base font-semibold mb-2">Key Insights</h4>
            <div class="text-muted-foreground">
              {@html analysis.insights}
            </div>
          </div>
        {/if}
      </div>
    {/if}

    <!-- Recommendations -->
    {#if recommendations && recommendations.trim()}
      <div
        class="card-professional p-6"
        in:fly={{ y: 20, duration: 400, delay: 300, easing: quintOut }}
      >
        <div class="flex items-center gap-2 mb-4">
          <CheckCircle2 class="w-5 h-5 text-primary" />
          <h3 class="text-lg font-semibold">Recommendations</h3>
        </div>

        <div class="prose prose-sm max-w-none mb-4">
          <div class="text-sm text-foreground whitespace-pre-wrap">{recommendations}</div>
        </div>

        <!-- Generate Outline Button -->
        <div class="flex justify-end pt-4 border-t border-border">
          <button
            onclick={openOutlineModal}
            class="flex items-center gap-2 px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors text-sm font-medium"
          >
            <Edit3 class="w-4 h-4" />
            Generate Outline
          </button>
        </div>
      </div>
    {/if}

    <!-- Content Strategy Recommendations -->
    {#if contentStrategy && contentStrategy.recommendations.length > 0}
      <div
        class="card-professional p-6"
        in:fly={{ y: 20, duration: 400, delay: 400, easing: quintOut }}
      >
        <div class="flex items-center gap-2 mb-4">
          <FileText class="w-5 h-5 text-primary" />
          <h3 class="text-lg font-semibold">Content Strategy</h3>
        </div>

        {#if contentStrategy.overall_strategy}
          <div class="mb-6 p-4 bg-muted/50 rounded-lg">
            <h4 class="font-medium text-sm mb-2">Strategy Overview</h4>
            <p class="text-sm text-muted-foreground">{contentStrategy.overall_strategy}</p>
          </div>
        {/if}

        <div class="space-y-4">
          <h4 class="font-medium text-sm text-muted-foreground mb-3">
            Recommended Articles ({contentStrategy.recommendations.length})
          </h4>

          {#each contentStrategy.recommendations as recommendation, i}
            <div
              class="border border-border rounded-lg p-4 hover:bg-muted/50 transition-colors"
              in:fly={{ x: -20, duration: 300, delay: 450 + (i * 100), easing: quintOut }}
            >
              <div class="flex items-start justify-between gap-4">
                <div class="flex-1 space-y-3">
                  <div>
                    <h5 class="font-medium text-base mb-1">{recommendation.title}</h5>
                    <p class="text-sm text-muted-foreground">{recommendation.content_angle}</p>
                  </div>

                  <div class="flex flex-wrap items-center gap-4 text-xs">
                    <div class="flex items-center gap-1">
                      <Target class="w-3 h-3 text-muted-foreground" />
                      <span class="text-muted-foreground">Keywords:</span>
                      <div class="flex flex-wrap gap-1">
                        {#each recommendation.target_keywords.slice(0, 3) as keyword}
                          <span class="px-1.5 py-0.5 bg-primary/10 text-primary rounded text-xs">
                            {keyword}
                          </span>
                        {/each}
                        {#if recommendation.target_keywords.length > 3}
                          <span class="text-muted-foreground">+{recommendation.target_keywords.length - 3}</span>
                        {/if}
                      </div>
                    </div>
                  </div>

                  <div class="flex items-center justify-between">
                    <div class="flex items-center gap-4 text-xs text-muted-foreground">
                      <span>Expected Impact: {recommendation.expected_impact}</span>
                      <span class="flex items-center gap-1">
                        Priority:
                        <span class="font-medium text-primary">{recommendation.priority_score}/10</span>
                      </span>
                    </div>
                  </div>
                </div>

                <div class="flex-shrink-0">
                  <button
                    class="inline-flex items-center gap-2 px-3 py-2 text-sm font-medium bg-primary text-primary-foreground hover:bg-primary/90 rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                    onclick={() => handleGenerateOutline(recommendation)}
                    title="Generate content outline for this topic"
                  >
                    <FileText class="w-4 h-4" />
                    Generate Outline
                  </button>
                </div>
              </div>
            </div>
          {/each}
        </div>
      </div>
    {/if}

    <!-- Data Attribution Footer -->
    <div
      class="card-professional p-4 text-center"
      in:fly={{ y: 20, duration: 400, delay: 400, easing: quintOut }}
    >
      <div class="text-xs text-muted-foreground space-y-1">
        <p>
          SEO analysis powered by {metadata.dataSources ? metadata.dataSources.join(' • ') : 'DataForSEO API • Web Search Intelligence'}
        </p>
        {#if metadata.apiCalls}
          <p>
            API calls: {Object.entries(metadata.apiCalls).map(([key, value]) => `${key}: ${value}`).join(' • ')}
          </p>
        {/if}
        <p class="text-xs">
          Generated on {formatDate(metadata.analysisDate || new Date().toISOString())}
        </p>
      </div>
    </div>
  </div>
{:else}
  <div class="text-center py-12">
    <Search class="w-12 h-12 text-muted-foreground mx-auto mb-4" />
    <h3 class="text-lg font-semibold mb-2">No SEO Data Available</h3>
    <p class="text-muted-foreground">
      Start an SEO analysis to see keyword research and competitive insights here.
    </p>
  </div>
{/if}

<!-- Generate Outline Modal -->
{#if showOutlineModal}
  <div class="fixed inset-0 bg-black/50 flex items-center justify-center z-50" onclick={closeOutlineModal}>
    <div class="bg-background border border-border rounded-lg shadow-lg max-w-2xl w-full mx-4 max-h-[80vh] overflow-hidden" onclick={(e) => e.stopPropagation()}>
      <!-- Modal Header -->
      <div class="flex items-center justify-between p-6 border-b border-border">
        <h2 class="text-xl font-semibold">Generate Content Outline</h2>
        <button onclick={closeOutlineModal} class="p-2 hover:bg-muted rounded-lg transition-colors">
          <X class="w-5 h-5" />
        </button>
      </div>

      <!-- Modal Content -->
      <div class="p-6 overflow-y-auto max-h-[60vh]">
        <p class="text-sm text-muted-foreground mb-4">
          Select the recommendations you'd like to create content outlines for:
        </p>

        <div class="space-y-3">
          {#each parsedRecommendations as recommendation}
            <label class="flex items-start gap-3 p-4 border border-border rounded-lg hover:bg-muted/50 cursor-pointer transition-colors">
              <input
                type="checkbox"
                checked={selectedRecommendations.includes(recommendation.id)}
                onchange={() => toggleRecommendation(recommendation.id)}
                class="mt-1 rounded border-border"
              />
              <div class="flex-1">
                <div class="font-medium text-sm mb-1">{recommendation.title}</div>
                <div class="text-xs text-muted-foreground">{recommendation.description}</div>
              </div>
            </label>
          {/each}
        </div>

        {#if parsedRecommendations.length === 0}
          <div class="text-center py-8 text-muted-foreground">
            <p>No structured recommendations found.</p>
            <p class="text-xs mt-1">The outline generator will work with the full recommendations text.</p>
          </div>
        {/if}
      </div>

      <!-- Modal Footer -->
      <div class="flex items-center justify-end gap-3 p-6 border-t border-border">
        <button
          onclick={closeOutlineModal}
          class="px-4 py-2 text-sm font-medium text-muted-foreground hover:text-foreground transition-colors"
        >
          Cancel
        </button>
        <button
          onclick={createOutline}
          disabled={selectedRecommendations.length === 0 && parsedRecommendations.length > 0}
          class="px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed transition-colors text-sm font-medium"
        >
          Create Outline
        </button>
      </div>
    </div>
  </div>
{/if}
