<script lang="ts">
  import { createEventDispatcher } from "svelte"
  import { X, Activity, TrendingUp, Clock, Star, Brain, Users, Wrench, ChevronRight } from "lucide-svelte"

  export let dashboardData: any = {}
  export let session: any
  export let environment: any

  const dispatch = createEventDispatcher()

  function handleClose() {
    dispatch('close')
  }

  // Mock data for demonstration
  const recentActivity = [
    {
      id: 1,
      type: 'content',
      action: 'Document created',
      title: 'Marketing Strategy Q4',
      time: '2 hours ago',
      icon: Brain,
      color: 'text-blue-500'
    },
    {
      id: 2,
      type: 'agent',
      action: 'SEO analysis completed',
      title: 'Keyword research for landing page',
      time: '4 hours ago',
      icon: Users,
      color: 'text-green-500'
    },
    {
      id: 3,
      type: 'tool',
      action: 'Tool integration',
      title: 'Connected Google Analytics',
      time: '1 day ago',
      icon: Wrench,
      color: 'text-purple-500'
    },
    {
      id: 4,
      type: 'content',
      action: 'External source connected',
      title: 'Slack workspace linked',
      time: '2 days ago',
      icon: Brain,
      color: 'text-blue-500'
    }
  ]

  const usageStats = [
    { label: 'Documents Created', value: '24', change: '+12%', trend: 'up' },
    { label: 'AI Requests', value: '156', change: '+8%', trend: 'up' },
    { label: 'Agent Sessions', value: '32', change: '+24%', trend: 'up' },
    { label: 'Tools Used', value: '18', change: '+5%', trend: 'up' }
  ]

  const recommendations = [
    {
      id: 1,
      title: 'Complete SEO audit',
      description: 'Your content could benefit from SEO optimization',
      action: 'Run SEO Agent',
      priority: 'high'
    },
    {
      id: 2,
      title: 'Connect more sources',
      description: 'Add Google Drive to expand your knowledge base',
      action: 'Connect Source',
      priority: 'medium'
    },
    {
      id: 3,
      title: 'Create campaign',
      description: 'Launch a multi-channel campaign for Q4',
      action: 'Open Orchestrator',
      priority: 'medium'
    }
  ]

  const quickAccess = [
    { name: 'Marketing Strategy Q4', type: 'document', time: '2h ago' },
    { name: 'SEO Agent', type: 'agent', time: '4h ago' },
    { name: 'Content Planner', type: 'tool', time: '1d ago' },
    { name: 'Brand Guidelines', type: 'document', time: '2d ago' }
  ]

  function getPriorityColor(priority: string) {
    switch (priority) {
      case 'high': return 'text-red-500 bg-red-50 border-red-200'
      case 'medium': return 'text-orange-500 bg-orange-50 border-orange-200'
      case 'low': return 'text-green-500 bg-green-50 border-green-200'
      default: return 'text-muted-foreground bg-muted border-border'
    }
  }
</script>

<div class="flex flex-col h-full bg-card">
  <!-- Header -->
  <div class="flex items-center justify-between p-4 border-b border-border">
    <div class="flex items-center gap-2">
      <Activity class="w-5 h-5 text-primary" />
      <h2 class="font-semibold text-foreground">Insights</h2>
    </div>
    <button
      on:click={handleClose}
      class="p-1 hover:bg-muted rounded-lg transition-colors"
      title="Close insights panel"
    >
      <X class="w-4 h-4 text-muted-foreground" />
    </button>
  </div>

  <!-- Content -->
  <div class="flex-1 overflow-y-auto p-4 space-y-6">
    
    <!-- Usage Analytics -->
    <div class="space-y-3">
      <div class="flex items-center gap-2">
        <TrendingUp class="w-4 h-4 text-green-500" />
        <h3 class="font-medium text-foreground text-sm">Usage Analytics</h3>
      </div>
      <div class="space-y-2">
        {#each usageStats as stat}
          <div class="p-3 bg-background border border-border rounded-lg">
            <div class="flex items-center justify-between mb-1">
              <span class="text-xs text-muted-foreground">{stat.label}</span>
              <span class="text-xs text-green-500 font-medium">{stat.change}</span>
            </div>
            <div class="text-lg font-semibold text-foreground">{stat.value}</div>
          </div>
        {/each}
      </div>
    </div>

    <!-- Recent Activity -->
    <div class="space-y-3">
      <div class="flex items-center gap-2">
        <Clock class="w-4 h-4 text-blue-500" />
        <h3 class="font-medium text-foreground text-sm">Recent Activity</h3>
      </div>
      <div class="space-y-2">
        {#each recentActivity as activity}
          <div class="p-3 bg-background border border-border rounded-lg hover:bg-muted/50 transition-colors">
            <div class="flex items-start gap-3">
              <div class="w-6 h-6 bg-muted rounded-lg flex items-center justify-center flex-shrink-0 mt-0.5">
                <svelte:component this={activity.icon} class="w-3 h-3 {activity.color}" />
              </div>
              <div class="flex-1 min-w-0">
                <div class="text-xs font-medium text-foreground mb-1">
                  {activity.action}
                </div>
                <div class="text-xs text-muted-foreground truncate mb-1">
                  {activity.title}
                </div>
                <div class="text-xs text-muted-foreground">
                  {activity.time}
                </div>
              </div>
            </div>
          </div>
        {/each}
      </div>
    </div>

    <!-- AI Recommendations -->
    <div class="space-y-3">
      <div class="flex items-center gap-2">
        <Star class="w-4 h-4 text-yellow-500" />
        <h3 class="font-medium text-foreground text-sm">Recommendations</h3>
      </div>
      <div class="space-y-2">
        {#each recommendations as rec}
          <div class="p-3 bg-background border border-border rounded-lg">
            <div class="flex items-start justify-between mb-2">
              <div class="flex-1 min-w-0">
                <div class="text-xs font-medium text-foreground mb-1">
                  {rec.title}
                </div>
                <div class="text-xs text-muted-foreground mb-2">
                  {rec.description}
                </div>
              </div>
              <span class="inline-flex items-center px-2 py-0.5 text-xs font-medium rounded-full border {getPriorityColor(rec.priority)}">
                {rec.priority}
              </span>
            </div>
            <button class="w-full px-3 py-1.5 text-xs bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors font-medium">
              {rec.action}
            </button>
          </div>
        {/each}
      </div>
    </div>

    <!-- Quick Access -->
    <div class="space-y-3">
      <div class="flex items-center gap-2">
        <ChevronRight class="w-4 h-4 text-purple-500" />
        <h3 class="font-medium text-foreground text-sm">Quick Access</h3>
      </div>
      <div class="space-y-1">
        {#each quickAccess as item}
          <button class="w-full p-2 bg-background border border-border rounded-lg hover:bg-muted/50 transition-colors text-left">
            <div class="flex items-center justify-between">
              <div class="flex-1 min-w-0">
                <div class="text-xs font-medium text-foreground truncate">
                  {item.name}
                </div>
                <div class="text-xs text-muted-foreground">
                  {item.type} • {item.time}
                </div>
              </div>
              <ChevronRight class="w-3 h-3 text-muted-foreground flex-shrink-0" />
            </div>
          </button>
        {/each}
      </div>
    </div>
  </div>

  <!-- Footer -->
  <div class="p-4 border-t border-border">
    <div class="text-xs text-muted-foreground text-center">
      Last updated: {new Date().toLocaleTimeString()}
    </div>
  </div>
</div>
