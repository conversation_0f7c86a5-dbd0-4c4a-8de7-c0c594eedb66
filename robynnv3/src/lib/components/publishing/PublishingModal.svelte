<script lang="ts">
  import { createEventDispatcher, onMount } from 'svelte'
  import { fade, scale } from 'svelte/transition'
  import { Send, X, Eye, Settings, CheckCircle, AlertCircle, Loader2 } from 'lucide-svelte'
  import type { OrchestratorResearchData, PublishingDestination } from '$lib/types/publishing'
  
  export let isOpen = false
  export let researchData: OrchestratorResearchData
  export let title = 'Publish Research Results'
  
  const dispatch = createEventDispatcher()
  
  // Component state
  let destinations: PublishingDestination[] = []
  let selectedDestinations: string[] = []
  let isLoading = false
  let isPublishing = false
  let isPreviewing = false
  let publishingResults: any[] = []
  let previewData: any = null
  let error: string | null = null
  
  // Publishing options
  let publishingOptions = {
    includeMetadata: true,
    includeContacts: true,
    includeCompetitors: true,
    includeInsights: true,
    messageStyle: 'detailed' as 'detailed' | 'summary' | 'executive',
    useThreads: false,
    mentionUsers: [] as string[]
  }
  
  // Load destinations on mount
  onMount(async () => {
    await loadDestinations()
  })
  
  async function loadDestinations() {
    try {
      isLoading = true
      const response = await fetch('/api/publishing/destinations?enabled=true')
      const data = await response.json()

      if (data.success) {
        destinations = data.destinations

        // Add placeholder destinations for unimplemented types
        const placeholderDestinations = [
          {
            id: 'hubspot-placeholder',
            type: 'hubspot',
            name: 'HubSpot CRM',
            description: 'Publish research results to HubSpot CRM as company and contact records',
            enabled: false,
            isPlaceholder: true,
            comingSoon: true
          },
          {
            id: 'salesforce-placeholder',
            type: 'salesforce',
            name: 'Salesforce CRM',
            description: 'Publish research results to Salesforce as accounts and opportunities',
            enabled: false,
            isPlaceholder: true,
            comingSoon: true
          },
          {
            id: 'webhook-placeholder',
            type: 'webhook',
            name: 'Generic Webhook',
            description: 'Send research results to custom webhook endpoints with flexible formatting',
            enabled: false,
            isPlaceholder: true,
            comingSoon: true
          }
        ]

        // Add placeholder destinations to the list
        destinations = [...destinations, ...placeholderDestinations]

        // Auto-select first enabled destination if available
        const enabledDestinations = destinations.filter(d => d.enabled && !d.isPlaceholder)
        if (enabledDestinations.length > 0) {
          selectedDestinations = [enabledDestinations[0].id]
        }
      } else {
        error = data.error || 'Failed to load destinations'
      }
    } catch (err) {
      error = 'Network error loading destinations'
      console.error('Error loading destinations:', err)
    } finally {
      isLoading = false
    }
  }
  
  async function handlePreview() {
    if (selectedDestinations.length === 0) {
      error = 'Please select at least one destination'
      return
    }
    
    try {
      isPreviewing = true
      error = null
      
      // Get the first selected destination type for preview
      const firstDestination = destinations.find(d => d.id === selectedDestinations[0])
      if (!firstDestination) {
        error = 'Selected destination not found'
        return
      }
      
      const response = await fetch('/api/publishing/publish/preview', {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          researchData,
          destinationType: firstDestination.type,
          options: publishingOptions
        })
      })
      
      const data = await response.json()
      
      if (data.success) {
        previewData = data.preview
      } else {
        error = data.error || 'Preview generation failed'
      }
    } catch (err) {
      error = 'Network error generating preview'
      console.error('Preview error:', err)
    } finally {
      isPreviewing = false
    }
  }
  
  async function handlePublish() {
    if (selectedDestinations.length === 0) {
      error = 'Please select at least one destination'
      return
    }
    
    try {
      isPublishing = true
      error = null
      publishingResults = []
      
      const response = await fetch('/api/publishing/publish', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          researchData,
          destinationIds: selectedDestinations,
          options: publishingOptions,
          parallel: true
        })
      })
      
      const data = await response.json()
      
      if (data.success) {
        publishingResults = data.results
        
        // Auto-close modal after successful publishing
        setTimeout(() => {
          handleClose()
        }, 2000)
      } else {
        error = data.error || 'Publishing failed'
        publishingResults = data.results || []
      }
    } catch (err) {
      error = 'Network error during publishing'
      console.error('Publishing error:', err)
    } finally {
      isPublishing = false
    }
  }
  
  function handleClose() {
    dispatch('close')
    // Reset state
    selectedDestinations = []
    publishingResults = []
    previewData = null
    error = null
  }
  
  function toggleDestination(destinationId: string) {
    if (selectedDestinations.includes(destinationId)) {
      selectedDestinations = selectedDestinations.filter(id => id !== destinationId)
    } else {
      selectedDestinations = [...selectedDestinations, destinationId]
    }
  }
  
  $: hasResults = publishingResults.length > 0
  $: successfulPublications = publishingResults.filter(r => r.success).length
  $: failedPublications = publishingResults.filter(r => !r.success).length
</script>

{#if isOpen}
  <!-- Modal backdrop -->
  <div
    class="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4"
    transition:fade={{ duration: 200 }}
    on:click={handleClose}
    on:keydown={(e) => e.key === 'Escape' && handleClose()}
    role="dialog"
    aria-modal="true"
    aria-labelledby="modal-title"
  >
    <!-- Modal content -->
    <div
      class="bg-background border-2 border-border shadow-soft max-w-2xl w-full max-h-[90vh] overflow-y-auto"
      transition:scale={{ duration: 200, start: 0.95 }}
      on:click|stopPropagation
      role="dialog"
    >
      <!-- Header -->
      <div class="flex items-center justify-between p-6 border-b border-border">
        <h2 id="modal-title" class="text-xl font-semibold text-foreground">
          {title}
        </h2>
        <button
          on:click={handleClose}
          class="p-2 hover:bg-muted rounded-lg transition-colors"
          aria-label="Close modal"
        >
          <X class="w-5 h-5" />
        </button>
      </div>
      
      <!-- Content -->
      <div class="p-6 space-y-6">
        <!-- Error display -->
        {#if error}
          <div class="flex items-center gap-2 p-3 bg-destructive/10 border border-destructive/20 rounded-lg">
            <AlertCircle class="w-5 h-5 text-destructive" />
            <span class="text-sm text-destructive">{error}</span>
          </div>
        {/if}
        
        <!-- Loading state -->
        {#if isLoading}
          <div class="flex items-center justify-center py-8">
            <Loader2 class="w-6 h-6 animate-spin text-muted-foreground" />
            <span class="ml-2 text-muted-foreground">Loading destinations...</span>
          </div>
        {:else}
          <!-- Destination selection -->
          <div class="space-y-3">
            <h3 class="text-lg font-medium text-foreground">Select Destinations</h3>
            
            {#if destinations.length === 0}
              <div class="p-4 bg-muted/50 rounded-lg text-center">
                <p class="text-muted-foreground">No publishing destinations configured.</p>
                <p class="text-sm text-muted-foreground mt-1">
                  Configure destinations in the settings to enable publishing.
                </p>
              </div>
            {:else}
              <div class="grid gap-3">
                {#each destinations as destination}
                  <label class="flex items-center gap-3 p-3 border border-border rounded-lg {destination.isPlaceholder ? 'opacity-60 cursor-not-allowed bg-muted/30' : 'hover:bg-muted/50 cursor-pointer'}">
                    <input
                      type="checkbox"
                      checked={selectedDestinations.includes(destination.id)}
                      on:change={() => !destination.isPlaceholder && toggleDestination(destination.id)}
                      disabled={destination.isPlaceholder}
                      class="w-4 h-4 text-primary {destination.isPlaceholder ? 'cursor-not-allowed' : ''}"
                    />
                    <div class="flex-1">
                      <div class="flex items-center gap-2">
                        <span class="font-medium text-foreground">{destination.name}</span>
                        <span class="px-2 py-1 bg-primary/10 text-primary text-xs rounded-full">
                          {destination.type}
                        </span>
                        {#if destination.comingSoon}
                          <span class="px-2 py-1 bg-orange-100 text-orange-700 text-xs rounded-full">
                            Coming Soon
                          </span>
                        {/if}
                      </div>
                      {#if destination.description}
                        <p class="text-sm text-muted-foreground mt-1">{destination.description}</p>
                      {/if}
                      {#if destination.isPlaceholder}
                        <p class="text-xs text-muted-foreground mt-1 italic">
                          This integration is planned for a future release
                        </p>
                      {/if}
                    </div>
                  </label>
                {/each}
              </div>
            {/if}
          </div>
          
          <!-- Publishing options -->
          <div class="space-y-3">
            <h3 class="text-lg font-medium text-foreground">Publishing Options</h3>
            
            <div class="grid grid-cols-2 gap-4">
              <label class="flex items-center gap-2">
                <input
                  type="checkbox"
                  bind:checked={publishingOptions.includeMetadata}
                  class="w-4 h-4 text-primary"
                />
                <span class="text-sm text-foreground">Include Metadata</span>
              </label>
              
              <label class="flex items-center gap-2">
                <input
                  type="checkbox"
                  bind:checked={publishingOptions.includeContacts}
                  class="w-4 h-4 text-primary"
                />
                <span class="text-sm text-foreground">Include Contacts</span>
              </label>
              
              <label class="flex items-center gap-2">
                <input
                  type="checkbox"
                  bind:checked={publishingOptions.includeCompetitors}
                  class="w-4 h-4 text-primary"
                />
                <span class="text-sm text-foreground">Include Competitors</span>
              </label>
              
              <label class="flex items-center gap-2">
                <input
                  type="checkbox"
                  bind:checked={publishingOptions.includeInsights}
                  class="w-4 h-4 text-primary"
                />
                <span class="text-sm text-foreground">Include Insights</span>
              </label>
            </div>
            
            <div class="space-y-2">
              <label class="block text-sm font-medium text-foreground">Message Style</label>
              <select
                bind:value={publishingOptions.messageStyle}
                class="w-full p-2 border border-border rounded-lg bg-background text-foreground"
              >
                <option value="detailed">Detailed</option>
                <option value="summary">Summary</option>
                <option value="executive">Executive</option>
              </select>
            </div>
          </div>
          
          <!-- Preview section -->
          {#if previewData}
            <div class="space-y-3">
              <h3 class="text-lg font-medium text-foreground">Preview</h3>
              <div class="p-4 bg-muted/50 rounded-lg border border-border max-h-60 overflow-y-auto">
                <pre class="text-sm text-foreground whitespace-pre-wrap">{JSON.stringify(previewData, null, 2)}</pre>
              </div>
            </div>
          {/if}
          
          <!-- Publishing results -->
          {#if hasResults}
            <div class="space-y-3">
              <h3 class="text-lg font-medium text-foreground">Publishing Results</h3>
              
              <div class="grid gap-2">
                {#each publishingResults as result}
                  <div class="flex items-center gap-3 p-3 border border-border rounded-lg">
                    {#if result.success}
                      <CheckCircle class="w-5 h-5 text-green-500" />
                      <span class="text-sm text-foreground">
                        Published to {result.destination} successfully
                      </span>
                    {:else}
                      <AlertCircle class="w-5 h-5 text-destructive" />
                      <span class="text-sm text-foreground">
                        Failed to publish to {result.destination}: {result.error}
                      </span>
                    {/if}
                  </div>
                {/each}
              </div>
              
              {#if successfulPublications > 0}
                <div class="p-3 bg-green-50 border border-green-200 rounded-lg">
                  <p class="text-sm text-green-800">
                    Successfully published to {successfulPublications} destination{successfulPublications !== 1 ? 's' : ''}
                  </p>
                </div>
              {/if}
            </div>
          {/if}
        {/if}
      </div>
      
      <!-- Footer -->
      <div class="flex items-center justify-between p-6 border-t border-border">
        <button
          on:click={handleClose}
          class="px-4 py-2 text-muted-foreground hover:text-foreground transition-colors"
          disabled={isPublishing}
        >
          Cancel
        </button>
        
        <div class="flex items-center gap-3">
          <button
            on:click={handlePreview}
            disabled={selectedDestinations.length === 0 || isPreviewing || isPublishing}
            class="flex items-center gap-2 px-4 py-2 border border-border rounded-lg hover:bg-muted transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {#if isPreviewing}
              <Loader2 class="w-4 h-4 animate-spin" />
            {:else}
              <Eye class="w-4 h-4" />
            {/if}
            Preview
          </button>
          
          <button
            on:click={handlePublish}
            disabled={selectedDestinations.length === 0 || isPublishing || hasResults}
            class="flex items-center gap-2 px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {#if isPublishing}
              <Loader2 class="w-4 h-4 animate-spin" />
            {:else}
              <Send class="w-4 h-4" />
            {/if}
            Publish
          </button>
        </div>
      </div>
    </div>
  </div>
{/if}
