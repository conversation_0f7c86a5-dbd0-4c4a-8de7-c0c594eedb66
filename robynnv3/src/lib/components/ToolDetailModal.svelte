<script lang="ts">
  import * as Dialog from '$lib/components/ui/dialog';
  import type { ToolInfo } from '$lib/services/tool-registry';
  
  export let tool: ToolInfo | null = null;
  export let open: boolean = false;

  const categoryColors = {
    content: 'bg-blue-100 text-blue-800 border-blue-200',
    research: 'bg-green-100 text-green-800 border-green-200', 
    seo: 'bg-purple-100 text-purple-800 border-purple-200',
    apollo: 'bg-orange-100 text-orange-800 border-orange-200'
  };

  const categoryLabels = {
    content: 'Content',
    research: 'Research',
    seo: 'SEO',
    apollo: 'Apollo'
  };

  const statusColors = {
    active: 'bg-green-100 text-green-800 border-green-200',
    beta: 'bg-yellow-100 text-yellow-800 border-yellow-200',
    deprecated: 'bg-red-100 text-red-800 border-red-200'
  };
</script>

<Dialog.Root bind:open>
  <Dialog.Content class="max-w-2xl max-h-[80vh] overflow-y-auto">
    {#if tool}
      <Dialog.Header>
        <Dialog.Title class="flex items-center gap-3">
          <span class="text-2xl">{tool.icon}</span>
          <div>
            <div class="text-xl font-semibold">{tool.name}</div>
            <div class="flex items-center gap-2 mt-1">
              <span class="px-2 py-1 text-xs font-medium border {categoryColors[tool.category]}">
                {categoryLabels[tool.category]}
              </span>
              <span class="px-2 py-1 text-xs font-medium border {statusColors[tool.status]}">
                {tool.status.toUpperCase()}
              </span>
            </div>
          </div>
        </Dialog.Title>
        <Dialog.Description class="text-base">
          {tool.description}
        </Dialog.Description>
      </Dialog.Header>
      
      <div class="space-y-6">
        <!-- Tool ID and Technical Info -->
        <div class="bg-muted p-4 border border-border">
          <h4 class="font-semibold text-sm text-muted-foreground mb-2">Technical Details</h4>
          <div class="space-y-1 text-sm">
            <div><span class="font-medium">Tool ID:</span> <code class="bg-background px-2 py-1 border border-border text-xs">{tool.id}</code></div>
            <div><span class="font-medium">Category:</span> {categoryLabels[tool.category]}</div>
            <div><span class="font-medium">Status:</span> {tool.status}</div>
          </div>
        </div>
        
        <!-- Used By Agents -->
        <div>
          <h4 class="font-semibold mb-3">Used by Agents</h4>
          {#if tool.usedBy.length > 0}
            <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
              {#each tool.usedBy as agent}
                <div class="p-4 border border-border bg-background">
                  <div class="font-medium text-foreground">{agent.name}</div>
                  <div class="text-sm text-muted-foreground mt-1">AI Marketing Agent</div>
                </div>
              {/each}
            </div>
          {:else}
            <p class="text-muted-foreground text-sm">This tool is not currently used by any agents.</p>
          {/if}
        </div>
        
        <!-- Tool Usage Examples -->
        <div>
          <h4 class="font-semibold mb-3">Common Use Cases</h4>
          <div class="space-y-2 text-sm">
            {#if tool.category === 'content'}
              <div class="flex items-start gap-2">
                <span class="text-primary">•</span>
                <span>Generate high-quality content for marketing campaigns</span>
              </div>
              <div class="flex items-start gap-2">
                <span class="text-primary">•</span>
                <span>Create structured outlines and improve writing quality</span>
              </div>
              <div class="flex items-start gap-2">
                <span class="text-primary">•</span>
                <span>Summarize long documents and manage citations</span>
              </div>
            {:else if tool.category === 'research'}
              <div class="flex items-start gap-2">
                <span class="text-primary">•</span>
                <span>Comprehensive web research and data gathering</span>
              </div>
              <div class="flex items-start gap-2">
                <span class="text-primary">•</span>
                <span>Competitor analysis and market intelligence</span>
              </div>
              <div class="flex items-start gap-2">
                <span class="text-primary">•</span>
                <span>Company research and business intelligence</span>
              </div>
            {:else if tool.category === 'seo'}
              <div class="flex items-start gap-2">
                <span class="text-primary">•</span>
                <span>Keyword research and search volume analysis</span>
              </div>
              <div class="flex items-start gap-2">
                <span class="text-primary">•</span>
                <span>Competitive SEO analysis and opportunity discovery</span>
              </div>
              <div class="flex items-start gap-2">
                <span class="text-primary">•</span>
                <span>Content optimization and ranking strategy</span>
              </div>
            {:else if tool.category === 'apollo'}
              <div class="flex items-start gap-2">
                <span class="text-primary">•</span>
                <span>Company data enrichment and lead generation</span>
              </div>
              <div class="flex items-start gap-2">
                <span class="text-primary">•</span>
                <span>Contact discovery and outreach preparation</span>
              </div>
              <div class="flex items-start gap-2">
                <span class="text-primary">•</span>
                <span>Sales intelligence and prospect research</span>
              </div>
            {/if}
          </div>
        </div>

        <!-- Input Schema (for advanced users) -->
        {#if tool.inputSchema}
          <details class="border border-border">
            <summary class="font-semibold cursor-pointer p-4 hover:bg-muted transition-colors">
              Input Parameters (Advanced)
            </summary>
            <div class="p-4 border-t border-border bg-muted">
              <pre class="text-xs bg-background p-3 border border-border overflow-auto max-h-64">
{JSON.stringify(tool.inputSchema, null, 2)}
              </pre>
            </div>
          </details>
        {/if}
      </div>
    {/if}
  </Dialog.Content>
</Dialog.Root>
