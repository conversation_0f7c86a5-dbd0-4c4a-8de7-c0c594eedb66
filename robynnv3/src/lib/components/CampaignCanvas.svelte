<script lang="ts">
  import { afterUpdate } from 'svelte'
  import { createEventDispatcher } from 'svelte'
  import CampaignExamplePrompts from './CampaignExamplePrompts.svelte'
  import ResearchDisplay from './research/ResearchDisplay.svelte'

  export let messages: any[] = []
  export let progressSteps: any[] = []
  export let isLoading: boolean = false
  export let showExamples: boolean = true

  const dispatch = createEventDispatcher()

  let canvasContainer: HTMLElement
  let shouldAutoScroll = true

  // Auto-scroll to bottom when new content appears
  afterUpdate(() => {
    if (shouldAutoScroll && canvasContainer) {
      canvasContainer.scrollTop = canvasContainer.scrollHeight
    }
  })

  function handleExampleSelect(event: CustomEvent) {
    dispatch('selectExample', event.detail)
  }

  function handleScroll() {
    if (!canvasContainer) return
    
    const { scrollTop, scrollHeight, clientHeight } = canvasContainer
    const isAtBottom = scrollTop + clientHeight >= scrollHeight - 10
    shouldAutoScroll = isAtBottom
  }
</script>

<div
  class="flex-1 flex flex-col h-screen overflow-hidden bg-background transition-all duration-400 ease-out"
  bind:this={canvasContainer}
  on:scroll={handleScroll}
>
  <!-- Main Content Area -->
  <div class="flex-1 overflow-y-auto p-8">
    {#if showExamples && messages.length === 0}
      <!-- Example Prompts -->
      <CampaignExamplePrompts on:selectPrompt={handleExampleSelect} />
    {:else}
      <!-- Agent Output Display -->
      <div class="max-w-4xl mx-auto space-y-6">
        {#if messages.length > 0}
          <div class="space-y-4">
            {#each messages as message}
              {#if message.role === 'assistant'}
                <div class="bg-card border border-border rounded-lg p-6 shadow-sm">
                  <div class="prose prose-sm max-w-none">
                    {@html message.content}
                  </div>
                  
                  {#if message.data}
                    <!-- Professional Research Display -->
                    <div class="mt-6">
                      <ResearchDisplay researchData={message.data} />
                    </div>
                  {/if}
                </div>
              {/if}
            {/each}
          </div>
        {/if}

        <!-- Progress Display -->
        {#if isLoading && progressSteps.length > 0}
          <div class="bg-card border border-border rounded-lg p-6 shadow-sm">
            <div class="flex items-center gap-2 mb-4">
              <div class="w-2 h-2 bg-primary rounded-full animate-pulse"></div>
              <h3 class="font-semibold">Research in Progress</h3>
            </div>
            
            <div class="space-y-3">
              {#each progressSteps as step}
                <div class="flex items-center gap-3">
                  <div class="flex-shrink-0">
                    {#if step.status === 'completed'}
                      <div class="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                        <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                          <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                        </svg>
                      </div>
                    {:else if step.status === 'active'}
                      <div class="w-6 h-6 bg-primary rounded-full flex items-center justify-center">
                        <div class="w-3 h-3 bg-white rounded-full animate-pulse"></div>
                      </div>
                    {:else}
                      <div class="w-6 h-6 border-2 border-muted-foreground/30 rounded-full"></div>
                    {/if}
                  </div>
                  
                  <div class="flex-1">
                    <div class="font-medium text-sm {step.status === 'completed' ? 'text-muted-foreground' : 'text-foreground'}">
                      {step.title}
                    </div>
                    {#if step.description}
                      <div class="text-xs text-muted-foreground mt-1">
                        {step.description}
                      </div>
                    {/if}
                  </div>
                </div>
              {/each}
            </div>
          </div>
        {/if}
      </div>
    {/if}
  </div>
</div>
