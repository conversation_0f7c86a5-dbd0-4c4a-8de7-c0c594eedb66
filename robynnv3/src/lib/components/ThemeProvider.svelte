<script lang="ts">
  import { onMount } from 'svelte';
  import type { ThemeType } from '$lib/themes';
  
  interface Props {
    themeCSS: string;
    theme: ThemeType;
  }
  
  let { themeCSS, theme }: Props = $props();
  
  onMount(() => {
    // Remove any existing theme styles
    const existingThemeStyle = document.getElementById('dynamic-theme-styles');
    if (existingThemeStyle) {
      existingThemeStyle.remove();
    }
    
    // Create and inject new theme styles
    const styleElement = document.createElement('style');
    styleElement.id = 'dynamic-theme-styles';
    styleElement.textContent = themeCSS;
    document.head.appendChild(styleElement);
    
    // Set DaisyUI data-theme attribute based on our theme
    const daisyUITheme = theme === 'PROFESSIONAL' ? 'professional' : 'neo';
    document.documentElement.setAttribute('data-theme', daisyUITheme);
    
    return () => {
      // Cleanup on unmount
      const styleEl = document.getElementById('dynamic-theme-styles');
      if (styleEl) {
        styleEl.remove();
      }
    };
  });
</script>