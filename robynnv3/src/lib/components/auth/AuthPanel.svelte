<script lang="ts">
  import { fadeIn } from '$lib/animations/actions';
  import { onMount } from 'svelte';
  
  interface Props {
    children?: any;
    title?: string;
    subtitle?: string;
    showHeader?: boolean;
  }
  
  let { 
    children, 
    title = "Welcome back",
    subtitle = "Sign in to your account to continue",
    showHeader = true 
  }: Props = $props();
  
  // Track if we're in Europe for cookie notice
  let isEurope = $state(false);
  
  onMount(() => {
    try {
      isEurope = Intl.DateTimeFormat()
        .resolvedOptions()
        .timeZone.startsWith("Europe/");
    } catch (e) {
      // Fallback if timezone detection fails
      isEurope = false;
    }
  });
</script>

<div class="w-full space-y-6">
  <!-- Header Section -->
  {#if showHeader}
    <div 
      class="text-center space-y-2"
      use:fadeIn={{ delay: 0.2, duration: 0.8 }}
    >
      <h1 class="linear-heading text-2xl md:text-3xl font-bold text-foreground">
        {title}
      </h1>
      <p class="linear-body text-muted-foreground">
        {subtitle}
      </p>
    </div>
  {/if}

  <!-- Main Authentication Card - Made narrower with 25% margins on each side -->
  <div
    class="linear-card bg-card border border-border rounded-2xl p-6 md:p-8 shadow-sm mx-auto w-full max-w-[75%] sm:max-w-[70%] md:max-w-[75%] lg:max-w-[70%]"
    use:fadeIn={{ delay: 0.4, duration: 0.6, direction: "up" }}
  >
    <slot />
  </div>

  <!-- Footer Links -->
  <div 
    class="text-center space-y-4"
    use:fadeIn={{ delay: 0.6, duration: 0.8 }}
  >
    <slot name="footer" />
    
    <!-- Cookie Notice for Europe -->
    {#if isEurope}
      <div 
        class="text-center p-3 bg-muted/50 border border-border rounded-lg"
        use:fadeIn={{ delay: 0.8, duration: 0.6 }}
      >
        <span class="linear-mono text-sm text-muted-foreground">
          🍪 Logging in uses Cookies 🍪
        </span>
      </div>
    {/if}
    
    <!-- Removed redundant legal links - these are in the main site footer -->
  </div>
</div>

<style>
  /* Enhanced card styling with theme support */
  .linear-card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);
  }
  
  .linear-card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-1px);
  }
  
  /* Responsive adjustments */
  @media (max-width: 767px) {
    .linear-card {
      margin: 0 -0.5rem; /* Extend to edges on very small screens */
      border-radius: 1rem; /* Slightly smaller radius on mobile */
    }
  }
  
  /* Professional theme specific styling */
  :global([data-theme="professional"]) .linear-card {
    background: var(--card);
    border-color: var(--border);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }
  
  :global([data-theme="professional"]) .linear-card:hover {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }
  
  /* NEO theme specific styling */
  :global([data-theme="neo"]) .linear-card {
    background: var(--card);
    border: 2px solid var(--border);
    box-shadow: 4px 4px 0px 0px var(--foreground);
  }
  
  :global([data-theme="neo"]) .linear-card:hover {
    transform: translate(-2px, -2px);
    box-shadow: 6px 6px 0px 0px var(--foreground);
  }
  
  /* Smooth transitions for all interactive elements */
  a {
    transition: color 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }
</style>
