<script lang="ts">
  import { onMount } from 'svelte';
  import { fadeIn } from '$lib/animations/actions';
  import { prefersReducedMotion } from '$lib/animations';
  
  interface Props {
    children?: any;
    showBrandPanel?: boolean;
  }
  
  let { children, showBrandPanel = true }: Props = $props();
  
  // Responsive state
  let isMobile = $state(false);
  let isTablet = $state(false);
  
  onMount(() => {
    const updateBreakpoints = () => {
      isMobile = window.innerWidth < 768;
      isTablet = window.innerWidth >= 768 && window.innerWidth < 1024;
    };
    
    updateBreakpoints();
    window.addEventListener('resize', updateBreakpoints);
    
    return () => {
      window.removeEventListener('resize', updateBreakpoints);
    };
  });
  
  // Hide brand panel on mobile
  const showBrand = $derived(showBrandPanel && !isMobile);
</script>

<!-- Main container with 90% viewport width and header spacing -->
<div class="min-h-screen bg-background flex w-[90vw] mx-auto mt-[5px]">
  <!-- Left Panel - Brand (hidden on mobile) -->
  {#if showBrand}
    <div 
      class="hidden md:flex md:w-2/5 lg:w-2/5 xl:w-2/5 bg-card border-r border-border flex-col justify-center p-8 lg:p-12"
      use:fadeIn={{ delay: 0.2, duration: 0.8 }}
    >
      <slot name="brand-panel" />
    </div>
  {/if}
  
  <!-- Right Panel - Authentication (full width on mobile) -->
  <div
    class="flex-1 flex items-center justify-center p-4 md:p-6 lg:p-8 {showBrand ? 'md:w-3/5 lg:w-3/5 xl:w-3/5' : ''}"
    use:fadeIn={{ delay: showBrand ? 0.4 : 0.2, duration: 0.6 }}
  >
    <div class="w-full max-w-none">
      <slot name="auth-panel" />
    </div>
  </div>
</div>

<style>
  /* Ensure smooth transitions for responsive changes */
  .flex-1 {
    transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  /* Mobile-first responsive adjustments */
  @media (max-width: 767px) {
    .min-h-screen {
      min-height: 100dvh; /* Use dynamic viewport height on mobile */
    }
  }
  
  /* Tablet adjustments */
  @media (min-width: 768px) and (max-width: 1023px) {
    .md\:w-2\/5 {
      width: 35%;
    }
    .md\:w-3\/5 {
      width: 65%;
    }
  }
  
  /* Desktop optimizations */
  @media (min-width: 1024px) {
    .lg\:w-2\/5 {
      width: 40%;
    }
    .lg\:w-3\/5 {
      width: 60%;
    }
  }
</style>
