<script lang="ts">
  import { fadeIn, cardHover } from '$lib/animations/actions';
  import { onMount } from 'svelte';
  
  interface Props {
    children?: any;
    variant?: 'default' | 'elevated' | 'minimal';
    showAnimation?: boolean;
    className?: string;
  }
  
  let { 
    children, 
    variant = 'default',
    showAnimation = true,
    className = ''
  }: Props = $props();
  
  // Theme detection for styling
  let currentTheme = $state('professional');
  
  onMount(() => {
    // Detect current theme from document
    const updateTheme = () => {
      const theme = document.documentElement.getAttribute('data-theme');
      currentTheme = theme || 'professional';
    };
    
    updateTheme();
    
    // Watch for theme changes
    const observer = new MutationObserver(updateTheme);
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['data-theme']
    });
    
    return () => observer.disconnect();
  });
  
  // Dynamic classes based on variant and theme
  $: cardClasses = [
    'auth-card',
    `auth-card--${variant}`,
    `auth-card--${currentTheme}`,
    className
  ].filter(Boolean).join(' ');
</script>

<div 
  class={cardClasses}
  use:fadeIn={showAnimation ? { delay: 0.3, duration: 0.8, direction: "up" } : {}}
  use:cardHover={showAnimation}
>
  <slot />
</div>

<style>
  /* Base card styling */
  .auth-card {
    @apply w-full bg-card text-card-foreground rounded-2xl border transition-all duration-300;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
  }
  
  /* Variant: Default */
  .auth-card--default {
    @apply p-6 md:p-8 border-border;
  }
  
  /* Variant: Elevated */
  .auth-card--elevated {
    @apply p-8 md:p-10 border-border;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }
  
  /* Variant: Minimal */
  .auth-card--minimal {
    @apply p-4 md:p-6 border-transparent bg-transparent;
    backdrop-filter: none;
    -webkit-backdrop-filter: none;
  }
  
  /* Professional Theme Styling */
  .auth-card--professional {
    background: var(--card);
    border-color: var(--border);
    box-shadow: 
      0 4px 6px -1px rgba(0, 0, 0, 0.1), 
      0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }
  
  .auth-card--professional:hover {
    box-shadow: 
      0 10px 15px -3px rgba(0, 0, 0, 0.1), 
      0 4px 6px -2px rgba(0, 0, 0, 0.05);
    transform: translateY(-2px);
  }
  
  .auth-card--professional.auth-card--elevated {
    box-shadow: 
      0 25px 50px -12px rgba(0, 0, 0, 0.25),
      0 0 0 1px rgba(255, 255, 255, 0.05);
  }
  
  .auth-card--professional.auth-card--elevated:hover {
    box-shadow: 
      0 32px 64px -12px rgba(0, 0, 0, 0.35),
      0 0 0 1px rgba(255, 255, 255, 0.1);
    transform: translateY(-4px);
  }
  
  /* NEO Theme Styling */
  .auth-card--neo {
    background: var(--card);
    border: 2px solid var(--border);
    border-radius: 0.75rem; /* Slightly less rounded for brutalist feel */
    box-shadow: 4px 4px 0px 0px var(--foreground);
  }
  
  .auth-card--neo:hover {
    transform: translate(-2px, -2px);
    box-shadow: 6px 6px 0px 0px var(--foreground);
  }
  
  .auth-card--neo.auth-card--elevated {
    border: 3px solid var(--border);
    box-shadow: 8px 8px 0px 0px var(--foreground);
  }
  
  .auth-card--neo.auth-card--elevated:hover {
    transform: translate(-4px, -4px);
    box-shadow: 12px 12px 0px 0px var(--foreground);
  }
  
  .auth-card--neo.auth-card--minimal {
    border: 1px solid var(--border);
    box-shadow: 2px 2px 0px 0px var(--foreground);
  }
  
  /* Responsive adjustments */
  @media (max-width: 767px) {
    .auth-card {
      border-radius: 1rem;
      margin: 0 -0.25rem;
    }
    
    .auth-card--neo {
      border-radius: 0.5rem;
    }
    
    .auth-card--default {
      @apply p-4 md:p-6;
    }
    
    .auth-card--elevated {
      @apply p-6 md:p-8;
    }
  }
  
  /* Accessibility improvements */
  @media (prefers-reduced-motion: reduce) {
    .auth-card {
      transition: none;
    }
    
    .auth-card:hover {
      transform: none;
    }
  }
  
  /* Focus states for accessibility */
  .auth-card:focus-within {
    outline: 2px solid var(--primary);
    outline-offset: 2px;
  }
  
  /* High contrast mode support */
  @media (prefers-contrast: high) {
    .auth-card {
      border-width: 2px;
    }
    
    .auth-card--professional {
      border-color: var(--foreground);
    }
  }
  
  /* Dark mode optimizations */
  @media (prefers-color-scheme: dark) {
    .auth-card {
      backdrop-filter: blur(20px);
      -webkit-backdrop-filter: blur(20px);
    }
  }
</style>
