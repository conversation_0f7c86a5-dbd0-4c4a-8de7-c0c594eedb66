<script lang="ts">
  import { createEventDispatcher, onMount } from 'svelte';
  import { fadeIn, hoverScale, clickFeedback } from '$lib/animations/actions';
  import { enhancedSlide } from '$lib/animations/svelte-transitions';
  import { Button } from '$lib/components/ui/button';
  import { Separator } from '$lib/components/ui/separator';
  import AuthCard from './AuthCard.svelte';
  
  interface Props {
    mode?: 'signin' | 'signup';
    showModeToggle?: boolean;
    showSocialLogin?: boolean;
    isLoading?: boolean;
    error?: string | null;
    supabaseClient?: any;
  }
  
  let { 
    mode = 'signin',
    showModeToggle = true,
    showSocialLogin = true,
    isLoading = false,
    error = null,
    supabaseClient
  }: Props = $props();
  
  const dispatch = createEventDispatcher();
  
  // Form state management
  let currentMode = $state(mode);
  let isTransitioning = $state(false);
  
  // Toggle between sign-in and sign-up
  const toggleMode = async () => {
    if (isTransitioning) return;
    
    isTransitioning = true;
    currentMode = currentMode === 'signin' ? 'signup' : 'signin';
    
    // Dispatch mode change event
    dispatch('modeChange', { mode: currentMode });
    
    // Reset transition state after animation
    setTimeout(() => {
      isTransitioning = false;
    }, 300);
  };
  
  // Social login handlers
  const handleSocialLogin = async (provider: string) => {
    if (!supabaseClient) return;
    
    dispatch('socialLogin', { provider });
    
    try {
      const { error } = await supabaseClient.auth.signInWithOAuth({
        provider,
        options: {
          redirectTo: `${window.location.origin}/auth/callback`
        }
      });
      
      if (error) {
        dispatch('error', { error: error.message });
      }
    } catch (err) {
      dispatch('error', { error: 'Failed to sign in with ' + provider });
    }
  };
  
  // Form submission handler
  const handleSubmit = (event: Event) => {
    event.preventDefault();
    dispatch('submit', { mode: currentMode, formData: new FormData(event.target as HTMLFormElement) });
  };
  
  // Dynamic content based on mode
  $: modeConfig = {
    signin: {
      title: 'Welcome back',
      subtitle: 'Sign in to your account to continue',
      submitText: 'Sign In',
      toggleText: "Don't have an account?",
      toggleAction: 'Sign up',
      forgotPasswordText: 'Forgot your password?'
    },
    signup: {
      title: 'Create your account',
      subtitle: 'Get started with your free account',
      submitText: 'Create Account',
      toggleText: 'Already have an account?',
      toggleAction: 'Sign in',
      forgotPasswordText: null
    }
  };
  
  $: config = modeConfig[currentMode];
</script>

<AuthCard variant="default" showAnimation={true}>
  <!-- Header -->
  <div 
    class="text-center space-y-2 mb-6"
    use:fadeIn={{ delay: 0.2, duration: 0.8 }}
  >
    <h1 class="linear-heading text-2xl font-bold text-foreground">
      {config.title}
    </h1>
    <p class="linear-body text-muted-foreground">
      {config.subtitle}
    </p>
  </div>

  <!-- Error Display -->
  {#if error}
    <div 
      class="mb-4 p-3 bg-destructive/10 border border-destructive/20 rounded-lg text-destructive text-sm"
      use:fadeIn={{ delay: 0.1, duration: 0.4 }}
    >
      {error}
    </div>
  {/if}

  <!-- Social Login Section -->
  {#if showSocialLogin}
    <div 
      class="space-y-3 mb-6"
      use:fadeIn={{ delay: 0.4, duration: 0.8 }}
    >
      <Button
        variant="outline"
        size="lg"
        class="w-full"
        on:click={() => handleSocialLogin('google')}
        disabled={isLoading}
        use:hoverScale={{ scale: 1.02 }}
        use:clickFeedback
      >
        <svg class="w-5 h-5 mr-2" viewBox="0 0 24 24">
          <path fill="currentColor" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
          <path fill="currentColor" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
          <path fill="currentColor" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
          <path fill="currentColor" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
        </svg>
        Continue with Google
      </Button>
      
      <Button
        variant="outline"
        size="lg"
        class="w-full"
        on:click={() => handleSocialLogin('github')}
        disabled={isLoading}
        use:hoverScale={{ scale: 1.02 }}
        use:clickFeedback
      >
        <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
          <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
        </svg>
        Continue with GitHub
      </Button>
      
      <div class="relative">
        <Separator />
        <div class="absolute inset-0 flex items-center justify-center">
          <span class="bg-card px-2 text-xs text-muted-foreground">or</span>
        </div>
      </div>
    </div>
  {/if}

  <!-- Form Content -->
  <div class="space-y-4">
    {#key currentMode}
      <div 
        in:enhancedSlide={{ delay: 100, duration: 300 }}
        out:enhancedSlide={{ duration: 200 }}
      >
        <form on:submit={handleSubmit} class="space-y-4">
          <slot name="form-fields" {currentMode} {isLoading} />
          
          <!-- Submit Button -->
          <Button
            type="submit"
            size="lg"
            class="w-full btn-professional"
            disabled={isLoading}
            use:hoverScale={{ scale: 1.02 }}
            use:clickFeedback
          >
            {#if isLoading}
              <svg class="animate-spin -ml-1 mr-3 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Processing...
            {:else}
              {config.submitText}
            {/if}
          </Button>
        </form>
      </div>
    {/key}
  </div>

  <!-- Footer Links -->
  <div 
    class="mt-6 space-y-4 text-center"
    use:fadeIn={{ delay: 0.8, duration: 0.8 }}
  >
    <!-- Forgot Password (Sign-in only) -->
    {#if currentMode === 'signin' && config.forgotPasswordText}
      <div>
        <a 
          href="/login/forgot_password" 
          class="text-sm text-primary hover:text-primary/80 transition-colors underline-offset-4 hover:underline"
        >
          {config.forgotPasswordText}
        </a>
      </div>
    {/if}
    
    <!-- Mode Toggle -->
    {#if showModeToggle}
      <div class="text-sm text-muted-foreground">
        {config.toggleText}
        <button
          type="button"
          on:click={toggleMode}
          disabled={isTransitioning}
          class="text-primary hover:text-primary/80 transition-colors underline-offset-4 hover:underline ml-1"
        >
          {config.toggleAction}
        </button>
      </div>
    {/if}
  </div>
</AuthCard>
