<script lang="ts">
  import { fadeIn, hoverScale } from '$lib/animations/actions';
  import { ChevronRight, Shield, Users, Zap, Star } from 'lucide-svelte';
  
  interface Props {
    brandMessage?: string;
    trustBadge?: string;
    trustCount?: number;
    features?: Array<{ icon: any; title: string; description: string }>;
  }
  
  let { 
    brandMessage = "Growth Re-Engineered. Powered by Agents. Built by Marketers.",
    trustBadge = "Built by growth engineers. Trusted by scaling startups.",
    trustCount = 50,
    features = [
      {
        icon: Zap,
        title: "AI-Powered Growth",
        description: "Intelligent agents that scale your marketing efforts"
      },
      {
        icon: Shield,
        title: "Enterprise Security",
        description: "Bank-grade security with SOC 2 compliance"
      },
      {
        icon: Users,
        title: "Team Collaboration",
        description: "Built for teams that move fast and break things"
      }
    ]
  }: Props = $props();
</script>

<div class="space-y-8">
  <!-- Marketing Message (removed redundant Robynn branding) -->
  <div
    class="space-y-4"
    use:fadeIn={{ delay: 0.3, duration: 0.8 }}
  >
    <h2 class="linear-heading text-3xl lg:text-4xl font-bold text-foreground leading-tight">
      Your very own <span class="text-primary">Multi-Agent Marketing Team</span>
    </h2>

    <p class="linear-body text-lg text-muted-foreground leading-relaxed">
      {brandMessage}
    </p>
  </div>

  <!-- Features List -->
  <div 
    class="space-y-4"
    use:fadeIn={{ delay: 0.5, duration: 0.8 }}
  >
    {#each features as feature, index}
      <div 
        class="flex items-start space-x-3 p-3 rounded-lg hover:bg-muted/50 transition-colors duration-200"
        use:fadeIn={{ delay: 0.6 + (index * 0.1), duration: 0.6 }}
        use:hoverScale={{ scale: 1.02 }}
      >
        <div class="flex-shrink-0 w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center mt-1">
          <svelte:component this={feature.icon} class="w-4 h-4 text-primary" />
        </div>
        <div class="space-y-1">
          <h3 class="linear-heading text-sm font-semibold text-foreground">
            {feature.title}
          </h3>
          <p class="linear-body text-sm text-muted-foreground">
            {feature.description}
          </p>
        </div>
      </div>
    {/each}
  </div>

  <!-- Trust Indicators -->
  <div 
    class="space-y-4"
    use:fadeIn={{ delay: 0.8, duration: 0.8 }}
  >
    <!-- Trust Badge -->
    <div class="flex items-center space-x-2 text-sm text-muted-foreground">
      <div class="flex items-center space-x-1">
        {#each Array(5) as _, i}
          <Star class="w-4 h-4 fill-primary text-primary" />
        {/each}
      </div>
      <span class="linear-mono font-medium">
        Trusted by {trustCount}+ startups
      </span>
    </div>
    
    <p class="linear-mono text-sm text-muted-foreground">
      {trustBadge}
    </p>
    
    <!-- Removed SOC 2 Compliant • GDPR Ready badge as requested -->
  </div>

  <!-- Call to Action -->
  <div 
    class="pt-4"
    use:fadeIn={{ delay: 1.0, duration: 0.8 }}
  >
    <div class="p-4 bg-primary/5 border border-primary/20 rounded-lg">
      <div class="flex items-center justify-between">
        <div class="space-y-1">
          <p class="linear-heading text-sm font-semibold text-foreground">
            Ready to scale your growth?
          </p>
          <!-- Removed "Join the waitlist for early access" text as requested -->
        </div>
        <ChevronRight class="w-5 h-5 text-primary" />
      </div>
    </div>
  </div>
</div>

<style>
  /* Ensure smooth hover transitions */
  .transition-colors {
    transition-property: background-color, border-color, color, fill, stroke;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  }
</style>
