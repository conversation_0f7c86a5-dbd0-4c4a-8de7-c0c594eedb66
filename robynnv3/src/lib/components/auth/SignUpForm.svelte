<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import { fadeIn, focusAnimation } from '$lib/animations/actions';
  import * as Form from '$lib/components/ui/form';
  import { Input } from '$lib/components/ui/input';
  import { Label } from '$lib/components/ui/label';
  import { Button } from '$lib/components/ui/button';
  import { Eye, EyeOff, Mail, Lock, AlertCircle, CheckCircle2 } from 'lucide-svelte';
  import { 
    EmailValidationFeedback, 
    PasswordStrengthIndicator, 
    PasswordMatchIndicator 
  } from '$lib/components/auth';
  
  interface Props {
    isLoading?: boolean;
    error?: string | null;
    initialEmail?: string;
  }
  
  let { 
    isLoading = false, 
    error = null,
    initialEmail = ''
  }: Props = $props();
  
  const dispatch = createEventDispatcher();
  
  // Form state
  let email = $state(initialEmail);
  let password = $state('');
  let confirmPassword = $state('');
  let showPassword = $state(false);
  let showConfirmPassword = $state(false);
  let emailTouched = $state(false);
  let passwordTouched = $state(false);
  let confirmPasswordTouched = $state(false);
  let agreedToTerms = $state(false);
  
  // Validation state
  let emailError = $state('');
  let passwordError = $state('');
  let confirmPasswordError = $state('');
  
  // Email validation
  const emailRegex = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;
  
  // Password validation (matching the schema requirements)
  const passwordRegex = /^(?=.*\d)(?=.*[a-z])(?=.*[A-Z]).{8,16}$/;
  
  $: isValidEmail = emailRegex.test(email);
  $: isValidPassword = passwordRegex.test(password);
  $: passwordsMatch = password === confirmPassword && confirmPassword.length > 0;
  $: isFormValid = isValidEmail && isValidPassword && passwordsMatch && agreedToTerms && !isLoading;
  
  // Real-time validation
  $: if (emailTouched) {
    emailError = email.length === 0 ? 'Email is required' : 
                 !isValidEmail ? 'Please enter a valid email address' : '';
  }
  
  $: if (passwordTouched) {
    passwordError = password.length === 0 ? 'Password is required' : 
                   password.length < 8 ? 'Password must be at least 8 characters' :
                   password.length > 16 ? 'Password must be at most 16 characters' :
                   !passwordRegex.test(password) ? 'Password must include uppercase, lowercase, and digits' : '';
  }
  
  $: if (confirmPasswordTouched) {
    confirmPasswordError = confirmPassword.length === 0 ? 'Please confirm your password' :
                          !passwordsMatch ? 'Passwords do not match' : '';
  }
  
  // Form submission
  const handleSubmit = (event: Event) => {
    event.preventDefault();
    
    // Mark all fields as touched for validation
    emailTouched = true;
    passwordTouched = true;
    confirmPasswordTouched = true;
    
    if (!isFormValid) return;
    
    dispatch('submit', {
      email: email.trim(),
      password,
      confirmPassword
    });
  };
  
  // Input handlers
  const handleEmailInput = (event: Event) => {
    const target = event.target as HTMLInputElement;
    email = target.value;
    if (!emailTouched) emailTouched = true;
  };
  
  const handlePasswordInput = (event: Event) => {
    const target = event.target as HTMLInputElement;
    password = target.value;
    if (!passwordTouched) passwordTouched = true;
  };
  
  const handleConfirmPasswordInput = (event: Event) => {
    const target = event.target as HTMLInputElement;
    confirmPassword = target.value;
    if (!confirmPasswordTouched) confirmPasswordTouched = true;
  };
  
  const togglePasswordVisibility = () => {
    showPassword = !showPassword;
  };
  
  const toggleConfirmPasswordVisibility = () => {
    showConfirmPassword = !showConfirmPassword;
  };
</script>

<div class="space-y-4">
  <!-- Email Field -->
  <div 
    class="space-y-2"
    use:fadeIn={{ delay: 0.2, duration: 0.6 }}
  >
    <Label for="signup-email" class="text-sm font-medium text-foreground">
      Email address
    </Label>
    <div class="relative">
      <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
        <Mail class="h-4 w-4 text-muted-foreground" />
      </div>
      <Input
        id="signup-email"
        name="email"
        type="email"
        autocomplete="email"
        placeholder="Enter your email"
        value={email}
        on:input={handleEmailInput}
        on:blur={() => emailTouched = true}
        class="pl-10 {emailError ? 'border-destructive focus:border-destructive' : ''}"
        disabled={isLoading}
        use:focusAnimation
        required
      />
    </div>
    
    <!-- Email validation feedback -->
    <EmailValidationFeedback 
      {email} 
      touched={emailTouched}
      showConfirmationNotice={true}
    />
    
    <!-- Email error -->
    {#if emailError}
      <div class="flex items-center gap-2 text-sm text-destructive">
        <AlertCircle class="h-4 w-4" />
        <span>{emailError}</span>
      </div>
    {/if}
  </div>

  <!-- Password Field -->
  <div 
    class="space-y-2"
    use:fadeIn={{ delay: 0.4, duration: 0.6 }}
  >
    <Label for="signup-password" class="text-sm font-medium text-foreground">
      Password
    </Label>
    <div class="relative">
      <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
        <Lock class="h-4 w-4 text-muted-foreground" />
      </div>
      <Input
        id="signup-password"
        name="password"
        type={showPassword ? 'text' : 'password'}
        autocomplete="new-password"
        placeholder="Create a strong password"
        value={password}
        on:input={handlePasswordInput}
        on:blur={() => passwordTouched = true}
        class="pl-10 pr-10 {passwordError ? 'border-destructive focus:border-destructive' : ''}"
        disabled={isLoading}
        use:focusAnimation
        required
      />
      <button
        type="button"
        on:click={togglePasswordVisibility}
        class="absolute inset-y-0 right-0 pr-3 flex items-center text-muted-foreground hover:text-foreground transition-colors"
        disabled={isLoading}
        aria-label={showPassword ? 'Hide password' : 'Show password'}
      >
        {#if showPassword}
          <EyeOff class="h-4 w-4" />
        {:else}
          <Eye class="h-4 w-4" />
        {/if}
      </button>
    </div>
    
    <!-- Password strength indicator -->
    {#if passwordTouched}
      <PasswordStrengthIndicator 
        {password} 
        showRequirements={true}
      />
    {/if}
    
    <!-- Password error -->
    {#if passwordError}
      <div class="flex items-center gap-2 text-sm text-destructive">
        <AlertCircle class="h-4 w-4" />
        <span>{passwordError}</span>
      </div>
    {/if}
  </div>

  <!-- Confirm Password Field -->
  <div 
    class="space-y-2"
    use:fadeIn={{ delay: 0.6, duration: 0.6 }}
  >
    <Label for="signup-confirm-password" class="text-sm font-medium text-foreground">
      Confirm password
    </Label>
    <div class="relative">
      <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
        <Lock class="h-4 w-4 text-muted-foreground" />
      </div>
      <Input
        id="signup-confirm-password"
        name="confirmPassword"
        type={showConfirmPassword ? 'text' : 'password'}
        autocomplete="new-password"
        placeholder="Confirm your password"
        value={confirmPassword}
        on:input={handleConfirmPasswordInput}
        on:blur={() => confirmPasswordTouched = true}
        class="pl-10 pr-10 {confirmPasswordError ? 'border-destructive focus:border-destructive' : ''}"
        disabled={isLoading}
        use:focusAnimation
        required
      />
      <button
        type="button"
        on:click={toggleConfirmPasswordVisibility}
        class="absolute inset-y-0 right-0 pr-3 flex items-center text-muted-foreground hover:text-foreground transition-colors"
        disabled={isLoading}
        aria-label={showConfirmPassword ? 'Hide password' : 'Show password'}
      >
        {#if showConfirmPassword}
          <EyeOff class="h-4 w-4" />
        {:else}
          <Eye class="h-4 w-4" />
        {/if}
      </button>
    </div>
    
    <!-- Password match indicator -->
    {#if confirmPasswordTouched}
      <PasswordMatchIndicator 
        {password} 
        {confirmPassword}
        showPassword={showConfirmPassword}
      />
    {/if}
    
    <!-- Confirm password error -->
    {#if confirmPasswordError}
      <div class="flex items-center gap-2 text-sm text-destructive">
        <AlertCircle class="h-4 w-4" />
        <span>{confirmPasswordError}</span>
      </div>
    {/if}
  </div>

  <!-- Terms and Conditions -->
  <div 
    class="flex items-start space-x-3"
    use:fadeIn={{ delay: 0.8, duration: 0.6 }}
  >
    <input
      id="terms-agreement"
      name="agreedToTerms"
      type="checkbox"
      bind:checked={agreedToTerms}
      class="h-4 w-4 text-primary focus:ring-primary border-border rounded mt-1"
      disabled={isLoading}
      required
    />
    <Label for="terms-agreement" class="text-sm text-muted-foreground cursor-pointer leading-relaxed">
      I agree to the 
      <a href="/terms" class="text-primary hover:text-primary/80 underline-offset-4 hover:underline" target="_blank">
        Terms of Service
      </a> 
      and 
      <a href="/privacy" class="text-primary hover:text-primary/80 underline-offset-4 hover:underline" target="_blank">
        Privacy Policy
      </a>
    </Label>
  </div>

  <!-- Submit Button -->
  <div use:fadeIn={{ delay: 1.0, duration: 0.6 }}>
    <Button
      type="submit"
      size="lg"
      class="w-full btn-professional"
      disabled={!isFormValid}
      on:click={handleSubmit}
    >
      {#if isLoading}
        <svg class="animate-spin -ml-1 mr-3 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        Creating account...
      {:else}
        Create Account
      {/if}
    </Button>
  </div>

  <!-- Additional info -->
  <div 
    class="text-center text-sm text-muted-foreground space-y-2"
    use:fadeIn={{ delay: 1.2, duration: 0.6 }}
  >
    <p>
      By creating an account, you'll receive a confirmation email to verify your address.
    </p>
    <div class="flex items-center justify-center gap-2 text-xs">
      <CheckCircle2 class="h-3 w-3 text-primary" />
      <span>Free forever • No credit card required</span>
    </div>
  </div>
</div>
