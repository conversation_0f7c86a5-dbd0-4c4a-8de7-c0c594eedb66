<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import { fadeIn, focusAnimation } from '$lib/animations/actions';
  import * as Form from '$lib/components/ui/form';
  import { Input } from '$lib/components/ui/input';
  import { Label } from '$lib/components/ui/label';
  import { Button } from '$lib/components/ui/button';
  import { Eye, EyeOff, Mail, Lock, AlertCircle } from 'lucide-svelte';
  import { EmailValidationFeedback } from '$lib/components/auth';
  
  interface Props {
    isLoading?: boolean;
    error?: string | null;
    initialEmail?: string;
  }
  
  let { 
    isLoading = false, 
    error = null,
    initialEmail = ''
  }: Props = $props();
  
  const dispatch = createEventDispatcher();
  
  // Form state
  let email = $state(initialEmail);
  let password = $state('');
  let showPassword = $state(false);
  let emailTouched = $state(false);
  let passwordTouched = $state(false);
  
  // Validation state
  let emailError = $state('');
  let passwordError = $state('');
  
  // Email validation
  const emailRegex = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;
  
  $: isValidEmail = emailRegex.test(email);
  $: isValidPassword = password.length >= 6;
  $: isFormValid = isValidEmail && isValidPassword && !isLoading;
  
  // Real-time validation
  $: if (emailTouched) {
    emailError = email.length === 0 ? 'Email is required' : 
                 !isValidEmail ? 'Please enter a valid email address' : '';
  }
  
  $: if (passwordTouched) {
    passwordError = password.length === 0 ? 'Password is required' : 
                   password.length < 6 ? 'Password must be at least 6 characters' : '';
  }
  
  // Form submission
  const handleSubmit = (event: Event) => {
    event.preventDefault();
    
    // Mark all fields as touched for validation
    emailTouched = true;
    passwordTouched = true;
    
    if (!isFormValid) return;
    
    dispatch('submit', {
      email: email.trim(),
      password
    });
  };
  
  // Input handlers
  const handleEmailInput = (event: Event) => {
    const target = event.target as HTMLInputElement;
    email = target.value;
    if (!emailTouched) emailTouched = true;
  };
  
  const handlePasswordInput = (event: Event) => {
    const target = event.target as HTMLInputElement;
    password = target.value;
    if (!passwordTouched) passwordTouched = true;
  };
  
  const togglePasswordVisibility = () => {
    showPassword = !showPassword;
  };
</script>

<div class="space-y-4">
  <!-- Email Field -->
  <div 
    class="space-y-2"
    use:fadeIn={{ delay: 0.2, duration: 0.6 }}
  >
    <Label for="signin-email" class="text-sm font-medium text-foreground">
      Email address
    </Label>
    <div class="relative">
      <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
        <Mail class="h-4 w-4 text-muted-foreground" />
      </div>
      <Input
        id="signin-email"
        name="email"
        type="email"
        autocomplete="email"
        placeholder="Enter your email"
        value={email}
        on:input={handleEmailInput}
        on:blur={() => emailTouched = true}
        class="pl-10 {emailError ? 'border-destructive focus:border-destructive' : ''}"
        disabled={isLoading}
        use:focusAnimation
        required
      />
    </div>
    
    <!-- Email validation feedback -->
    {#if emailTouched}
      <EmailValidationFeedback 
        {email} 
        touched={emailTouched}
        showConfirmationNotice={false}
      />
    {/if}
    
    <!-- Email error -->
    {#if emailError}
      <div class="flex items-center gap-2 text-sm text-destructive">
        <AlertCircle class="h-4 w-4" />
        <span>{emailError}</span>
      </div>
    {/if}
  </div>

  <!-- Password Field -->
  <div 
    class="space-y-2"
    use:fadeIn={{ delay: 0.4, duration: 0.6 }}
  >
    <Label for="signin-password" class="text-sm font-medium text-foreground">
      Password
    </Label>
    <div class="relative">
      <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
        <Lock class="h-4 w-4 text-muted-foreground" />
      </div>
      <Input
        id="signin-password"
        name="password"
        type={showPassword ? 'text' : 'password'}
        autocomplete="current-password"
        placeholder="Enter your password"
        value={password}
        on:input={handlePasswordInput}
        on:blur={() => passwordTouched = true}
        class="pl-10 pr-10 {passwordError ? 'border-destructive focus:border-destructive' : ''}"
        disabled={isLoading}
        use:focusAnimation
        required
      />
      <button
        type="button"
        on:click={togglePasswordVisibility}
        class="absolute inset-y-0 right-0 pr-3 flex items-center text-muted-foreground hover:text-foreground transition-colors"
        disabled={isLoading}
        aria-label={showPassword ? 'Hide password' : 'Show password'}
      >
        {#if showPassword}
          <EyeOff class="h-4 w-4" />
        {:else}
          <Eye class="h-4 w-4" />
        {/if}
      </button>
    </div>
    
    <!-- Password error -->
    {#if passwordError}
      <div class="flex items-center gap-2 text-sm text-destructive">
        <AlertCircle class="h-4 w-4" />
        <span>{passwordError}</span>
      </div>
    {/if}
  </div>

  <!-- Remember me option -->
  <div 
    class="flex items-center space-x-2"
    use:fadeIn={{ delay: 0.6, duration: 0.6 }}
  >
    <input
      id="remember-me"
      name="remember"
      type="checkbox"
      class="h-4 w-4 text-primary focus:ring-primary border-border rounded"
      disabled={isLoading}
    />
    <Label for="remember-me" class="text-sm text-muted-foreground cursor-pointer">
      Remember me for 30 days
    </Label>
  </div>

  <!-- Submit Button -->
  <div use:fadeIn={{ delay: 0.8, duration: 0.6 }}>
    <Button
      type="submit"
      size="lg"
      class="w-full btn-professional"
      disabled={!isFormValid}
      on:click={handleSubmit}
    >
      {#if isLoading}
        <svg class="animate-spin -ml-1 mr-3 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        Signing in...
      {:else}
        Sign In
      {/if}
    </Button>
  </div>

  <!-- Additional help text -->
  <div 
    class="text-center text-sm text-muted-foreground"
    use:fadeIn={{ delay: 1.0, duration: 0.6 }}
  >
    <p>
      By signing in, you agree to our 
      <a href="/terms" class="text-primary hover:text-primary/80 underline-offset-4 hover:underline">
        Terms of Service
      </a> 
      and 
      <a href="/privacy" class="text-primary hover:text-primary/80 underline-offset-4 hover:underline">
        Privacy Policy
      </a>
    </p>
  </div>
</div>

<style>
  /* Enhanced input styling */
  :global(.signin-input) {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  :global(.signin-input:focus) {
    transform: scale(1.01);
    box-shadow: 0 0 0 2px rgba(var(--primary), 0.2);
  }
  
  /* Smooth transitions for validation states */
  .space-y-2 > * {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  /* Accessibility improvements */
  button[aria-label] {
    transition: color 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  /* Focus ring for checkbox */
  input[type="checkbox"]:focus {
    outline: 2px solid var(--primary);
    outline-offset: 2px;
  }
</style>
