<script lang="ts">
  import { fadeIn, hoverScale } from '$lib/animations/actions';
  import { ArrowLeft } from 'lucide-svelte';
  
  interface Props {
    showBackToHome?: boolean;
    className?: string;
  }

  let {
    showBackToHome = true,
    className = ''
  }: Props = $props();
</script>

<footer class="space-y-6 {className}">
  <!-- Back to Home Link -->
  {#if showBackToHome}
    <div 
      class="text-center"
      use:fadeIn={{ delay: 0.2, duration: 0.8 }}
    >
      <a 
        href="/"
        class="inline-flex items-center gap-2 text-sm text-muted-foreground hover:text-foreground transition-colors duration-200 group"
        use:hoverScale={{ scale: 1.02 }}
      >
        <ArrowLeft class="h-4 w-4 group-hover:-translate-x-1 transition-transform duration-200" />
        <span>Back to home</span>
      </a>
    </div>
  {/if}

  <!-- Removed redundant navigation links - these are in the main site footer -->

  <!-- Removed redundant security features - these are in the main site footer -->

  <!-- Removed redundant support section - these links are in the main site footer -->

  <!-- Removed redundant company info - this is in the main site footer -->

  <!-- Removed redundant status indicator - this is in the main site footer -->
</footer>

<style>
  /* Smooth transitions for all interactive elements */
  a {
    transition: color 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  /* Enhanced hover effects */
  a:hover {
    text-decoration-thickness: 2px;
  }
  
  /* Responsive adjustments */
  @media (max-width: 640px) {
    .flex.flex-wrap.justify-center {
      flex-direction: column;
      align-items: center;
      gap: 0.75rem;
    }
    
    .gap-x-6 {
      gap: 1rem;
    }
  }
  
  /* Professional theme specific styling */
  :global([data-theme="professional"]) footer {
    background: linear-gradient(to bottom, transparent, var(--muted)/20);
  }
  
  /* NEO theme specific styling */
  :global([data-theme="neo"]) footer {
    background: linear-gradient(to bottom, transparent, var(--muted)/10);
  }
  
  :global([data-theme="neo"]) .border-t {
    border-width: 2px;
    border-style: solid;
  }
  
  /* Accessibility improvements */
  @media (prefers-reduced-motion: reduce) {
    .animate-pulse {
      animation: none;
    }
    
    .group-hover\:-translate-x-1 {
      transform: none !important;
    }
  }
  
  /* Focus states */
  a:focus {
    outline: 2px solid var(--primary);
    outline-offset: 2px;
    border-radius: 0.25rem;
  }
  
  /* High contrast mode */
  @media (prefers-contrast: high) {
    .text-muted-foreground {
      color: var(--foreground);
      opacity: 0.8;
    }
  }
</style>
