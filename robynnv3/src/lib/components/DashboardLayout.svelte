<script lang="ts">
  import { createEventDispatcher } from "svelte"
  import DashboardNavPanel from "./DashboardNavPanel.svelte"
  import DashboardContentArea from "./DashboardContentArea.svelte"
  import DashboardInsightsPanel from "./DashboardInsightsPanel.svelte"

  export let session: any
  export let environment: any
  export let allTools: any[] = []
  export let categoryStats: any = {}
  export let filteredTools: any[] = []

  const dispatch = createEventDispatcher()

  // Panel visibility state
  let leftPanelOpen = true
  let rightPanelOpen = false

  // Navigation state
  let selectedSection: 'content-board' | 'agents' | 'tools' | null = null
  let selectedAgent: string | null = null
  let selectedTool: any = null
  let selectedCategory: string | null = null

  // Dashboard data state
  let dashboardData = {
    recentActivity: [],
    usageStats: {},
    recommendations: []
  }

  function toggleLeftPanel() {
    leftPanelOpen = !leftPanelOpen
  }

  function toggleRightPanel() {
    rightPanelOpen = !rightPanelOpen
  }

  function handleSectionSelect(event: CustomEvent) {
    const { section, agent, tool, category } = event.detail
    selectedSection = section
    selectedAgent = agent || null
    selectedTool = tool || null
    selectedCategory = category || null
  }

  function handleAgentSelect(event: CustomEvent) {
    selectedSection = 'agents'
    selectedAgent = event.detail.agentId
    selectedTool = null
  }

  function handleToolSelect(event: CustomEvent) {
    selectedSection = 'tools'
    selectedTool = event.detail.tool
    selectedAgent = null
  }

  function handleContentBoardSelect() {
    selectedSection = 'content-board'
    selectedAgent = null
    selectedTool = null
  }

  // Event handlers for preserving existing functionality
  function handleToolClick(event: CustomEvent) {
    dispatch('toolClick', event.detail)
  }

  function handleAgentNavigation(event: CustomEvent) {
    dispatch('agentNavigation', event.detail)
  }
</script>

<div class="flex h-full bg-background">
  <!-- Left Panel - Navigation -->
  {#if leftPanelOpen}
    <div class="w-72 border-r border-border bg-card flex-shrink-0 transition-all duration-300">
      <DashboardNavPanel
        {session}
        {environment}
        {allTools}
        {categoryStats}
        {selectedSection}
        {selectedAgent}
        {selectedTool}
        on:sectionSelect={handleSectionSelect}
        on:agentSelect={handleAgentSelect}
        on:toolSelect={handleToolSelect}
        on:contentBoardSelect={handleContentBoardSelect}
        on:toolClick={handleToolClick}
        on:agentNavigation={handleAgentNavigation}
      />
    </div>
  {/if}

  <!-- Main Content Area -->
  <div class="flex-1 flex flex-col min-w-0">
    <DashboardContentArea
      {selectedSection}
      {selectedAgent}
      {selectedTool}
      {selectedCategory}
      {allTools}
      {filteredTools}
      {categoryStats}
      {leftPanelOpen}
      {rightPanelOpen}
      {dashboardData}
      {session}
      {environment}
      on:toggleLeftPanel={toggleLeftPanel}
      on:toggleRightPanel={toggleRightPanel}
      on:sectionSelect={handleSectionSelect}
      on:agentSelect={handleAgentSelect}
      on:toolSelect={handleToolSelect}
      on:toolClick={handleToolClick}
      on:agentNavigation={handleAgentNavigation}
    />
  </div>

  <!-- Right Panel - Insights -->
  {#if rightPanelOpen}
    <div class="w-80 border-l border-border bg-card flex-shrink-0 transition-all duration-300">
      <DashboardInsightsPanel
        {dashboardData}
        {session}
        {environment}
        on:close={toggleRightPanel}
      />
    </div>
  {/if}
</div>
