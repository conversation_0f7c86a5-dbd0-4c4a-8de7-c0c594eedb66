<script lang="ts">
  import { createEventDispatcher } from 'svelte'
  import { fade, scale } from 'svelte/transition'
  import { CheckCircle2, X, AlertTriangle } from 'lucide-svelte'

  export let isOpen: boolean = false
  export let title: string = "Confirm Action"
  export let message: string = "Are you sure you want to proceed?"
  export let confirmText: string = "Yes, proceed"
  export let cancelText: string = "Cancel"
  export let type: 'info' | 'warning' | 'success' = 'info'
  export let showPreview: boolean = false
  export let previewContent: string = ""

  const dispatch = createEventDispatcher()

  function handleConfirm() {
    dispatch('confirm')
    isOpen = false
  }

  function handleCancel() {
    dispatch('cancel')
    isOpen = false
  }

  function handleKeyDown(event: KeyboardEvent) {
    if (event.key === 'Escape') {
      handleCancel()
    } else if (event.key === 'Enter' && (event.ctrlKey || event.metaKey)) {
      handleConfirm()
    }
  }

  function getIcon() {
    switch (type) {
      case 'success':
        return CheckCircle2
      case 'warning':
        return AlertTriangle
      default:
        return CheckCircle2
    }
  }

  function getIconColor() {
    switch (type) {
      case 'success':
        return 'text-green-500'
      case 'warning':
        return 'text-orange-500'
      default:
        return 'text-blue-500'
    }
  }
</script>

<svelte:window on:keydown={handleKeyDown} />

{#if isOpen}
  <!-- Backdrop -->
  <div
    class="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4"
    transition:fade={{ duration: 200 }}
    on:click={handleCancel}
    role="dialog"
    aria-modal="true"
    aria-labelledby="dialog-title"
  >
    <!-- Dialog -->
    <div
      class="bg-background border-2 border-border shadow-soft max-w-md w-full max-h-[80vh] overflow-y-auto"
      transition:scale={{ duration: 200, start: 0.95 }}
      on:click|stopPropagation
    >
      <!-- Header -->
      <div class="p-6 border-b border-border">
        <div class="flex items-center gap-3">
          <svelte:component this={getIcon()} class="w-6 h-6 {getIconColor()}" />
          <h2 id="dialog-title" class="text-xl font-bold text-foreground">
            {title}
          </h2>
          <button
            on:click={handleCancel}
            class="ml-auto p-1 hover:bg-muted rounded transition-colors"
            aria-label="Close dialog"
          >
            <X class="w-5 h-5" />
          </button>
        </div>
      </div>

      <!-- Content -->
      <div class="p-6">
        <p class="text-muted-foreground mb-4">
          {message}
        </p>

        {#if showPreview && previewContent}
          <div class="mb-4">
            <h3 class="text-sm font-medium text-foreground mb-2">Preview:</h3>
            <div class="bg-muted p-3 rounded border max-h-40 overflow-y-auto">
              <div class="text-sm text-muted-foreground prose prose-sm max-w-none">
                {@html previewContent}
              </div>
            </div>
          </div>
        {/if}
      </div>

      <!-- Actions -->
      <div class="p-6 border-t border-border flex gap-3 justify-end">
        <button
          on:click={handleCancel}
          class="px-4 py-2 border-2 border-border hover:bg-muted transition-colors"
        >
          {cancelText}
        </button>
        <button
          on:click={handleConfirm}
          class="px-4 py-2 bg-primary text-primary-foreground border-2 border-border hover:bg-primary/90 transition-colors shadow-soft-sm"
        >
          {confirmText}
        </button>
      </div>
    </div>
  </div>
{/if}

<style>
  .shadow-soft {
    box-shadow: 4px 4px 0px 0px hsl(var(--border));
  }
  
  .shadow-soft-sm {
    box-shadow: 2px 2px 0px 0px hsl(var(--border));
  }
</style>
