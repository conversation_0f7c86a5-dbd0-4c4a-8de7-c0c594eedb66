<script lang="ts">
  import { writable } from 'svelte/store'
  import { createEventDispatcher } from 'svelte'
  import {
    Bo<PERSON>,
    User,
    Send,
    Loader2,
    X,
    ChevronRight,
    FileText,
    Lightbulb,
    CheckCircle2,
    Sparkles,
    Search,
    Quote,
    PenTool,
    List,
    Eye,
    MessageSquare,
    Zap
  } from 'lucide-svelte'
  import { slide, fade } from 'svelte/transition'

  export let documentId: string | null = null
  export let currentContent: string = ''
  export let selectedText: string = ''
  export let envSlug: string

  const dispatch = createEventDispatcher()

  interface Message {
    id: string
    role: 'user' | 'assistant'
    content: string
    timestamp: Date
    action?: string
  }

  interface ProgressStep {
    id: number
    title: string
    description: string
    status: 'pending' | 'active' | 'completed'
    progress?: number
  }

  const messages = writable<Message[]>([])
  let input = ''
  let isLoading = false
  let progressSteps: ProgressStep[] = []
  let showConfirmation = false
  let confirmationMessage = ''
  let confirmationAction: (() => void) | null = null
  let messagesContainer: HTMLElement

  // Quick actions for enhanced UX
  const quickActions = [
    {
      id: 'improve',
      label: 'Improve Writing',
      icon: Sparkles,
      prompt: 'Please improve the writing quality, clarity, and flow of this content.',
      requiresContent: true
    },
    {
      id: 'summarize',
      label: 'Summarize',
      icon: List,
      prompt: 'Please provide a concise summary of this content.',
      requiresContent: true
    },
    {
      id: 'expand',
      label: 'Expand Ideas',
      icon: Lightbulb,
      prompt: 'Please expand on the key ideas and add more detail to this content.',
      requiresContent: true
    },
    {
      id: 'outline',
      label: 'Create Outline',
      icon: FileText,
      prompt: 'Please create a detailed outline for content about: ',
      requiresContent: false
    },
    {
      id: 'research',
      label: 'Research Topic',
      icon: Search,
      prompt: 'Please research and provide key information about: ',
      requiresContent: false
    },
    {
      id: 'citations',
      label: 'Add Citations',
      icon: Quote,
      prompt: 'Please add proper citations and references to support the claims in this content.',
      requiresContent: true
    }
  ]

  function handleClose() {
    dispatch('close')
  }

  async function sendMessage(messageText?: string) {
    const messageToSend = messageText || input.trim()
    if (!messageToSend || isLoading) return

    const userMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content: messageToSend,
      timestamp: new Date()
    }

    messages.update(msgs => [...msgs, userMessage])
    input = ''
    isLoading = true

    // Initialize progress steps
    progressSteps = [
      { id: 1, title: 'Processing request', description: 'Understanding your message...', status: 'active', progress: 0 },
      { id: 2, title: 'Analyzing content', description: 'Reviewing document context...', status: 'pending' },
      { id: 3, title: 'Generating response', description: 'Creating helpful content...', status: 'pending' },
      { id: 4, title: 'Finalizing', description: 'Preparing final response...', status: 'pending' }
    ]

    try {
      // Determine context type
      let contextType = 'none'
      let contextContent = ''

      if (selectedText && selectedText.trim()) {
        contextType = 'selected'
        contextContent = selectedText
      } else if (currentContent && currentContent.trim()) {
        contextType = 'document'
        contextContent = currentContent
      }

      // Update progress
      updateProgress(1, 'completed', 100)
      updateProgress(2, 'active', 0)

      const response = await fetch(`/dashboard/${envSlug}/content-board?stream=true`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: messageToSend,
          documentId,
          currentContent: contextContent,
          selectedText,
          contextType
        })
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      updateProgress(2, 'completed', 100)
      updateProgress(3, 'active', 0)

      const reader = response.body?.getReader()
      const decoder = new TextDecoder()
      let assistantMessage: Message = {
        id: Date.now().toString(),
        role: 'assistant',
        content: '',
        timestamp: new Date()
      }

      messages.update(msgs => [...msgs, assistantMessage])

      if (reader) {
        let buffer = ''
        
        while (true) {
          const { done, value } = await reader.read()
          if (done) break

          buffer += decoder.decode(value, { stream: true })
          const lines = buffer.split('\n')
          buffer = lines.pop() || ''

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              try {
                const data = JSON.parse(line.slice(6))
                if (data.content) {
                  assistantMessage.content += data.content
                  messages.update(msgs => {
                    const updated = [...msgs]
                    updated[updated.length - 1] = { ...assistantMessage }
                    return updated
                  })
                  
                  // Update progress
                  const progress = Math.min(90, (assistantMessage.content.length / 10))
                  updateProgress(3, 'active', progress)
                }
              } catch (e) {
                console.error('Error parsing SSE data:', e)
              }
            }
          }
        }
      }

      updateProgress(3, 'completed', 100)
      updateProgress(4, 'active', 0)
      
      // Small delay for final step
      setTimeout(() => {
        updateProgress(4, 'completed', 100)
        progressSteps = []
      }, 500)

    } catch (error) {
      console.error('Error sending message:', error)
      
      const errorMessage: Message = {
        id: Date.now().toString(),
        role: 'assistant',
        content: 'Sorry, I encountered an error while processing your request. Please try again.',
        timestamp: new Date()
      }

      messages.update(msgs => [...msgs, errorMessage])
      progressSteps = []
    } finally {
      isLoading = false
      scrollToBottom()
    }
  }

  function updateProgress(stepId: number, status: 'pending' | 'active' | 'completed', progress?: number) {
    progressSteps = progressSteps.map(step => 
      step.id === stepId 
        ? { ...step, status, progress: progress ?? step.progress }
        : step
    )
  }

  function handleQuickAction(action: any) {
    if (action.requiresContent && !currentContent && !selectedText) {
      alert('Please select some text or ensure there is content in the document to use this action.')
      return
    }

    if (action.requiresContent) {
      sendMessage(action.prompt)
    } else {
      input = action.prompt
      // Focus the input for user to complete the prompt
      setTimeout(() => {
        const inputElement = document.querySelector('#agent-input') as HTMLInputElement
        if (inputElement) {
          inputElement.focus()
          inputElement.setSelectionRange(input.length, input.length)
        }
      }, 100)
    }
  }

  function scrollToBottom() {
    setTimeout(() => {
      if (messagesContainer) {
        messagesContainer.scrollTop = messagesContainer.scrollHeight
      }
    }, 100)
  }

  function formatTime(date: Date) {
    return date.toLocaleTimeString('en-US', { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: false 
    })
  }

  function handleKeyPress(event: KeyboardEvent) {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault()
      sendMessage()
    }
  }

  // Auto-scroll when new messages arrive
  $: if ($messages.length > 0) {
    scrollToBottom()
  }
</script>

<div class="flex flex-col h-full bg-card">
  <!-- Header -->
  <div class="flex items-center justify-between p-4 border-b border-border">
    <div class="flex items-center gap-3">
      <div class="p-2 bg-primary/10 rounded-lg">
        <MessageSquare class="w-5 h-5 text-primary" />
      </div>
      <div>
        <h3 class="font-semibold text-foreground">Content Assistant</h3>
        <p class="text-xs text-muted-foreground">AI-powered writing help</p>
      </div>
    </div>
    <button
      on:click={handleClose}
      class="p-1.5 hover:bg-muted rounded-lg transition-colors"
    >
      <X class="w-4 h-4 text-muted-foreground" />
    </button>
  </div>

  <!-- Context Indicator -->
  {#if selectedText || currentContent}
    <div class="px-4 py-2 bg-muted/50 border-b border-border">
      <div class="flex items-center gap-2 text-xs text-muted-foreground">
        <Eye class="w-3 h-3" />
        {#if selectedText}
          <span>Working with selected text ({selectedText.length} chars)</span>
        {:else if currentContent}
          <span>Working with document content</span>
        {/if}
      </div>
    </div>
  {/if}

  <!-- Quick Actions -->
  <div class="p-4 border-b border-border">
    <div class="grid grid-cols-2 gap-2">
      {#each quickActions as action}
        <button
          on:click={() => handleQuickAction(action)}
          class="flex items-center gap-2 p-2 text-xs bg-background hover:bg-muted border border-border rounded-lg transition-colors"
          disabled={isLoading}
        >
          <svelte:component this={action.icon} class="w-3 h-3 text-muted-foreground" />
          <span class="text-foreground">{action.label}</span>
        </button>
      {/each}
    </div>
  </div>

  <!-- Messages -->
  <div bind:this={messagesContainer} class="flex-1 overflow-y-auto p-4 space-y-4">
    {#if $messages.length === 0}
      <div class="text-center text-muted-foreground py-8">
        <Bot class="w-12 h-12 mx-auto mb-3 text-muted-foreground/50" />
        <p class="text-sm">Start a conversation with your content assistant</p>
        <p class="text-xs mt-1">Try the quick actions above or ask a question</p>
      </div>
    {/if}

    {#each $messages as message}
      <div class="flex gap-3 {message.role === 'user' ? 'justify-end' : 'justify-start'}">
        {#if message.role === 'assistant'}
          <div class="p-1.5 bg-primary/10 rounded-full flex-shrink-0">
            <Bot class="w-4 h-4 text-primary" />
          </div>
        {/if}
        
        <div class="max-w-[80%] {message.role === 'user' ? 'order-first' : ''}">
          <div class="p-3 rounded-lg {message.role === 'user' 
            ? 'bg-primary text-primary-foreground ml-auto' 
            : 'bg-muted text-foreground'}">
            <div class="text-sm whitespace-pre-wrap">{message.content}</div>
          </div>
          <div class="text-xs text-muted-foreground mt-1 {message.role === 'user' ? 'text-right' : 'text-left'}">
            {formatTime(message.timestamp)}
          </div>
        </div>

        {#if message.role === 'user'}
          <div class="p-1.5 bg-muted rounded-full flex-shrink-0">
            <User class="w-4 h-4 text-muted-foreground" />
          </div>
        {/if}
      </div>
    {/each}

    <!-- Progress Steps -->
    {#if progressSteps.length > 0}
      <div class="space-y-2" transition:fade>
        {#each progressSteps as step}
          <div class="flex items-center gap-3 p-2 bg-muted/50 rounded-lg">
            <div class="flex-shrink-0">
              {#if step.status === 'completed'}
                <CheckCircle2 class="w-4 h-4 text-green-500" />
              {:else if step.status === 'active'}
                <Loader2 class="w-4 h-4 text-primary animate-spin" />
              {:else}
                <div class="w-4 h-4 rounded-full border-2 border-muted-foreground/30"></div>
              {/if}
            </div>
            <div class="flex-1 min-w-0">
              <div class="text-sm font-medium text-foreground">{step.title}</div>
              <div class="text-xs text-muted-foreground">{step.description}</div>
              {#if step.status === 'active' && step.progress !== undefined}
                <div class="w-full bg-muted rounded-full h-1 mt-1">
                  <div class="bg-primary h-1 rounded-full transition-all duration-300" style="width: {step.progress}%"></div>
                </div>
              {/if}
            </div>
          </div>
        {/each}
      </div>
    {/if}
  </div>

  <!-- Input -->
  <div class="p-4 border-t border-border">
    <div class="flex gap-2">
      <textarea
        id="agent-input"
        bind:value={input}
        on:keydown={handleKeyPress}
        placeholder="Ask me anything about your content..."
        class="flex-1 resize-none px-3 py-2 text-sm border border-border rounded-lg bg-background focus:outline-none focus:ring-2 focus:ring-primary min-h-[40px] max-h-[120px]"
        disabled={isLoading}
        rows="1"
      ></textarea>
      <button
        on:click={() => sendMessage()}
        disabled={!input.trim() || isLoading}
        class="p-2 bg-primary text-primary-foreground hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed rounded-lg transition-colors"
      >
        {#if isLoading}
          <Loader2 class="w-4 h-4 animate-spin" />
        {:else}
          <Send class="w-4 h-4" />
        {/if}
      </button>
    </div>
  </div>
</div>
