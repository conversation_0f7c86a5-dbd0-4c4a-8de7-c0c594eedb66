import { env } from '$env/dynamic/private'
import type { 
  FirecrawlScrapeResult, 
  FirecrawlExtractResult, 
  FirecrawlSearchResult,
  FirecrawlError 
} from '$lib/types/firecrawl'

// Configuration constants
export const FIRECRAWL_CONFIG = {
  baseUrl: 'https://api.firecrawl.dev',
  timeout: 30000, // 30 seconds default timeout
  maxRetries: 3,
  retryDelay: 1000 // 1 second
} as const

// Firecrawl context interface
export interface FirecrawlContext {
  apiKey: string
  baseUrl: string
  timeout: number
}

// Get Firecrawl configuration from environment
export function getFirecrawlContext(): FirecrawlContext {
  const apiKey = env.FIRECRAWL_API_KEY
  if (!apiKey) {
    throw new Error('FIRECRAWL_API_KEY environment variable is required')
  }

  return {
    apiKey,
    baseUrl: FIRECRAWL_CONFIG.baseUrl,
    timeout: FIRECRAWL_CONFIG.timeout
  }
}

// Check if Firecrawl is properly configured
export function isFirecrawlConfigured(): boolean {
  return !!env.FIRECRAWL_API_KEY
}

// Create headers for Firecrawl API requests
export function createFirecrawlHeaders(apiKey: string): Record<string, string> {
  return {
    'Authorization': `Bearer ${apiKey}`,
    'Content-Type': 'application/json',
    'User-Agent': 'Robynn-Deep-Researcher/1.0'
  }
}

// Create timeout controller for requests
export function createTimeoutController(timeoutMs: number) {
  const controller = new AbortController()
  const timeoutId = setTimeout(() => {
    controller.abort()
  }, timeoutMs)

  return { controller, timeoutId }
}

// Parse Firecrawl API errors
export function parseFirecrawlError(error: any, statusCode?: number): FirecrawlError {
  if (error instanceof Error && error.name === 'AbortError') {
    return {
      type: 'timeout',
      message: 'Request timed out',
      statusCode: 408,
      retryable: true
    }
  }

  if (statusCode) {
    switch (statusCode) {
      case 401:
        return {
          type: 'authentication',
          message: 'Invalid API key or authentication failed',
          statusCode: 401,
          retryable: false
        }
      case 429:
        return {
          type: 'rate_limit',
          message: 'Rate limit exceeded. Please try again later.',
          statusCode: 429,
          retryable: true
        }
      case 500:
        return {
          type: 'server_error',
          message: 'Firecrawl server error. Please try again.',
          statusCode: 500,
          retryable: true
        }
      case 502:
        return {
          type: 'server_error',
          message: 'Firecrawl Bad Gateway (502). This may indicate: 1) Firecrawl service is temporarily down, 2) Target website is blocking crawlers, 3) API key issues. Check Firecrawl status and try again.',
          statusCode: 502,
          retryable: true
        }
      case 503:
        return {
          type: 'server_error',
          message: 'Firecrawl service unavailable (503). Please try again later.',
          statusCode: 503,
          retryable: true
        }
      case 504:
        return {
          type: 'timeout',
          message: 'Firecrawl gateway timeout (504). The target website may be slow to respond.',
          statusCode: 504,
          retryable: true
        }
      default:
        return {
          type: 'api_error',
          message: error.message || `API error (${statusCode})`,
          statusCode,
          retryable: statusCode >= 500
        }
    }
  }

  return {
    type: 'unknown',
    message: error.message || 'Unknown error occurred',
    statusCode: 0,
    retryable: false
  }
}

// Handle Firecrawl errors with structured responses (no throwing for agent resilience)
export function handleFirecrawlError(error: FirecrawlError): any {
  const baseErrorResponse = {
    success: false,
    confidence_score: 0.0,
    processing_time: '0s',
    source: 'firecrawl-error',
    fallback_data: null,
    note: 'Firecrawl tool failed - research continued with other available tools'
  }

  switch (error.type) {
    case 'authentication':
      return {
        ...baseErrorResponse,
        error: 'Firecrawl authentication failed. Please check your API key.',
        type: 'authentication_error'
      }
    case 'rate_limit':
      return {
        ...baseErrorResponse,
        error: 'Firecrawl rate limit exceeded. Please try again in a few minutes.',
        type: 'rate_limit_error'
      }
    case 'timeout':
      return {
        ...baseErrorResponse,
        error: 'Firecrawl request timed out. The target website may be slow to respond.',
        type: 'timeout_error'
      }
    case 'server_error':
      return {
        ...baseErrorResponse,
        error: 'Firecrawl server error. Please try again later.',
        type: 'server_error'
      }
    default:
      return {
        ...baseErrorResponse,
        error: `Firecrawl error: ${error.message}`,
        type: 'api_error'
      }
  }
}

// URL validation and normalization
export function validateFirecrawlUrl(url: string): boolean {
  try {
    const parsedUrl = new URL(url)
    return parsedUrl.protocol === 'http:' || parsedUrl.protocol === 'https:'
  } catch {
    return false
  }
}

export function normalizeUrl(url: string): string {
  try {
    const parsedUrl = new URL(url)
    return parsedUrl.toString()
  } catch {
    return url
  }
}

export function extractDomain(url: string): string {
  try {
    const parsedUrl = new URL(url)
    return parsedUrl.hostname
  } catch {
    return 'unknown'
  }
}

// Calculate confidence score based on data quality
export function calculateFirecrawlConfidence(
  hasContent: boolean,
  hasMetadata: boolean,
  hasStructuredData: boolean,
  statusCode?: number
): number {
  let confidence = 0.5 // Base confidence

  if (hasContent) confidence += 0.2
  if (hasMetadata) confidence += 0.1
  if (hasStructuredData) confidence += 0.1

  // Adjust based on HTTP status
  if (statusCode === 200) confidence += 0.1
  else if (statusCode && statusCode >= 400) confidence -= 0.2

  return Math.max(0.1, Math.min(0.95, confidence))
}

// Mock data generators for testing
export function generateMockScrapeResult(url: string): FirecrawlScrapeResult {
  const domain = extractDomain(url)
  return {
    success: true,
    url,
    markdown: `# ${domain} Analysis\n\nThis is mock content for testing purposes.\n\n## Key Features\n- Feature 1\n- Feature 2\n- Feature 3`,
    html: `<h1>${domain} Analysis</h1><p>This is mock content for testing purposes.</p>`,
    metadata: {
      title: `${domain} - Company Website`,
      description: `Mock description for ${domain}`,
      statusCode: 200,
      error: null
    },
    extractedData: {
      company_name: domain,
      value_propositions: ['Innovation', 'Quality', 'Customer Focus'],
      pricing_strategy: 'Competitive pricing with premium features'
    }
  }
}

export function generateMockExtractResults(urls: string[]): FirecrawlExtractResult[] {
  return urls.map(url => ({
    url,
    extractedData: {
      title: `Mock Title for ${extractDomain(url)}`,
      description: `Mock description extracted from ${url}`,
      key_points: ['Point 1', 'Point 2', 'Point 3']
    },
    metadata: {
      statusCode: 200,
      title: `Mock Title for ${extractDomain(url)}`,
      description: `Mock description for ${extractDomain(url)}`
    }
  }))
}

export function generateMockSearchResults(query: string, limit: number): FirecrawlSearchResult[] {
  const results: FirecrawlSearchResult[] = []
  
  for (let i = 0; i < limit; i++) {
    results.push({
      url: `https://example${i + 1}.com/search-result`,
      title: `Mock Search Result ${i + 1} for "${query}"`,
      markdown: `# Search Result ${i + 1}\n\nThis is mock content related to "${query}".\n\n## Key Information\n- Relevant point 1\n- Relevant point 2`,
      html: `<h1>Search Result ${i + 1}</h1><p>This is mock content related to "${query}".</p>`,
      metadata: {
        title: `Mock Search Result ${i + 1}`,
        description: `Mock description for search result ${i + 1}`,
        statusCode: 200
      }
    })
  }
  
  return results
}
