/**
 * Research Result Parser
 * 
 * Parses raw JSON responses from the Deep Research Agent into structured data
 * for display in the enhanced UI components.
 */

export interface ParsedCompany {
  name: string
  domain: string
  industry: string
  employee_count: string | number
  location: string
  founded_year: string | number
  revenue: string | number
  description: string
  confidence_score: number
  source: string
  contacts?: ParsedContact[]
  web_presence?: any
  technical_analysis?: any
}

export interface ParsedContact {
  name: string
  first_name: string
  last_name: string
  title: string
  email?: string
  phone?: string
  linkedin_url?: string
  company_name: string
  company_domain: string
  location: string
  seniority: string
  department: string
  confidence_score: number
  source: string
}

export interface ParsedResearchResult {
  target_company?: ParsedCompany
  competitor_companies?: ParsedCompany[]
  all_contacts?: ParsedContact[]
  web_analysis?: any
  technical_insights?: any
  summary?: string
  confidence_score?: number
  sources_used?: string[]
  api_calls_made?: any
  tool_status?: any
  processing_time?: string
  data_freshness?: string
}

/**
 * Parse raw research response into structured format
 */
export function parseResearchResult(rawContent: string): ParsedResearchResult | null {
  try {
    // Try to extract JSON from markdown code blocks or raw JSON
    let jsonContent = rawContent
    
    // Remove markdown code block formatting if present
    const jsonMatch = rawContent.match(/```json\s*([\s\S]*?)\s*```/) || 
                     rawContent.match(/```\s*([\s\S]*?)\s*```/)
    
    if (jsonMatch) {
      jsonContent = jsonMatch[1]
    }
    
    // Clean up common formatting issues
    jsonContent = jsonContent
      .replace(/^\s*#+.*$/gm, '') // Remove markdown headers
      .replace(/^\s*\*\*.*?\*\*\s*$/gm, '') // Remove bold text lines
      .replace(/^\s*-\s+/gm, '') // Remove list markers
      .trim()
    
    // Try to parse as JSON
    const parsed = JSON.parse(jsonContent)
    
    return {
      target_company: parseCompany(parsed.target_company),
      competitor_companies: parsed.competitor_companies?.map(parseCompany) || [],
      all_contacts: parseContacts(parsed.all_contacts || parsed.contacts),
      web_analysis: parsed.web_analysis,
      technical_insights: parsed.technical_insights,
      summary: parsed.summary || parsed.executive_summary,
      confidence_score: parsed.confidence_score || 0.75,
      sources_used: parsed.sources_used || [],
      api_calls_made: parsed.api_calls_made,
      tool_status: parsed.tool_status,
      processing_time: parsed.processing_time,
      data_freshness: parsed.data_freshness
    }
  } catch (error) {
    console.warn('Failed to parse research result as JSON:', error)
    return null
  }
}

/**
 * Parse company data with fallbacks
 */
function parseCompany(companyData: any): ParsedCompany | undefined {
  if (!companyData) return undefined
  
  return {
    name: companyData.name || companyData.company_name || 'Unknown Company',
    domain: companyData.domain || companyData.website || companyData.company_domain || '',
    industry: companyData.industry || companyData.sector || 'Unknown Industry',
    employee_count: companyData.employee_count || companyData.employees || companyData.size || 'N/A',
    location: companyData.location || companyData.headquarters || companyData.address || 'Unknown Location',
    founded_year: companyData.founded_year || companyData.founded || companyData.year_founded || 'N/A',
    revenue: companyData.revenue || companyData.annual_revenue || companyData.estimated_revenue || 'N/A',
    description: companyData.description || companyData.short_description || companyData.summary || 'No description available',
    confidence_score: companyData.confidence_score || 0.75,
    source: companyData.source || 'apollo',
    contacts: parseContacts(companyData.contacts),
    web_presence: companyData.web_presence || companyData.website_analysis,
    technical_analysis: companyData.technical_analysis || companyData.tech_stack
  }
}

/**
 * Parse contacts data with fallbacks
 */
function parseContacts(contactsData: any): ParsedContact[] {
  if (!contactsData) return []
  
  // Handle different contact data structures
  let contacts: any[] = []
  
  if (Array.isArray(contactsData)) {
    contacts = contactsData
  } else if (typeof contactsData === 'object') {
    // Handle contacts_by_company structure
    contacts = Object.values(contactsData).flat()
  }
  
  return contacts.map(contact => ({
    name: contact.name || `${contact.first_name || ''} ${contact.last_name || ''}`.trim() || 'Unknown Contact',
    first_name: contact.first_name || '',
    last_name: contact.last_name || '',
    title: contact.title || contact.job_title || 'Unknown Title',
    email: contact.email || null,
    phone: contact.phone || contact.phone_number || null,
    linkedin_url: contact.linkedin_url || contact.linkedin || null,
    company_name: contact.company_name || contact.organization?.name || 'Unknown Company',
    company_domain: contact.company_domain || contact.domain || '',
    location: contact.location || [contact.city, contact.state, contact.country].filter(Boolean).join(', ') || 'Unknown Location',
    seniority: contact.seniority || contact.level || 'Unknown',
    department: contact.department || contact.departments?.[0] || 'Unknown',
    confidence_score: contact.confidence_score || 0.75,
    source: contact.source || 'apollo'
  }))
}

/**
 * Check if content appears to be structured research data
 */
export function isStructuredResearchData(content: string): boolean {
  if (!content || content.length < 50) {
    return false
  }

  // Look for common research data indicators - need at least 2 to be confident
  const indicators = [
    'target_company',
    'competitor_companies',
    'contacts_by_company',
    'all_contacts',
    'web_analysis',
    'technical_insights',
    'api_calls_made',
    'confidence_score'
  ]

  const foundIndicators = indicators.filter(indicator => content.includes(indicator))
  const result = foundIndicators.length >= 2

  return result
}

/**
 * Extract summary from research content
 */
export function extractResearchSummary(content: string): string | null {
  try {
    const parsed = JSON.parse(content)
    return parsed.summary || parsed.executive_summary || null
  } catch {
    // Try to extract summary from markdown
    const summaryMatch = content.match(/## Summary\s*([\s\S]*?)(?=\n##|\n\*\*|$)/i) ||
                         content.match(/\*\*Summary\*\*\s*([\s\S]*?)(?=\n\*\*|$)/i)
    
    return summaryMatch ? summaryMatch[1].trim() : null
  }
}

/**
 * Get tool status from research result
 */
export function getToolStatus(content: string): any {
  try {
    const parsed = JSON.parse(content)
    return parsed.tool_status || {}
  } catch {
    return {}
  }
}

/**
 * Format confidence score for display
 */
export function formatConfidenceScore(score: number): string {
  return `${Math.round(score * 100)}%`
}

/**
 * Get confidence color class
 */
export function getConfidenceColorClass(score: number): string {
  if (score >= 0.8) return 'text-green-600'
  if (score >= 0.6) return 'text-yellow-600'
  return 'text-red-600'
}
