// Auto-generated Mastra registry
// This file is automatically generated by src/scripts/update-registry.js
// Do not edit manually - changes will be overwritten

import { apolloFindCompaniesTool } from './tools/apollo-find-companies-tool.js'
import { apolloFindContactsTool } from './tools/apollo-find-contacts-tool.js'
import { apolloSearchCompanyTool } from './tools/apollo-search-company-tool.js'
import { braveSearchTool } from './tools/brave-search-tool.js'
import { citationTool } from './tools/citation-tool.js'
import { contentGenerationTool } from './tools/content-generation-tool.js'
import { domainIntersectionTool } from './tools/domain-intersection-tool.js'
import { exaSearchEnhancedTool } from './tools/exa-search-enhanced-tool.js'
import { firecrawlExtractTool } from './tools/firecrawl-extract-tool.js'
import { firecrawlScrapeTool } from './tools/firecrawl-scrape-tool.js'
import { firecrawlSearchTool } from './tools/firecrawl-search-tool.js'
import { grammarStyleTool } from './tools/grammar-style-tool.js'
import { keywordDifficultyTool } from './tools/keyword-difficulty-tool.js'
import { keywordVolumeTool } from './tools/keyword-volume-tool.js'
import { keywordsForSiteTool } from './tools/keywords-for-site-tool.js'
import { outlineGenerationTool } from './tools/outline-generation-tool.js'
import { relatedKeywordsTool } from './tools/related-keywords-tool.js'
import { serpKeywordVolumeTool } from './tools/serp-api-tool.js'
import { serpKeywordDifficultyTool } from './tools/serp-api-tool.js'
import { simpleWebSearchTool } from './tools/simple-web-search.js'
import { textSummarizationTool } from './tools/text-summarization-tool.js'
import { webSearchTool } from './tools/web-search-tool.js'

import { companyResearcherAgent } from './agents/company-researcher.js'
import { contentAgent } from './agents/content-agent.js'
import { orchestratorAgent } from './agents/orchestrator-agent.js'
import { seoStrategistAgent } from './agents/seo-strategist.js'

// Tool exports
export const tools = [
  apolloFindCompaniesTool,
  apolloFindContactsTool,
  apolloSearchCompanyTool,
  braveSearchTool,
  citationTool,
  contentGenerationTool,
  domainIntersectionTool,
  exaSearchEnhancedTool,
  firecrawlExtractTool,
  firecrawlScrapeTool,
  firecrawlSearchTool,
  grammarStyleTool,
  keywordDifficultyTool,
  keywordVolumeTool,
  keywordsForSiteTool,
  outlineGenerationTool,
  relatedKeywordsTool,
  serpKeywordVolumeTool,
  serpKeywordDifficultyTool,
  simpleWebSearchTool,
  textSummarizationTool,
  webSearchTool
]

// Agent exports
export const agents = [
  companyResearcherAgent,
  contentAgent,
  orchestratorAgent,
  seoStrategistAgent
]

// Tools organized by category
export const toolsByCategory = {
  'Lead Generation': [
    apolloFindCompaniesTool,
    apolloFindContactsTool,
    apolloSearchCompanyTool
  ],
  'Search & Discovery': [
    braveSearchTool,
    exaSearchEnhancedTool,
    firecrawlExtractTool,
    firecrawlSearchTool,
    keywordVolumeTool,
    simpleWebSearchTool,
    webSearchTool
  ],
  'Utility': [
    citationTool
  ],
  'Content & Writing': [
    contentGenerationTool,
    grammarStyleTool,
    outlineGenerationTool,
    textSummarizationTool
  ],
  'SEO & Keywords': [
    domainIntersectionTool,
    keywordDifficultyTool,
    keywordsForSiteTool,
    relatedKeywordsTool,
    serpKeywordVolumeTool,
    serpKeywordDifficultyTool
  ],
  'Web Scraping': [
    firecrawlScrapeTool
  ]
}

// Tool metadata for dashboard
export const toolMetadata = {
  'apollo_find_companies': {
    name: 'apolloFindCompaniesTool',
    id: 'apollo_find_companies',
    description: 'Discover matching companies based on attributes using Apollo API',
    category: 'Lead Generation'
  },
  'apollo_find_contacts': {
    name: 'apolloFindContactsTool',
    id: 'apollo_find_contacts',
    description: 'Retrieve contacts for companies using Apollo API',
    category: 'Lead Generation'
  },
  'apollo_search_company': {
    name: 'apolloSearchCompanyTool',
    id: 'apollo_search_company',
    description: 'Enrich target company data using Apollo API by domain or company name',
    category: 'Lead Generation'
  },
  'brave-search': {
    name: 'braveSearchTool',
    id: 'brave-search',
    description: 'Fallback web search tool using Brave Search API for company research and competitive analysis',
    category: 'Search & Discovery'
  },
  'citation-management': {
    name: 'citationTool',
    id: 'citation-management',
    description: 'Generate properly formatted citations in various academic and professional styles',
    category: 'Utility'
  },
  'content-generation': {
    name: 'contentGenerationTool',
    id: 'content-generation',
    description: 'Generate high-quality content based on topic, type, audience, and style preferences',
    category: 'Content & Writing'
  },
  'get_domain_intersection': {
    name: 'domainIntersectionTool',
    id: 'get_domain_intersection',
    description: 'Find keywords where two domains both rank in Google SERPs using DataForSEO Domain Intersection API',
    category: 'SEO & Keywords'
  },
  'exa_search': {
    name: 'exaSearchEnhancedTool',
    id: 'exa_search',
    description: 'Find competitors and deep company intelligence using Exa AI',
    category: 'Search & Discovery'
  },
  'firecrawl-extract': {
    name: 'firecrawlExtractTool',
    id: 'firecrawl-extract',
    description: 'Bulk data extraction from multiple URLs with schema-driven structured data extraction and natural language processing. Perfect for directory scraping, competitor product catalogs, and market research across multiple sources.',
    category: 'Search & Discovery'
  },
  'firecrawl-scrape': {
    name: 'firecrawlScrapeTool',
    id: 'firecrawl-scrape',
    description: 'Deep single-page analysis with AI-powered content extraction, JavaScript rendering, and user interaction simulation. Ideal for competitor landing pages, pricing intelligence, and real-time content monitoring.',
    category: 'Web Scraping'
  },
  'firecrawl-search': {
    name: 'firecrawlSearchTool',
    id: 'firecrawl-search',
    description: 'Web search with automatic result scraping, geo-targeting, and full content extraction. Ideal for competitor discovery, trend analysis, content research, and market intelligence gathering.',
    category: 'Search & Discovery'
  },
  'grammar-style-correction': {
    name: 'grammarStyleTool',
    id: 'grammar-style-correction',
    description: 'Check and correct grammar, style, tone, and readability of text content',
    category: 'Content & Writing'
  },
  'get_keyword_difficulty': {
    name: 'keywordDifficultyTool',
    id: 'get_keyword_difficulty',
    description: 'Get SEO keyword difficulty scores (0-100) for a list of keywords using DataForSEO Labs API',
    category: 'SEO & Keywords'
  },
  'get_keyword_volume': {
    name: 'keywordVolumeTool',
    id: 'get_keyword_volume',
    description: 'Get monthly search volume data for a list of keywords using DataForSEO Google Ads API',
    category: 'Search & Discovery'
  },
  'get_keywords_for_site': {
    name: 'keywordsForSiteTool',
    id: 'get_keywords_for_site',
    description: 'Get all keywords a website or webpage ranks for using DataForSEO Keywords for Site API',
    category: 'SEO & Keywords'
  },
  'outline-generation': {
    name: 'outlineGenerationTool',
    id: 'outline-generation',
    description: 'Generate structured, hierarchical outlines for various types of content',
    category: 'Content & Writing'
  },
  'get_related_keywords': {
    name: 'relatedKeywordsTool',
    id: 'get_related_keywords',
    description: 'Discover related and long-tail keywords based on seed keywords using DataForSEO Keywords For Keywords API',
    category: 'SEO & Keywords'
  },
  'serp-api': {
    name: 'serpKeywordVolumeTool',
    id: 'serp-api',
    description: 'Fallback keyword volume analysis using SERP API',
    category: 'SEO & Keywords'
  },
  'serp-api': {
    name: 'serpKeywordDifficultyTool',
    id: 'serp-api',
    description: 'Fallback keyword volume analysis using SERP API',
    category: 'SEO & Keywords'
  },
  'simple-web-search': {
    name: 'simpleWebSearchTool',
    id: 'simple-web-search',
    description: 'Simple web search using Exa',
    category: 'Search & Discovery'
  },
  'text-summarization': {
    name: 'textSummarizationTool',
    id: 'text-summarization',
    description: 'Create concise, focused summaries of text content in various formats and lengths',
    category: 'Content & Writing'
  },
  'web-search': {
    name: 'webSearchTool',
    id: 'web-search',
    description: 'Search the web for comprehensive information about companies using Exa AI',
    category: 'Search & Discovery'
  }
}

// Agent metadata for dashboard
export const agentMetadata = {
  'companyResearcherAgent': {
    name: 'companyResearcherAgent',
    displayName: 'Company Researcher',
    description: 'Agent from company-researcher.js'
  },
  'contentAgent': {
    name: 'contentAgent',
    displayName: 'Content Agent',
    description: 'Agent from content-agent.js'
  },
  'orchestratorAgent': {
    name: 'orchestratorAgent',
    displayName: 'Deep Researcher Agent',
    description: 'Agent from orchestrator-agent.js'
  },
  'seoStrategistAgent': {
    name: 'seoStrategistAgent',
    displayName: 'SEO Strategist',
    description: 'Agent from seo-strategist.js'
  }
}

// All available tools and agents
export default {
  tools,
  agents,
  toolsByCategory,
  toolMetadata,
  agentMetadata
}
