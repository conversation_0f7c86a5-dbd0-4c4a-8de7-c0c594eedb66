// Simple web search tool for Mastra Studio
import { z } from "zod"
import { createTool } from "@mastra/core"
import Exa from "exa-js"
import { ENV_VARS } from '../shared/env.js'

const webSearchSchema = z.object({
  query: z.string().describe("Search query"),
  numResults: z.number().default(5).describe("Number of results")
})

export const simpleWebSearchTool = createTool({
  id: "simple-web-search",
  description: "Simple web search using Exa",
  inputSchema: webSearchSchema,
  execute: async (context) => {
    const { query, numResults } = context.context
    
    const apiKey = ENV_VARS.EXA_API_KEY()
    if (!apiKey) {
      throw new Error("EXA_API_KEY not configured")
    }
    
    const exa = new Exa(apiKey)
    
    try {
      const results = await exa.search(query, {
        numResults: Math.min(numResults, 10)
      })
      
      return {
        success: true,
        results: results.results.map(result => ({
          title: result.title,
          url: result.url,
          snippet: result.snippet || 'No snippet available'
        }))
      }
    } catch (error) {
      return {
        success: false,
        error: error.message
      }
    }
  }
})
