// Auto-generated Mastra tool from apollo-find-companies-tool.ts
// This file is automatically generated by src/scripts/sync-mastra.js
// Do not edit manually - changes will be overwritten

import { z } from 'zod'
import { createTool } from '@mastra/core'
import { ENV_VARS } from '../shared/env.js'

const apolloFindCompaniesSchema = z.object({
  industry: z.string().optional().describe('Industry to search for (e.g., Technology, Software)'),
  employee_range: z.string().optional().describe('Employee count range (e.g., 201-500, 501-1000)'),
  revenue_range: z.string().optional().describe('Revenue range (e.g., $50M-$100M)'),
  location: z.string().optional().describe('Company location (e.g., San Francisco, CA)'),
  technologies: z.array(z.string()).optional().describe('Technologies used by the company'),
  keywords: z.array(z.string()).optional().describe('Keywords to search for in company descriptions'),
  limit: z.number().optional().default(10).describe('Maximum number of companies to return (max 25)')
})

export const apolloFindCompaniesTool = createTool({
  id: 'apollo_find_companies',
  description: 'Discover matching companies based on attributes using Apollo API',
  inputSchema: apolloFindCompaniesSchema,
  execute: async (context) => {
    const { industry, employee_range, revenue_range, location, technologies, keywords, limit } = context.context

    const apiKey = ENV_VARS.APOLLO_API_KEY()
    if (!apiKey) {
      console.error('Apollo API credentials missing - returning mock data for testing')
      // Return mock data instead of throwing error
      const mockCompanies = Array.from({ length: Math.min(limit, 5) }, (_, i) => ({
        id: `mock-company-${i + 1}`,
        name: `Sample Company ${i + 1}`,
        domain: `sample${i + 1}.com`,
        industry: industry || 'Technology',
        employees: employee_range || '201-500',
        revenue: revenue_range || '$50M-$100M',
        description: `Mock company ${i + 1} for testing purposes`,
        location: location || 'San Francisco, CA',
        founded_year: 2020 - i,
        website_url: `https://sample${i + 1}.com`
      }))

      return {
        success: true,
        companies: mockCompanies,
        total_results: mockCompanies.length,
        note: 'Mock data returned - Apollo API credentials not configured'
      }
    }

    try {
      // Build search filters for Apollo Organization Search API
      const searchFilters = {}

      if (industry) {
        searchFilters.q_organization_keyword_tags = [industry]
      }

      if (employee_range) {
        // Parse employee range and convert to Apollo format
        const match = employee_range.match(/(\d+)-(\d+)/)
        if (match) {
          searchFilters.organization_num_employees_ranges = [`${match[1]},${match[2]}`]
        }
      }

      if (location) {
        searchFilters.q_organization_locations = [location]
      }

      if (technologies && technologies.length > 0) {
        searchFilters.organization_technology_names = technologies
      }

      if (keywords && keywords.length > 0) {
        searchFilters.q_keywords = keywords.join(' ')
      }

      const requestData = {
        ...searchFilters,
        page: 1,
        per_page: Math.min(limit, 25), // Apollo API limit
        organization_locations: searchFilters.q_organization_locations,
        person_locations: undefined // Focus on organization search
      }

      console.log('Making Apollo organization search request with filters:', requestData)
      
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), 30000) // 30 second timeout
      
      const response = await fetch('https://api.apollo.io/api/v1/mixed_companies/search', {
        method: 'POST',
        headers: {
          'X-Api-Key': apiKey,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData),
        signal: controller.signal
      })
      
      clearTimeout(timeoutId)

      if (!response.ok) {
        throw new Error(`Apollo API error: ${response.status} ${response.statusText}`)
      }

      const data = await response.json()
      
      if (!data.organizations) {
        throw new Error('No organizations data found in Apollo response')
      }

      const companies = data.organizations.map((org) => ({
        id: org.id,
        name: org.name,
        domain: org.primary_domain,
        industry: org.industry,
        employees: org.estimated_num_employees ? `${org.estimated_num_employees}` : 'Unknown',
        revenue: org.annual_revenue ? `$${org.annual_revenue}` : 'Unknown',
        description: org.short_description || org.description,
        location: [org.primary_city, org.primary_state, org.primary_country].filter(Boolean).join(', '),
        founded_year: org.founded_year,
        website_url: org.website_url,
        linkedin_url: org.linkedin_url,
        phone: org.phone,
        technologies: org.technologies?.map((tech) => tech.name) || []
      }))

      return {
        success: true,
        companies,
        total_results: data.pagination?.total_entries || companies.length,
        page: data.pagination?.page || 1,
        per_page: data.pagination?.per_page || limit
      }

    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('Apollo API request timeout after 30 seconds')
      }
      
      console.error('Apollo find companies error:', error)
      throw new Error(`Apollo API error: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }
})
