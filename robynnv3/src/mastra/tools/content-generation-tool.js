// Auto-generated Mastra tool from content-generation-tool.ts
// This file is automatically generated by src/scripts/sync-mastra.js
// Do not edit manually - changes will be overwritten

import { z } from "zod"
import { createTool } from "@mastra/core"

const contentGenerationSchema = z.object({
  topic: z.string().describe("The main topic or subject for the content"),
  contentType: z
    .enum([
      "article",
      "blog-post", 
      "whitepaper",
      "social-media",
      "email",
      "documentation",
      "press-release",
      "case-study",
      "newsletter",
      "landing-page"
    ])
    .describe("Type of content to generate"),
  targetAudience: z
    .enum([
      "general",
      "technical", 
      "executive",
      "marketing",
      "academic",
      "beginner",
      "intermediate",
      "expert"
    ])
    .describe("Target audience for the content"),
  tone: z
    .enum([
      "professional",
      "casual",
      "friendly", 
      "authoritative",
      "conversational",
      "formal",
      "persuasive",
      "educational"
    ])
    .default("professional")
    .describe("Tone and style of the content"),
  length: z
    .enum([
      "short", // 200-500 words
      "medium", // 500-1000 words  
      "long", // 1000-2000 words
      "extended" // 2000+ words
    ])
    .default("medium")
    .describe("Desired length of the content"),
  keywords: z
    .array(z.string())
    .optional()
    .describe("Optional keywords to include for SEO optimization"),
  includeIntroduction: z
    .boolean()
    .default(true)
    .describe("Whether to include an introduction section"),
  includeConclusion: z
    .boolean()
    .default(true)
    .describe("Whether to include a conclusion section"),
  sections: z
    .array(z.string())
    .optional()
    .describe("Specific sections or topics to cover in the content")
})

export const contentGenerationTool = createTool({
  id: "content-generation",
  description: "Generate high-quality content based on topic, type, audience, and style preferences",
  inputSchema: contentGenerationSchema,
  execute: async (context) => {
    const {
      topic,
      contentType,
      targetAudience,
      tone,
      length,
      keywords,
      includeIntroduction,
      includeConclusion,
      sections
    } = context.context

    // Generate word count targets based on length
    const wordCountTargets = {
      short: { min: 200, max: 500, target: 350 },
      medium: { min: 500, max: 1000, target: 750 },
      long: { min: 1000, max: 2000, target: 1500 },
      extended: { min: 2000, max: 4000, target: 3000 }
    }

    const targetWords = wordCountTargets[length]

    // Build content structure based on type
    const contentStructures = {
      "article": {
        structure: ["Introduction", "Main Content", "Key Points", "Conclusion"],
        format: "informative article format with clear sections"
      },
      "blog-post": {
        structure: ["Hook", "Introduction", "Main Points", "Call to Action"],
        format: "engaging blog post with personal touch"
      },
      "whitepaper": {
        structure: ["Executive Summary", "Problem Statement", "Solution", "Benefits", "Conclusion"],
        format: "authoritative whitepaper with data and insights"
      },
      "social-media": {
        structure: ["Hook", "Main Message", "Call to Action"],
        format: "concise social media post with engagement focus"
      },
      "email": {
        structure: ["Subject Line", "Opening", "Main Message", "Call to Action", "Closing"],
        format: "professional email communication"
      },
      "documentation": {
        structure: ["Overview", "Instructions", "Examples", "Troubleshooting"],
        format: "clear technical documentation"
      },
      "press-release": {
        structure: ["Headline", "Lead", "Body", "Boilerplate", "Contact Info"],
        format: "professional press release format"
      },
      "case-study": {
        structure: ["Challenge", "Solution", "Implementation", "Results"],
        format: "detailed case study with metrics"
      },
      "newsletter": {
        structure: ["Header", "Main Stories", "Updates", "Call to Action"],
        format: "engaging newsletter format"
      },
      "landing-page": {
        structure: ["Headline", "Value Proposition", "Benefits", "Social Proof", "CTA"],
        format: "conversion-focused landing page"
      }
    }

    const contentStructure = contentStructures[contentType]

    // Build the content generation prompt
    let generationPrompt = `Generate ${contentType} content about "${topic}" for ${targetAudience} audience.

REQUIREMENTS:
- Content Type: ${contentType}
- Target Audience: ${targetAudience}
- Tone: ${tone}
- Length: ${length} (approximately ${targetWords.target} words)
- Format: ${contentStructure.format}

STRUCTURE:
${contentStructure.structure.map((section, index) => `${index + 1}. ${section}`).join('\n')}

CONTENT GUIDELINES:
- Write in ${tone} tone appropriate for ${targetAudience} audience
- Target approximately ${targetWords.target} words
- Use clear, engaging language
- Include relevant examples and insights
- Ensure content is valuable and actionable`

    if (keywords && keywords.length > 0) {
      generationPrompt += `\n- Naturally incorporate these keywords: ${keywords.join(', ')}`
    }

    if (sections && sections.length > 0) {
      generationPrompt += `\n- Cover these specific topics: ${sections.join(', ')}`
    }

    if (!includeIntroduction) {
      generationPrompt += `\n- Skip the introduction and start with main content`
    }

    if (!includeConclusion) {
      generationPrompt += `\n- Skip the conclusion section`
    }

    generationPrompt += `\n\nGenerate the complete ${contentType} now:`

    // Return the structured content generation request
    return {
      success: true,
      contentType,
      topic,
      targetAudience,
      tone,
      length,
      targetWordCount: targetWords.target,
      structure: contentStructure.structure,
      generationPrompt,
      metadata: {
        keywords: keywords || [],
        sections: sections || [],
        includeIntroduction,
        includeConclusion,
        estimatedReadingTime: Math.ceil(targetWords.target / 200) // Average reading speed
      }
    }
  }
})
