// Auto-generated Mastra tool from firecrawl-search-tool.ts
// This file is automatically generated by src/scripts/sync-mastra.js
// Do not edit manually - changes will be overwritten

import { z } from 'zod'
import { createTool } from '@mastra/core'
import { firecrawlSearchSchema } from '../shared/schemas.js'
// Inlined utils - see generated code below
// Type imports removed for JavaScript

export const firecrawlSearchTool = createTool({
  id: 'firecrawl-search',
  description: 'Web search with automatic result scraping, geo-targeting, and full content extraction. Ideal for competitor discovery, trend analysis, content research, and market intelligence gathering.',
  inputSchema: firecrawlSearchSchema,
  execute: async (context) => {
    // Handle both direct calls and agent-wrapped calls
    // Agent calls: { context: { query, limit, ... } }
    // Direct calls: { query, limit, ... }
    const inputData = context.context || context

    // Enhanced parameter validation with helpful error messages
    if (!inputData || typeof inputData !== 'object') {
      return {
        success: false,
        error: 'Parameter validation failed: Expected object with parameters, received: ' + typeof inputData,
        help: 'Please check the system prompt for correct JSON parameter examples',
        source: 'firecrawl-search',
        confidence_score: 0.0,
        processing_time: '0s'
      }
    }

    // Validate extractOptions if provided
    if (inputData.extractOptions && typeof inputData.extractOptions === 'string') {
      return {
        success: false,
        error: 'Parameter validation failed: Invalid extractOptions: Expected JSON object like {"onlyMainContent": true}, received string: ' + inputData.extractOptions,
        help: 'Please check the system prompt for correct JSON parameter examples',
        source: 'firecrawl-search',
        confidence_score: 0.0,
        processing_time: '0s'
      }
    }

    // Validate searchOptions if provided
    if (inputData.searchOptions && typeof inputData.searchOptions === 'string') {
      return {
        success: false,
        error: 'Parameter validation failed: Invalid searchOptions: Expected JSON object like {"tbs": "qdr:d"}, received string: ' + inputData.searchOptions,
        help: 'Please check the system prompt for correct JSON parameter examples',
        source: 'firecrawl-search',
        confidence_score: 0.0,
        processing_time: '0s'
      }
    }

    const {
      query,
      limit,
      country,
      language,
      searchOptions,
      extractOptions
    } = inputData

    // Validate search query
    if (!query.trim()) {
      throw new Error('Search query cannot be empty')
    }

    if (query.length > 500) {
      throw new Error('Search query too long. Please limit to 500 characters.')
    }

    const searchLimit = Math.min(limit || 10, 100) // Enforce API limits

    // Check if Firecrawl API is configured
    if (!isFirecrawlConfigured()) {
      console.error('Firecrawl API credentials missing - returning mock data for testing')
      
      const mockResults = generateMockSearchResults(query, searchLimit)
      return {
        success: true,
        data: mockResults.map(result => ({
          ...result,
          metadata: {
            ...result.metadata,
            note: 'Mock data returned - Firecrawl API credentials not configured'
          }
        })),
        source: 'firecrawl-search',
        query,
        total_results: mockResults.length,
        unique_domains: [...new Set(mockResults.map(r => extractDomain(r.url)))].length,
        confidence_score: 0.5, // Lower confidence for mock data
        processing_time: '0.8s',
        search_parameters: {
          query,
          limit: searchLimit,
          country: country || 'global',
          language: language || 'en'
        },
        note: 'Mock data returned - Firecrawl API credentials not configured'
      }
    }

    const firecrawlContext = getFirecrawlContext()

    try {
      // Build request payload
      const requestPayload = {
        query,
        limit: searchLimit,
        ...(country && { country }),
        ...(language && { language }),
        ...(searchOptions && { searchOptions }),
        ...(extractOptions && { extractOptions })
      }

      console.log('Making Firecrawl search request for query:', query)
      console.log('Search parameters:', {
        limit: searchLimit,
        country: country || 'global',
        language: language || 'en',
        hasTimeFilter: !!searchOptions?.tbs,
        hasAdditionalFilters: !!searchOptions?.filter
      })

      const startTime = Date.now()
      const { controller, timeoutId } = createTimeoutController(firecrawlContext.timeout * 1.5) // Extended timeout for search

      const response = await fetch(`${firecrawlContext.baseUrl}/v1/search`, {
        method: 'POST',
        headers: createFirecrawlHeaders(firecrawlContext.apiKey),
        body: JSON.stringify(requestPayload),
        signal: controller.signal
      })

      clearTimeout(timeoutId)
      const processingTime = `${((Date.now() - startTime) / 1000).toFixed(1)}s`

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        const firecrawlError = parseFirecrawlError(errorData, response.status)
        const errorResponse = handleFirecrawlError(firecrawlError)
        console.error('Firecrawl search API error:', errorResponse)
        return errorResponse
      }

      const data = await response.json()

      if (!data.success || !data.data) {
        console.error('Firecrawl search data error:', data.error)
        return {
          success: false,
          error: `Firecrawl search failed: ${data.error || 'Unknown error'}`,
          source: 'firecrawl-search',
          confidence_score: 0.0,
          processing_time: processingTime,
          type: 'data_error',
          note: 'Firecrawl tool failed - research continued with other available tools'
        }
      }

      const results = data.data
      const domains = results.map(result => extractDomain(result.url))
      const uniqueDomains = [...new Set(domains)]
      
      // Calculate overall confidence score based on content quality
      const resultsWithContent = results.filter(result => 
        result.markdown || result.html
      )
      
      const overallConfidence = resultsWithContent.length > 0 
        ? resultsWithContent.reduce((sum, result) => {
            const confidence = calculateFirecrawlConfidence(
              !!(result.markdown || result.html),
              !!result.metadata,
              true, // Search results always have some extracted data
              result.metadata.statusCode
            )
            return sum + confidence
          }, 0) / resultsWithContent.length
        : 0.4

      // Enhanced results with additional metadata
      const enhancedResults = results.map((result, index) => ({
        ...result,
        metadata: {
          ...result.metadata,
          domain: extractDomain(result.url),
          rank: index + 1,
          confidence_score: calculateFirecrawlConfidence(
            !!(result.markdown || result.html),
            !!result.metadata,
            true,
            result.metadata.statusCode
          ),
          content_length: result.markdown ? result.markdown.length : 0,
          has_content: !!(result.markdown || result.html),
          search_relevance: Math.max(0.1, 1 - (index * 0.05)) // Decreasing relevance by rank
        }
      }))

      const enhancedResponse = {
        success: true,
        data: enhancedResults,
        source: 'firecrawl-search',
        query,
        total_results: results.length,
        results_with_content: resultsWithContent.length,
        unique_domains: uniqueDomains.length,
        domains: uniqueDomains,
        confidence_score: overallConfidence,
        processing_time: processingTime,
        search_parameters: {
          query,
          limit: searchLimit,
          country: country || 'global',
          language: language || 'en',
          time_filter: searchOptions?.tbs,
          additional_filters: searchOptions?.filter
        },
        statistics: {
          content_success_rate: (resultsWithContent.length / results.length) * 100,
          avg_content_length: resultsWithContent.length > 0 
            ? resultsWithContent.reduce((sum, result) => 
                sum + (result.markdown?.length || 0), 0
              ) / resultsWithContent.length 
            : 0,
          domain_diversity: (uniqueDomains.length / results.length) * 100,
          avg_relevance_score: enhancedResults.reduce((sum, result) => 
            sum + (result.metadata.search_relevance || 0), 0
          ) / enhancedResults.length
        }
      }

      console.log('Firecrawl search completed successfully:', {
        query,
        totalResults: results.length,
        resultsWithContent: resultsWithContent.length,
        uniqueDomains: uniqueDomains.length,
        overallConfidence,
        processingTime
      })

      return enhancedResponse

    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        const timeoutError = parseFirecrawlError(error)
        const errorResponse = handleFirecrawlError(timeoutError)
        console.error('Firecrawl search timeout error:', errorResponse)
        return errorResponse
      }

      console.error('Firecrawl search error:', error)
      
      // For network or API errors, provide fallback response
      if (error instanceof Error && (
        error.message.includes('fetch') || 
        error.message.includes('network') ||
        error.message.includes('timeout')
      )) {
        console.log('Network error detected, providing fallback response')
        
        const mockResults = generateMockSearchResults(query, searchLimit)
        return {
          success: false,
          error: error.message,
          fallback_data: mockResults.map(result => ({
            ...result,
            metadata: {
              ...result.metadata,
              note: 'Fallback data due to network error'
            }
          })),
          source: 'firecrawl-search',
          query,
          total_results: mockResults.length,
          unique_domains: [...new Set(mockResults.map(r => extractDomain(r.url)))].length,
          confidence_score: 0.3,
          processing_time: '0s',
          search_parameters: {
            query,
            limit: searchLimit,
            country: country || 'global',
            language: language || 'en'
          }
        }
      }

      // Return error response instead of throwing for agent resilience
      return {
        success: false,
        error: `Firecrawl search error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        source: 'firecrawl-search',
        confidence_score: 0.0,
        processing_time: '0s',
        type: 'unexpected_error',
        note: 'Firecrawl tool failed - research continued with other available tools'
      }
    }
  }
})
