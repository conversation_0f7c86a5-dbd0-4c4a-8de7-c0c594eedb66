// Auto-generated Mastra tool from text-summarization-tool.ts
// This file is automatically generated by src/scripts/sync-mastra.js
// Do not edit manually - changes will be overwritten

import { z } from "zod"
import { createTool } from "@mastra/core"

const textSummarizationSchema = z.object({
  inputText: z
    .string()
    .min(100)
    .describe("The text content to be summarized (minimum 100 characters)"),
  summaryLength: z
    .enum([
      "brief", // 1-2 sentences
      "short", // 3-5 sentences  
      "medium", // 1-2 paragraphs
      "detailed" // 2-3 paragraphs
    ])
    .default("short")
    .describe("Desired length of the summary"),
  summaryFormat: z
    .enum([
      "paragraph",
      "bullet-points",
      "executive-summary",
      "key-takeaways",
      "abstract",
      "highlights"
    ])
    .default("paragraph")
    .describe("Format style for the summary output"),
  focusArea: z
    .enum([
      "main-points",
      "key-findings", 
      "actionable-items",
      "conclusions",
      "methodology",
      "results",
      "recommendations"
    ])
    .default("main-points")
    .describe("What aspect of the content to focus on in the summary"),
  targetAudience: z
    .enum([
      "general",
      "technical",
      "executive", 
      "academic",
      "marketing"
    ])
    .default("general")
    .describe("Target audience for the summary"),
  preserveKeyTerms: z
    .boolean()
    .default(true)
    .describe("Whether to preserve important technical terms and keywords"),
  includeStatistics: z
    .boolean()
    .default(true)
    .describe("Whether to include key statistics and numbers in the summary")
})

export const textSummarizationTool = createTool({
  id: "text-summarization",
  description: "Create concise, focused summaries of text content in various formats and lengths",
  inputSchema: textSummarizationSchema,
  execute: async (context) => {
    const {
      inputText,
      summaryLength,
      summaryFormat,
      focusArea,
      targetAudience,
      preserveKeyTerms,
      includeStatistics
    } = context.context

    // Calculate input text statistics
    const wordCount = inputText.split(/\s+/).length
    const characterCount = inputText.length
    const estimatedReadingTime = Math.ceil(wordCount / 200)

    // Define summary length targets
    const lengthTargets = {
      brief: { sentences: "1-2", words: "20-50", description: "Very concise overview" },
      short: { sentences: "3-5", words: "50-100", description: "Brief summary" },
      medium: { sentences: "6-10", words: "100-200", description: "Moderate detail" },
      detailed: { sentences: "10-15", words: "200-300", description: "Comprehensive summary" }
    }

    const targetLength = lengthTargets[summaryLength]

    // Define format-specific instructions
    const formatInstructions = {
      paragraph: "Write paragraphs with smooth transitions",
      "bullet-points": "Present, concise bullet points with parallel structure",
      "executive-summary": "Write formal executive summary with key insights upfront",
      "key-takeaways": "List the most important takeaways in order of importance",
      abstract: "Write academic-style abstract with methodology and conclusions",
      highlights: "Present key points with brief explanations"
    }

    // Define focus area instructions
    const focusInstructions = {
      "main-points": "Focus on the primary arguments and central themes",
      "key-findings": "Emphasize discoveries, results, and important findings",
      "actionable-items": "Highlight specific actions, recommendations, and next steps",
      conclusions: "Focus on final outcomes, decisions, and conclusions reached",
      methodology: "Emphasize the approach, process, and methods used",
      results: "Concentrate on outcomes, data, and measurable results",
      recommendations: "Focus on suggested actions and strategic recommendations"
    }

    // Build the summarization prompt
    let summarizationPrompt = `Summarize the following text content:

ORIGINAL TEXT:
${inputText}

SUMMARIZATION REQUIREMENTS:
- Length: ${summaryLength} (${targetLength.sentences} sentences, approximately ${targetLength.words} words)
- Format: ${summaryFormat} - ${formatInstructions[summaryFormat]}
- Focus: ${focusArea} - ${focusInstructions[focusArea]}
- Audience: ${targetAudience}
- Preserve key terms: ${preserveKeyTerms ? 'Yes' : 'No'}
- Include statistics: ${includeStatistics ? 'Yes' : 'No'}

INSTRUCTIONS:
1. ${formatInstructions[summaryFormat]}
2. ${focusInstructions[focusArea]}
3. Write for ${targetAudience} audience level
4. Keep within ${targetLength.words} words`

    if (preserveKeyTerms) {
      summarizationPrompt += `\n5. Preserve important technical terms and domain-specific vocabulary`
    }

    if (includeStatistics) {
      summarizationPrompt += `\n6. Include key numbers, percentages, and statistical data`
    }

    // Add format-specific closing instructions
    if (summaryFormat === "bullet-points") {
      summarizationPrompt += `\n\nFormat points with each point being a complete thought.`
    } else if (summaryFormat === "executive-summary") {
      summarizationPrompt += `\n\nStructure as: Key insight, supporting details, implications.`
    } else if (summaryFormat === "key-takeaways") {
      summarizationPrompt += `\n\nList takeaways in order of importance with brief explanations.`
    }

    summarizationPrompt += `\n\nGenerate the ${summaryLength} ${summaryFormat} summary now:`

    // Analyze content complexity
    const complexityIndicators = {
      technicalTerms: (inputText.match(/\b[A-Z]{2,}\b/g) || []).length,
      longSentences: inputText.split('.').filter(s => s.split(' ').length > 20).length,
      avgWordsPerSentence: wordCount / (inputText.split('.').length || 1)
    }

    const complexityScore = Math.min(10, Math.round(
      (complexityIndicators.technicalTerms * 0.3) +
      (complexityIndicators.longSentences * 0.4) +
      (complexityIndicators.avgWordsPerSentence * 0.3)
    ))

    return {
      success: true,
      inputAnalysis: {
        wordCount,
        characterCount,
        estimatedReadingTime,
        complexityScore,
        complexityLevel: complexityScore < 3 ? 'Simple' : complexityScore < 7 ? 'Moderate' : 'Complex'
      },
      summaryConfig: {
        length: summaryLength,
        format: summaryFormat,
        focusArea,
        targetAudience,
        targetWordCount: targetLength.words,
        targetSentences: targetLength.sentences
      },
      summarizationPrompt,
      metadata: {
        compressionRatio: `${Math.round((parseInt(targetLength.words.split('-')[1]) / wordCount) * 100)}%`,
        preserveKeyTerms,
        includeStatistics,
        formatInstructions: formatInstructions[summaryFormat],
        focusInstructions: focusInstructions[focusArea]
      }
    }
  }
})
