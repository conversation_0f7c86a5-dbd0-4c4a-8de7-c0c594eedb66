// Auto-generated Mastra tool from firecrawl-extract-tool.ts
// This file is automatically generated by src/scripts/sync-mastra.js
// Do not edit manually - changes will be overwritten

import { z } from 'zod'
import { createTool } from '@mastra/core'
import { firecrawlExtractSchema } from '../shared/schemas.js'
// Inlined utils - see generated code below
// Type imports removed for JavaScript

export const firecrawlExtractTool = createTool({
  id: 'firecrawl-extract',
  description: 'Bulk data extraction from multiple URLs with schema-driven structured data extraction and natural language processing. Perfect for directory scraping, competitor product catalogs, and market research across multiple sources.',
  inputSchema: firecrawlExtractSchema,
  execute: async (context) => {
    // Handle both direct calls and agent-wrapped calls
    // Agent calls: { context: { urls, schema, ... } }
    // Direct calls: { urls, schema, ... }
    const inputData = context.context || context

    // Enhanced parameter validation with helpful error messages
    if (!inputData || typeof inputData !== 'object') {
      return {
        success: false,
        error: 'Parameter validation failed: Expected object with parameters, received: ' + typeof inputData,
        help: 'Please check the system prompt for correct JSON parameter examples',
        source: 'firecrawl-extract',
        confidence_score: 0.0,
        processing_time: '0s'
      }
    }

    // Validate extractOptions if provided
    if (inputData.extractOptions && typeof inputData.extractOptions === 'string') {
      return {
        success: false,
        error: 'Parameter validation failed: Invalid extractOptions: Expected JSON object like {"onlyMainContent": true}, received string: ' + inputData.extractOptions,
        help: 'Please check the system prompt for correct JSON parameter examples',
        source: 'firecrawl-extract',
        confidence_score: 0.0,
        processing_time: '0s'
      }
    }

    // Validate schema parameter
    if (inputData.schema && typeof inputData.schema === 'string') {
      return {
        success: false,
        error: 'Parameter validation failed: Invalid schema: Expected JSON object defining data structure, received string: ' + inputData.schema,
        help: 'Please check the system prompt for correct JSON parameter examples',
        source: 'firecrawl-extract',
        confidence_score: 0.0,
        processing_time: '0s'
      }
    }

    const {
      urls,
      schema,
      prompt,
      allowExternalLinks,
      limit,
      extractOptions
    } = inputData

    // Validate all URLs
    const invalidUrls = urls.filter(url => !validateFirecrawlUrl(url))
    if (invalidUrls.length > 0) {
      throw new Error(`Invalid URLs detected: ${invalidUrls.join(', ')}. Please provide valid HTTP or HTTPS URLs.`)
    }

    const normalizedUrls = urls.map(normalizeUrl)
    const domains = normalizedUrls.map(extractDomain)
    const uniqueDomains = [...new Set(domains)]

    // Check if Firecrawl API is configured
    if (!isFirecrawlConfigured()) {
      console.error('Firecrawl API credentials missing - returning mock data for testing')
      
      const mockResults = generateMockExtractResults(normalizedUrls)
      return {
        success: true,
        data: mockResults.map(result => ({
          ...result,
          metadata: {
            ...result.metadata,
            note: 'Mock data returned - Firecrawl API credentials not configured'
          }
        })),
        source: 'firecrawl-extract',
        total_urls: normalizedUrls.length,
        processed_urls: mockResults.length,
        unique_domains: uniqueDomains.length,
        domains: uniqueDomains,
        confidence_score: 0.5, // Lower confidence for mock data
        processing_time: '1.2s',
        schema_used: schema,
        extraction_prompt: prompt,
        note: 'Mock data returned - Firecrawl API credentials not configured'
      }
    }

    const firecrawlContext = getFirecrawlContext()
    
    try {
      // Build request payload
      const requestPayload = {
        urls: normalizedUrls,
        schema,
        prompt,
        allowExternalLinks: allowExternalLinks ?? false,
        limit: limit ?? 10,
        ...(extractOptions && { extractOptions })
      }

      console.log('Making Firecrawl extract request for', normalizedUrls.length, 'URLs')
      console.log('Unique domains:', uniqueDomains)
      console.log('Schema keys:', Object.keys(schema))
      console.log('Extraction prompt:', prompt)

      const startTime = Date.now()
      const { controller, timeoutId } = createTimeoutController(firecrawlContext.timeout * 2) // Double timeout for bulk operations

      const response = await fetch(`${firecrawlContext.baseUrl}/v1/extract`, {
        method: 'POST',
        headers: createFirecrawlHeaders(firecrawlContext.apiKey),
        body: JSON.stringify(requestPayload),
        signal: controller.signal
      })

      clearTimeout(timeoutId)
      const processingTime = `${((Date.now() - startTime) / 1000).toFixed(1)}s`

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        const firecrawlError = parseFirecrawlError(errorData, response.status)
        const errorResponse = handleFirecrawlError(firecrawlError)
        console.error('Firecrawl extract API error:', errorResponse)
        return errorResponse
      }

      const data = await response.json()

      if (!data.success || !data.data) {
        console.error('Firecrawl extract data error:', data.error)
        return {
          success: false,
          error: `Firecrawl extract failed: ${data.error || 'Unknown error'}`,
          source: 'firecrawl-extract',
          confidence_score: 0.0,
          processing_time: processingTime,
          type: 'data_error',
          note: 'Firecrawl tool failed - research continued with other available tools'
        }
      }

      const results = data.data
      
      // Calculate overall confidence score based on successful extractions
      const successfulExtractions = results.filter(result => 
        result.extractedData && Object.keys(result.extractedData).length > 0
      )
      
      const overallConfidence = successfulExtractions.length > 0 
        ? successfulExtractions.reduce((sum, result) => {
            const confidence = calculateFirecrawlConfidence(
              true, // Has content (extracted data)
              !!result.metadata,
              !!result.extractedData,
              result.metadata.statusCode
            )
            return sum + confidence
          }, 0) / successfulExtractions.length
        : 0.3

      // Enhanced results with additional metadata
      const enhancedResults = results.map(result => ({
        ...result,
        metadata: {
          ...result.metadata,
          domain: extractDomain(result.url),
          confidence_score: calculateFirecrawlConfidence(
            true,
            !!result.metadata,
            !!result.extractedData,
            result.metadata.statusCode
          ),
          extracted_fields: result.extractedData ? Object.keys(result.extractedData) : [],
          schema_compliance: result.extractedData 
            ? Object.keys(schema).filter(key => result.extractedData[key] !== undefined).length / Object.keys(schema).length
            : 0
        }
      }))

      const enhancedResponse = {
        success: true,
        data: enhancedResults,
        source: 'firecrawl-extract',
        total_urls: normalizedUrls.length,
        processed_urls: results.length,
        successful_extractions: successfulExtractions.length,
        unique_domains: uniqueDomains.length,
        domains: uniqueDomains,
        confidence_score: overallConfidence,
        processing_time: processingTime,
        schema_used: schema,
        extraction_prompt: prompt,
        statistics: {
          success_rate: (successfulExtractions.length / results.length) * 100,
          avg_fields_extracted: successfulExtractions.length > 0 
            ? successfulExtractions.reduce((sum, result) => 
                sum + Object.keys(result.extractedData).length, 0
              ) / successfulExtractions.length 
            : 0,
          schema_compliance_avg: enhancedResults.reduce((sum, result) => 
            sum + (result.metadata.schema_compliance || 0), 0
          ) / enhancedResults.length
        }
      }

      console.log('Firecrawl extract completed successfully:', {
        totalUrls: normalizedUrls.length,
        processedUrls: results.length,
        successfulExtractions: successfulExtractions.length,
        uniqueDomains: uniqueDomains.length,
        overallConfidence,
        processingTime
      })

      return enhancedResponse

    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        const timeoutError = parseFirecrawlError(error)
        const errorResponse = handleFirecrawlError(timeoutError)
        console.error('Firecrawl extract timeout error:', errorResponse)
        return errorResponse
      }

      console.error('Firecrawl extract error:', error)
      
      // For network or API errors, provide fallback response
      if (error instanceof Error && (
        error.message.includes('fetch') || 
        error.message.includes('network') ||
        error.message.includes('timeout')
      )) {
        console.log('Network error detected, providing fallback response')
        
        const mockResults = generateMockExtractResults(normalizedUrls)
        return {
          success: false,
          error: error.message,
          fallback_data: mockResults.map(result => ({
            ...result,
            metadata: {
              ...result.metadata,
              note: 'Fallback data due to network error'
            }
          })),
          source: 'firecrawl-extract',
          total_urls: normalizedUrls.length,
          processed_urls: mockResults.length,
          unique_domains: uniqueDomains.length,
          domains: uniqueDomains,
          confidence_score: 0.3,
          processing_time: '0s',
          schema_used: schema,
          extraction_prompt: prompt
        }
      }

      // Return error response instead of throwing for agent resilience
      return {
        success: false,
        error: `Firecrawl extract error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        source: 'firecrawl-extract',
        confidence_score: 0.0,
        processing_time: '0s',
        type: 'unexpected_error',
        note: 'Firecrawl tool failed - research continued with other available tools'
      }
    }
  }
})
