// Pure Zod schemas for Mastra Studio compatibility
// This file contains all schemas without SvelteKit dependencies

import { z } from 'zod'

// ===== FIRECRAWL SCHEMAS =====
export const firecrawlLocationSchema = z.object({
  country: z.string().optional().describe('Country code for geo-targeting (e.g., "US", "UK")'),
  language: z.string().optional().describe('Language code (e.g., "en", "es", "fr")')
})

export const firecrawlActionSchema = z.object({
  type: z.enum(['click', 'wait', 'scroll', 'key', 'screenshot']).describe('Type of action to perform'),
  selector: z.string().optional().describe('CSS selector for the element to interact with'),
  text: z.string().optional().describe('Text to type (for key actions)'),
  milliseconds: z.number().optional().describe('Time to wait in milliseconds (for wait actions)')
})

export const firecrawlExtractOptionsSchema = z.object({
  onlyMainContent: z.boolean().optional().describe('Extract only main content, excluding headers, footers, navigation'),
  includeTags: z.array(z.string()).optional().describe('HTML tags to include in extraction'),
  excludeTags: z.array(z.string()).optional().describe('HTML tags to exclude from extraction'),
  onlyIncludeTags: z.array(z.string()).optional().describe('Only include these HTML tags')
})

export const firecrawlScrapeSchema = z.object({
  url: z.string().url().describe('The URL to scrape and analyze'),
  formats: z.array(z.enum(['markdown', 'html', 'rawHtml', 'content', 'links', 'screenshot']))
    .optional()
    .default(['markdown', 'html'])
    .describe('Output formats to return'),
  extractOptions: firecrawlExtractOptionsSchema.optional().describe('Content extraction options'),
  actions: z.array(firecrawlActionSchema).optional().describe('Actions to perform on the page (clicking, scrolling, etc.)'),
  location: firecrawlLocationSchema.optional().describe('Geographic location for the request'),
  jsonOptions: z.object({
    schema: z.record(z.any()).optional().describe('JSON schema for structured data extraction'),
    systemPrompt: z.string().optional().describe('System prompt for AI-powered extraction'),
    userPrompt: z.string().optional().describe('User prompt describing what data to extract')
  }).optional().describe('AI-powered structured data extraction options'),
  timeout: z.number().optional().default(30000).describe('Request timeout in milliseconds')
})

export const firecrawlExtractSchema = z.object({
  urls: z.array(z.string().url()).min(1).max(100).describe('Array of URLs to extract data from (max 100)'),
  schema: z.record(z.any()).describe('JSON schema defining the structure of data to extract'),
  prompt: z.string().describe('Natural language prompt describing what data to extract'),
  allowExternalLinks: z.boolean().optional().default(false).describe('Allow following external links during extraction'),
  limit: z.number().optional().default(10).describe('Maximum number of URLs to process'),
  extractOptions: firecrawlExtractOptionsSchema.optional().describe('Content extraction options')
})

export const firecrawlSearchSchema = z.object({
  query: z.string().min(1).max(500).describe('Search query to find relevant web pages'),
  limit: z.number().max(100).optional().default(10).describe('Maximum number of search results to return'),
  country: z.string().optional().describe('Country code for geo-targeted search (e.g., "US", "UK", "CA")'),
  language: z.string().optional().describe('Language code for search results (e.g., "en", "es", "fr")'),
  searchOptions: z.object({
    tbs: z.string().optional().describe('Time-based search filter (e.g., "qdr:d" for past day, "qdr:w" for past week)'),
    filter: z.string().optional().describe('Additional search filters and operators'),
    safe: z.enum(['active', 'off']).optional().describe('Safe search setting')
  }).optional().describe('Advanced search options and filters'),
  extractOptions: firecrawlExtractOptionsSchema.optional().describe('Content extraction options for search results')
})

// ===== COMMON TOOL SCHEMAS =====
export const webSearchSchema = z.object({
  query: z.string().min(1).describe('Search query to find relevant information'),
  includeDetails: z.boolean().default(false).describe('Include additional details in results'),
  numResults: z.number().default(10).describe('Number of search results to return')
})

export const keywordVolumeSchema = z.object({
  keywords: z.array(z.string()).min(1).max(100).describe('Keywords to get volume data for'),
  location: z.string().optional().default('United States').describe('Geographic location for search volume data'),
  language: z.string().optional().default('en').describe('Language code for keyword analysis')
})

export const keywordDifficultySchema = z.object({
  keywords: z.array(z.string()).min(1).max(100).describe('Keywords to analyze difficulty for'),
  location: z.string().optional().default('United States').describe('Geographic location for difficulty analysis')
})

// Apollo schemas temporarily disabled due to Zod .max() compatibility issue

// ===== TYPE INFERENCE =====
export type FirecrawlScrapeInput = z.infer<typeof firecrawlScrapeSchema>
export type FirecrawlExtractInput = z.infer<typeof firecrawlExtractSchema>
export type FirecrawlSearchInput = z.infer<typeof firecrawlSearchSchema>
export type WebSearchInput = z.infer<typeof webSearchSchema>
export type KeywordVolumeInput = z.infer<typeof keywordVolumeSchema>
export type KeywordDifficultyInput = z.infer<typeof keywordDifficultySchema>
export type ApolloSearchCompanyInput = z.infer<typeof apolloSearchCompanySchema>
export type ApolloFindCompaniesInput = z.infer<typeof apolloFindCompaniesSchema>
export type ApolloFindContactsInput = z.infer<typeof apolloFindContactsSchema>
