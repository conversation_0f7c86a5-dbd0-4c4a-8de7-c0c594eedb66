// Shared types for Mastra Studio compatibility
// Common types used across agents and tools without SvelteKit dependencies

export interface ToolExecutionResult {
  success: boolean
  data?: any
  error?: string
  metadata?: Record<string, any>
}

export interface AgentMetadata {
  id: string
  name: string
  description: string
  version: string
  tools: string[]
  lastUpdated: string
}

export interface ToolMetadata {
  id: string
  name: string
  description: string
  category: 'content' | 'research' | 'seo' | 'apollo' | 'firecrawl'
  icon: string
  status: 'active' | 'beta' | 'deprecated'
  version: string
  usedBy: string[]
  inputSchema: any
  outputSchema?: any
}

export interface LLMConfig {
  provider: 'openai' | 'anthropic' | 'google' | string
  model: string
  temperature?: number
  maxTokens?: number
  apiKey?: string
}

export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: {
    message: string
    code?: string
    details?: any
  }
  timestamp: string
}

// Tool execution context for Mastra
export interface MastraToolContext {
  toolId: string
  agentId?: string
  sessionId?: string
  userId?: string
  environment: 'development' | 'production' | 'studio'
  metadata?: Record<string, any>
}

// Registry interfaces
export interface RegistryAgent {
  id: string
  name: string
  description: string
  export: string
  filePath: string
  tools: string[]
  metadata: AgentMetadata
}

export interface RegistryTool {
  id: string
  name: string
  description: string
  category: ToolMetadata['category']
  export: string
  filePath: string
  metadata: ToolMetadata
}

export interface MastraRegistry {
  agents: RegistryAgent[]
  tools: RegistryTool[]
  lastUpdated: string
  version: string
}

// Environment configuration
export interface EnvironmentConfig {
  apiKeys: Record<string, string>
  serviceUrls: Record<string, string>
  defaultSettings: {
    llmProvider: string
    llmModel: string
    timeout: number
  }
}
