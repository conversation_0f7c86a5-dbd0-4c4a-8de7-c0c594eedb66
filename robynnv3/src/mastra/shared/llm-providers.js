// Auto-generated Mastra tool from llm-providers.ts
// This file is automatically generated by src/scripts/sync-mastra.js
// Do not edit manually - changes will be overwritten

import { createOpenAI } from '@ai-sdk/openai'
import { createAnthropic } from '@ai-sdk/anthropic'
import { createGoogleGenerativeAI } from '@ai-sdk/google'
import { createOpenAICompatible } from '@ai-sdk/openai-compatible'
import { ENV_VARS } from '../shared/env.js'
export function createLLMClient(config) {
  const { provider, model } = config
  
  console.log('=== LLM CLIENT CONFIGURATION ===');
  console.log('Provider:', provider);
  console.log('Model:', model);
  console.log('Config source:', config);
  
  switch (provider) {
    case 'openai':
      if (!ENV_VARS.OPENAI_API_KEY()) {
        throw new Error('OPENAI_API_KEY is required for OpenAI provider')
      }
      const openaiClient = createOpenAI({
        apiKey: ENV_VARS.OPENAI_API_KEY()
      })
      return openaiClient(model)
    
    case 'anthropic':
      if (!ENV_VARS.ANTHROPIC_API_KEY()) {
        throw new Error('ANTHROPIC_API_KEY is required for Anthropic provider')
      }
      const anthropicClient = createAnthropic({
        apiKey: ENV_VARS.ANTHROPIC_API_KEY()
      })
      return anthropicClient(model)
    
    case 'google':
      if (!ENV_VARS.GOOGLE_GENERATIVE_AI_API_KEY()) {
        throw new Error('GOOGLE_GENERATIVE_AI_API_KEY is required for Google provider')
      }
      const googleClient = createGoogleGenerativeAI({
        apiKey: ENV_VARS.GOOGLE_GENERATIVE_AI_API_KEY()
      })
      return googleClient(model)
    
    case 'deepseek':
      if (!ENV_VARS.DEEPSEEK_API_KEY()) {
        throw new Error('DEEPSEEK_API_KEY is required for DeepSeek provider')
      }
      const deepseek = createOpenAICompatible({
        name: 'deepseek',
        apiKey: ENV_VARS.DEEPSEEK_API_KEY(),
        baseURL: 'https://api.deepseek.com/v1'
      })
      return deepseek(model)
    
    default:
      throw new Error(`Unsupported LLM provider: ${provider}`)
  }
}

// Default configurations for each provider
export const defaultConfigs = {
  openai: { provider: 'openai', model: 'gpt-4o-mini' },
  anthropic: { provider: 'anthropic', model: 'claude-3-5-sonnet-20241022' },
  google: { provider: 'google', model: 'gemini-1.5-flash' },
  deepseek: { provider: 'deepseek', model: 'deepseek-chat' }
}

// Helper function to get default config for a provider
export function getDefaultConfig(provider) {
  return defaultConfigs[provider]
}

// Helper function to create LLM client with environment-based selection
export function createDefaultLLMClient() {
  const provider = (ENV_VARS.LLM_PROVIDER()) || 'openai'
  const model = ENV_VARS.LLM_MODEL() || defaultConfigs[provider].model
  
  return createLLMClient({ provider, model })
}
