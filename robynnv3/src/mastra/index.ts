// Main Mastra Studio entry point
// This creates a Mastra instance with working tools for Studio discovery

import { <PERSON><PERSON> } from '@mastra/core'
import {
  apolloFindCompaniesTool,
  apolloFindContactsTool,
  apolloSearchCompanyTool,
  braveSearchTool,
  citationTool,
  contentGenerationTool,
  grammarStyleTool,
  outlineGenerationTool,
  textSummarizationTool,
  webSearchTool
} from './tools/index.js'

import {
  companyResearcherAgent,
  contentAgent,
  orchestratorAgent,
  seoStrategistAgent
} from './agents/index.js'

// Create the main Mastra instance for Studio
const mastra = new Mastra({
  name: 'ROBYNNV3',
  tools: [
    apolloFindCompaniesTool,
    apolloFindContactsTool,
    apolloSearchCompanyTool,
    braveSearchTool,
    citationTool,
    contentGenerationTool,
    grammarStyleTool,
    outlineGenerationTool,
    textSummarizationTool,
    webSearchTool
  ],
  agents: [
    companyResearcherAgent,
    contentAgent,
    orchestratorAgent,
    seoStrategistAgent
  ]
})

export { mastra }
