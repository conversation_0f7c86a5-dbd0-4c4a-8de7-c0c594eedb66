import { getContentService } from "$lib/services/content"

export const load = async ({ parent }) => {
  // Get parent data (auth and environment from root layout)
  const parentData = await parent()

  const contentService = getContentService()

  try {
    const homeContent = await contentService.getAllHomeContent()

    return {
      ...parentData,
      homeContent,
      // Add metadata for SEO
      meta: {
        title:
          homeContent.hero?.frontmatter.title ||
          "Robynn AI - Marketing Agents for Startups",
        description:
          homeContent.hero?.content ||
          "Strategic marketing leadership that scales with your ambitions.",
        lastModified: new Date().toISOString(),
      },
    }
  } catch (error) {
    console.error("Error loading home page content:", error)

    // Return fallback content structure
    return {
      ...parentData,
      homeContent: {
        hero: null,
        sections: {
          problem: null,
          approach: null,
          services: null,
          marketingAgents: null,
          results: null,
          stories: null,
          team: null,
          cta: null,
        },
        teamMembers: [],
        caseStudies: [],
        marketingAgents: [],
        features: {
          problems: null,
          approach: null,
          services: null,
          metrics: null,
        },
      },
      meta: {
        title: "Robynn AI - Marketing Agents for Startups",
        description:
          "Strategic marketing leadership that scales with your ambitions.",
        lastModified: new Date().toISOString(),
      },
    }
  }
}
