<script lang="ts">
  import { onMount } from "svelte"
  import { page } from "$app/stores"
  import { goto } from "$app/navigation"

  export let data

  onMount(() => {
    // Redirect to the new content-board route
    const currentPath = $page.url.pathname
    const newPath = currentPath.replace('/content-agent', '/content-board')
    goto(newPath, { replaceState: true })
  })
</script>

<div class="flex items-center justify-center h-screen">
  <div class="text-center">
    <h2 class="text-lg font-semibold mb-2">Redirecting to Content Board...</h2>
    <p class="text-muted-foreground">The Content Agent has been upgraded to Content Board.</p>
  </div>
</div>