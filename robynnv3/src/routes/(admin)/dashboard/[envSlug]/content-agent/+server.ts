import { json } from "@sveltejs/kit"
import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "./$types"
import { contentAgent } from "$lib/agents"
import { ContentService } from "$lib/services/content-service"

// Keep the original POST handler for non-streaming requests
export const POST: RequestHandler = async ({
  request,
  locals,
  url,
  params,
}) => {
  // Verify user is authenticated
  const { session, user } = locals.auth
  if (!session || !user) {
    return json({ error: "Unauthorized" }, { status: 401 })
  }

  // Get the supabase client from locals
  const supabase = locals.supabase

  // Check if client wants streaming response
  const wantsStream = url.searchParams.get("stream") === "true"

  if (wantsStream) {
    return handleStreamingRequest(request, user, supabase, params.envSlug)
  }

  try {
    const { message, documentId, action, currentContent, selectedText, contextType } = await request.json()

    if (!message || typeof message !== "string") {
      return json({ error: "Message is required" }, { status: 400 })
    }

    console.log(
      "Content agent request received:",
      message.substring(0, 100) + "...",
      `Context type: ${contextType || 'none'}`
    )

    // Prepare context-aware message
    let contextualMessage = message
    if (currentContent && currentContent.trim()) {
      const contextLabel = contextType === 'selected' ? 'Selected text' : 'Current document content'
      contextualMessage = `${message}\n\n${contextLabel}:\n${currentContent}`
    }

    // Generate response using the content agent with timeout
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(
        () =>
          reject(
            new Error("Agent timeout after 120 seconds - try a simpler query"),
          ),
        120000,
      )
    })

    const agentPromise = contentAgent.generate([
      {
        role: "user",
        content: contextualMessage,
      },
    ])

    const response: any = await Promise.race([agentPromise, timeoutPromise])

    console.log(
      "Content agent response generated, length:",
      response.text?.length || 0,
    )

    // If documentId is provided, save the interaction to session
    if (documentId) {
      const contentService = new ContentService(supabase)
      try {
        // Get environment ID from the environment slug
        const { data: environment } = await supabase
          .from("environments")
          .select("id")
          .eq("slug", params.envSlug)
          .single()

        if (environment) {
          await contentService.createSession({
            document_id: documentId,
            user_id: user.id,
            environment_id: environment.id,
            session_data: {
              message,
              response: response.text,
              action,
              timestamp: new Date().toISOString(),
            },
          })
        }
      } catch (error) {
        console.warn("Failed to save session:", error)
      }
    }

    return json({
      response: response.text,
      documentId,
      action,
    })
  } catch (error) {
    console.error("Content agent error:", error)
    return json(
      {
        error:
          error instanceof Error ? error.message : "Unknown error occurred",
      },
      { status: 500 },
    )
  }
}

// Streaming handler for progress updates
async function handleStreamingRequest(
  request: Request,
  user: any,
  supabase: any,
  envSlug: string,
) {
  const { message, documentId, action } = await request.json()

  if (!message || typeof message !== "string") {
    return json({ error: "Message is required" }, { status: 400 })
  }

  console.log(
    "Content agent streaming request received:",
    message.substring(0, 100) + "...",
  )

  const stream = new ReadableStream({
    start(controller) {
      const encoder = new TextEncoder()

      function send(data: Record<string, unknown>) {
        const message = `data: ${JSON.stringify(data)}\n\n`
        controller.enqueue(encoder.encode(message))
      }

      // Start processing
      ;(async () => {
        try {
          send({
            step: 1,
            action: "Initializing Content Agent...",
            progress: 10,
            status: "active",
          })

          // Determine the type of request and set up appropriate steps
          const isOutlineGeneration =
            message.toLowerCase().includes("outline") ||
            action === "generate-outline"
          const isContentGeneration =
            message.toLowerCase().includes("generate") ||
            action === "generate-content"
          const isSummarization =
            message.toLowerCase().includes("summarize") ||
            action === "summarize"
          const isGrammarCheck =
            message.toLowerCase().includes("grammar") ||
            message.toLowerCase().includes("correct") ||
            action === "grammar-check"

          let steps = [
            {
              id: 1,
              title: "Initializing",
              description: "Setting up Content Agent",
            },
            {
              id: 2,
              title: "Processing",
              description: "Analyzing your request",
            },
            {
              id: 3,
              title: "Generating",
              description: "Creating content response",
            },
            {
              id: 4,
              title: "Finalizing",
              description: "Preparing final output",
            },
          ]

          if (isOutlineGeneration) {
            steps = [
              {
                id: 1,
                title: "Analyzing Topic",
                description: "Understanding your content requirements",
              },
              {
                id: 2,
                title: "Structuring",
                description: "Creating logical content structure",
              },
              {
                id: 3,
                title: "Generating Outline",
                description: "Building detailed outline",
              },
              {
                id: 4,
                title: "Formatting",
                description: "Finalizing outline format",
              },
            ]
          } else if (isContentGeneration) {
            steps = [
              {
                id: 1,
                title: "Research",
                description: "Gathering relevant information",
              },
              {
                id: 2,
                title: "Planning",
                description: "Structuring content approach",
              },
              { id: 3, title: "Writing", description: "Generating content" },
              {
                id: 4,
                title: "Review",
                description: "Polishing and finalizing",
              },
            ]
          } else if (isSummarization) {
            steps = [
              {
                id: 1,
                title: "Analysis",
                description: "Analyzing source content",
              },
              {
                id: 2,
                title: "Extraction",
                description: "Identifying key points",
              },
              {
                id: 3,
                title: "Summarizing",
                description: "Creating concise summary",
              },
              {
                id: 4,
                title: "Formatting",
                description: "Finalizing summary format",
              },
            ]
          } else if (isGrammarCheck) {
            steps = [
              {
                id: 1,
                title: "Scanning",
                description: "Analyzing text for issues",
              },
              {
                id: 2,
                title: "Checking",
                description: "Identifying grammar and style issues",
              },
              {
                id: 3,
                title: "Correcting",
                description: "Applying corrections",
              },
              { id: 4, title: "Reviewing", description: "Final quality check" },
            ]
          }

          // Send step updates
          for (let i = 0; i < steps.length - 1; i++) {
            const step = steps[i]
            send({
              step: step.id,
              action: step.description,
              progress: 20 + i * 20,
              status: "active",
            })

            // Add realistic delay
            await new Promise((resolve) =>
              setTimeout(resolve, 1000 + Math.random() * 1000),
            )
          }

          send({
            step: steps.length - 1,
            action: "Generating AI response...",
            progress: 80,
            status: "active",
          })

          // Generate the actual response
          let response: any
          try {
            const timeoutDuration = 120000 // 2 minutes

            response = await Promise.race([
              contentAgent.generate([
                {
                  role: "user",
                  content: message,
                },
              ]),
              new Promise((_, reject) =>
                setTimeout(
                  () =>
                    reject(
                      new Error(
                        `Agent generation timeout after ${timeoutDuration / 1000} seconds`,
                      ),
                    ),
                  timeoutDuration,
                ),
              ),
            ])

            console.log("Content agent generation completed")
          } catch (error) {
            console.error("Content agent generation failed:", error)
            send({
              step: steps.length,
              action: "Error occurred during generation",
              progress: 100,
              status: "error",
              error: error instanceof Error ? error.message : "Unknown error",
            })
            controller.close()
            return
          }

          // Save session if documentId provided
          if (documentId) {
            try {
              // Get environment ID from the environment slug
              const { data: environment } = await supabase
                .from("environments")
                .select("id")
                .eq("slug", envSlug)
                .single()

              if (environment) {
                const contentService = new ContentService(supabase)
                await contentService.createSession({
                  document_id: documentId,
                  user_id: user.id,
                  environment_id: environment.id,
                  session_data: {
                    message,
                    response: response.text,
                    action,
                    timestamp: new Date().toISOString(),
                    steps,
                  },
                })
              }
            } catch (error) {
              console.warn("Failed to save session:", error)
            }
          }

          send({
            step: steps.length,
            action: "Content generated successfully!",
            progress: 100,
            status: "completed",
            response: response.text,
            documentId,
            actionType: action,
          })

          controller.close()
        } catch (error) {
          console.error("Streaming error:", error)
          send({
            step: 0,
            action: "Error occurred",
            progress: 0,
            status: "error",
            error: error instanceof Error ? error.message : "Unknown error",
          })
          controller.close()
        }
      })()
    },
  })

  return new Response(stream, {
    headers: {
      "Content-Type": "text/event-stream",
      "Cache-Control": "no-cache",
      Connection: "keep-alive",
    },
  })
}

// GET handler for document operations
export const GET: RequestHandler = async ({ url, locals, params }) => {
  const { session, supabase } = locals.auth
  if (!session) {
    return json({ error: "Unauthorized" }, { status: 401 })
  }

  const documentId = url.searchParams.get("documentId")
  const action = url.searchParams.get("action")

  const contentService = new ContentService(supabase)

  try {
    if (action === "list") {
      // Get environment ID from slug
      const { data: environment } = await supabase
        .from("environments")
        .select("id")
        .eq("slug", params.envSlug)
        .single()

      if (!environment) {
        return json({ error: "Environment not found" }, { status: 404 })
      }

      const documents = await contentService.getUserDocuments(
        session.user.id,
        environment.id,
        {
          limit: parseInt(url.searchParams.get("limit") || "50"),
          offset: parseInt(url.searchParams.get("offset") || "0"),
          status: url.searchParams.get("status") || undefined,
          contentType: url.searchParams.get("contentType") || undefined,
        },
      )

      return json({ documents })
    }

    if (action === "get" && documentId) {
      const document = await contentService.getDocument(
        documentId,
        session.user.id,
      )
      if (!document) {
        return json({ error: "Document not found" }, { status: 404 })
      }
      return json({ document })
    }

    if (action === "stats") {
      // Get environment ID from slug
      const { data: environment } = await supabase
        .from("environments")
        .select("id")
        .eq("slug", params.envSlug)
        .single()

      if (!environment) {
        return json({ error: "Environment not found" }, { status: 404 })
      }

      const stats = await contentService.getDocumentStats(
        session.user.id,
        environment.id,
      )
      return json({ stats })
    }

    return json({ error: "Invalid action" }, { status: 400 })
  } catch (error) {
    console.error("GET request error:", error)
    return json(
      { error: error instanceof Error ? error.message : "Unknown error" },
      { status: 500 },
    )
  }
}
