<script lang="ts">
  import { invalidate } from "$app/navigation"
  import { onMount } from "svelte"
  import { ToolRegistry } from '$lib/services/tool-registry'
  import ToolDetailModal from '$lib/components/ToolDetailModal.svelte'
  import type { ToolInfo } from '$lib/services/tool-registry'
  import DashboardLayout from '$lib/components/DashboardLayout.svelte'

  export let data

  let selectedTool: ToolInfo | null = null
  let toolModalOpen = false

  const allTools = ToolRegistry.getAllTools()
  const categoryStats = ToolRegistry.getCategoryStats()

  // Reactive filtered tools - for now just pass all tools
  $: filteredTools = allTools

  function handleToolClick(event: CustomEvent<ToolInfo>) {
    selectedTool = event.detail
    toolModalOpen = true
  }

  function handleAgentNavigation(event: CustomEvent) {
    // Handle agent navigation if needed
    console.log('Agent navigation:', event.detail)
  }

  onMount(async () => {
    invalidate("data:init")
  })
</script>

<svelte:head>
  <title>Dashboard - Robynn.ai</title>
</svelte:head>

<div class="h-full w-full">
  <DashboardLayout
    session={data.session}
    environment={data.environment}
    {allTools}
    {categoryStats}
    {filteredTools}
    on:toolClick={handleToolClick}
    on:agentNavigation={handleAgentNavigation}
  />
</div>

<!-- Tool Detail Modal -->
<ToolDetailModal bind:open={toolModalOpen} tool={selectedTool} />
