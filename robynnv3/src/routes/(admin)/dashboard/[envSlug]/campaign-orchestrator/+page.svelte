<script lang="ts">
  import { writable } from "svelte/store"
  import { page } from "$app/stores"
  import { onMount } from "svelte"
  import { PanelRightOpen, PanelRightClose, ChevronRight, Menu } from "lucide-svelte"
  import CampaignOrchestratorSidebar from "$lib/components/CampaignOrchestratorSidebar.svelte"
  import CampaignCanvas from "$lib/components/CampaignCanvas.svelte"

  interface CampaignMessage {
    id: string
    role: "user" | "assistant"
    content: string
    timestamp: Date
    isReport?: boolean
    data?: any
  }

  interface ProgressStep {
    id: number
    title: string
    description: string
    status: "pending" | "active" | "completed"
    progress?: number
  }

  // State management
  let sidebarOpen = true // Default open
  const messages = writable<CampaignMessage[]>([])
  let isLoading = false
  let progressSteps: ProgressStep[] = []
  let currentProgress = 0
  let sidebarComponent: CampaignOrchestratorSidebar

  // Header navigation state
  let mobileMenuOpen = false

  // Error handling state
  let requestTimeout: NodeJS.Timeout | undefined
  const REQUEST_TIMEOUT_MS = 120000 // 2 minutes timeout

  function generateId(): string {
    return Math.random().toString(36).substring(2, 11)
  }

  function handleRequestError(error: any, errorType: 'timeout' | 'network' | 'server' | 'unknown' = 'unknown') {
    console.error("Campaign Orchestrator error:", error)

    // Clear any existing timeout
    if (requestTimeout) {
      clearTimeout(requestTimeout)
      requestTimeout = undefined
    }

    // Mark current active step as failed and reset others to pending
    progressSteps = progressSteps.map((step) => {
      if (step.status === "active") {
        return { ...step, status: "pending", description: "Failed - ready to retry" }
      }
      return { ...step, status: step.status === "completed" ? "completed" : "pending" }
    })

    // Determine error message based on type
    let errorMessage = "Sorry, there was an error processing your request. Please try again."
    if (errorType === 'timeout') {
      errorMessage = "Request timed out. The research is taking longer than expected. Please try again."
    } else if (errorType === 'network') {
      errorMessage = "Network connection issue. Please check your connection and try again."
    } else if (errorType === 'server') {
      errorMessage = "Server error occurred during processing. Please try again in a moment."
    }

    messages.update((msgs) => [
      ...msgs,
      {
        id: generateId(),
        role: "assistant",
        content: errorMessage,
        timestamp: new Date(),
        isReport: true,
      },
    ])
  }

  function toggleSidebar() {
    sidebarOpen = !sidebarOpen
  }

  async function handleSendMessage(event: CustomEvent) {
    const { message } = event.detail
    if (!message.trim() || isLoading) return

    isLoading = true

    // Set up request timeout
    requestTimeout = setTimeout(() => {
      if (isLoading) {
        handleRequestError(new Error("Request timeout"), 'timeout')
        isLoading = false
      }
    }, REQUEST_TIMEOUT_MS)

    // Initialize progress steps - synchronized with server definitions
    progressSteps = [
      {
        id: 1,
        title: "Processing Input",
        description: "Processing input and extracting company information",
        status: "pending",
      },
      {
        id: 2,
        title: "Company Enrichment",
        description: "Enriching target company data with Apollo API",
        status: "pending",
      },
      {
        id: 3,
        title: "Intelligence Gathering",
        description: "Gathering competitive intelligence with Exa search",
        status: "pending",
      },
      {
        id: 4,
        title: "Company Discovery",
        description: "Discovering similar companies and competitors",
        status: "pending",
      },
      {
        id: 5,
        title: "Contact Retrieval",
        description: "Retrieving contact information for all companies",
        status: "pending",
      },
      {
        id: 6,
        title: "Results Formatting",
        description: "Formatting comprehensive research results",
        status: "pending",
      },
    ]
    currentProgress = 0

    try {
      // Use streaming for progress updates
      const response = await fetch(
        `/dashboard/${$page.params.envSlug}/campaign-orchestrator?stream=true`,
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ message }),
        },
      )

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const reader = response.body?.getReader()
      const decoder = new TextDecoder()

      if (reader) {
        while (true) {
          const { done, value } = await reader.read()
          if (done) break

          const chunk = decoder.decode(value)
          const lines = chunk.split("\n")

          for (const line of lines) {
            if (line.startsWith("data: ")) {
              try {
                const data = JSON.parse(line.slice(6))

                if (data.step && data.step > 0) {
                  // Update progress
                  currentProgress = data.progress || 0

                  // Update step status
                  progressSteps = progressSteps.map((step) => {
                    if (step.id < data.step) {
                      return { ...step, status: "completed" }
                    } else if (step.id === data.step) {
                      return {
                        ...step,
                        status: "active",
                        description: data.action,
                      }
                    }
                    return step
                  })
                }

                if (data.status === "completed" && data.response) {
                  console.log("Received completed response:", {
                    response: data.response,
                    data: data.data,
                    hasData: !!data.data,
                  })

                  // Clear timeout on successful completion
                  if (requestTimeout) {
                    clearTimeout(requestTimeout)
                    requestTimeout = undefined
                  }

                  // Mark all steps as completed and set progress to 100%
                  progressSteps = progressSteps.map((step) => ({
                    ...step,
                    status: "completed",
                  }))
                  currentProgress = 100

                  // Add assistant response with structured data from server
                  messages.update((msgs) => [
                    ...msgs,
                    {
                      id: generateId(),
                      role: "assistant",
                      content: data.response,
                      timestamp: new Date(),
                      data: data.data || null,
                      isReport: true,
                    },
                  ])

                  // Also add to sidebar
                  if (sidebarComponent) {
                    sidebarComponent.addAssistantMessage(data.response, data.data || null)
                  }
                }

                if (data.status === "error") {
                  throw new Error(data.error || "Unknown error occurred")
                }
              } catch (parseError) {
                console.warn("Failed to parse SSE data:", parseError)
              }
            }
          }
        }
      }
    } catch (error) {
      // Determine error type for better user feedback
      let errorType: 'timeout' | 'network' | 'server' | 'unknown' = 'unknown'

      if (error instanceof Error) {
        if (error.message.includes('timeout') || error.message.includes('Request timeout')) {
          errorType = 'timeout'
        } else if (error.message.includes('fetch') || error.message.includes('network')) {
          errorType = 'network'
        } else if (error.message.includes('server') || error.message.includes('500')) {
          errorType = 'server'
        }
      }

      handleRequestError(error, errorType)
    } finally {
      isLoading = false
      // Clear timeout if still active
      if (requestTimeout) {
        clearTimeout(requestTimeout)
        requestTimeout = undefined
      }
    }
  }

  function handleExampleSelect(event: CustomEvent) {
    const { message } = event.detail
    // Populate the sidebar input field instead of auto-executing
    if (sidebarComponent) {
      sidebarComponent.populateInput(message)
      // Open sidebar if it's closed to show the populated input
      if (!sidebarOpen) {
        sidebarOpen = true
      }
    }
  }



  // Mount lifecycle
  onMount(() => {
    // Any initialization if needed
  })

  // Progress updates now handled entirely by server streaming
  // Removed client-side simulation to prevent race conditions

  // Auto-scroll to bottom when new messages are added (modern layout)
  $: if ($messages.length > 0) {
    setTimeout(() => {
      // Scroll to the bottom of the page to show the latest message
      window.scrollTo({
        top: document.body.scrollHeight,
        behavior: "smooth",
      })
    }, 100)
  }
</script>

<svelte:head>
  <title>Deep Researcher Agent - AI Company Research & Intelligence</title>
  <meta
    name="description"
    content="Comprehensive company research with competitor analysis and contact discovery powered by AI"
  />
</svelte:head>

<!-- Main Page Container -->
<div class="min-h-screen bg-background">
  <!-- Dashboard Header -->
  <!-- Header Navigation -->
  <div class="bg-background border-b-2 border-border sticky top-0 z-50 nav-blur">
    <div class="max-w-6xl mx-auto px-6 py-4">
      <div class="flex items-center justify-between">
        <div class="flex-1 flex items-center space-x-2">
          <div
            class="w-8 h-8 flex items-center justify-center bg-primary text-primary-foreground border-2 border-border shadow-soft-sm"
          >
            <span class="font-bold text-sm">R</span>
          </div>
          <a
            href="/"
            class="text-xl font-bold text-foreground hover:opacity-70 transition-colors"
          >
            Robynn.ai
          </a>
        </div>
        <div class="flex-none">
          <ul class="hidden sm:flex items-center gap-8 font-bold">
            <li>
              <a
                href="/sign_out"
                class="text-muted-foreground hover:text-foreground transition-colors"
              >
                Sign Out
              </a>
            </li>
            <li>
              <a
                href="/dashboard/{$page.params.envSlug}"
                class="btn-professional px-6 py-2"
              >
                Dashboard
              </a>
            </li>
          </ul>

          <div class="sm:hidden">
            <button
              on:click={() => mobileMenuOpen = !mobileMenuOpen}
              class="p-2 text-muted-foreground hover:text-foreground transition-colors"
              aria-label="Toggle menu"
            >
              <Menu class="h-5 w-5" />
            </button>

            {#if mobileMenuOpen}
              <div class="absolute right-0 top-full mt-2 w-56 bg-background border-2 border-border shadow-lg rounded-lg">
                <div class="p-2">
                  <a
                    href="/sign_out"
                    class="block px-3 py-2 text-sm font-bold text-muted-foreground hover:text-foreground transition-colors"
                    on:click={() => mobileMenuOpen = false}
                  >
                    Sign Out
                  </a>
                  <a
                    href="/dashboard/{$page.params.envSlug}"
                    class="block px-3 py-2 text-sm font-bold text-muted-foreground hover:text-foreground transition-colors"
                    on:click={() => mobileMenuOpen = false}
                  >
                    Dashboard
                  </a>
                </div>
              </div>
            {/if}
          </div>
        </div>
      </div>
    </div>
  </div>

<!-- Breadcrumb Navigation -->
<div class="bg-background border-b border-border">
  <div class="px-6 py-4">
    <nav class="flex items-center space-x-2 text-sm text-muted-foreground">
      <a
        href="/dashboard/{$page.params.envSlug}"
        class="hover:text-foreground transition-colors"
      >
        Dashboard
      </a>
      <ChevronRight class="w-4 h-4" />
      <span class="text-foreground font-medium">Deep Researcher Agent</span>
    </nav>
  </div>
</div>

<!-- Responsive Sidebar + Canvas Layout -->
<div class="flex-1 flex bg-background relative" style="height: calc(100vh - 120px);">
  <!-- Sidebar Toggle Button -->
  <button
    on:click={toggleSidebar}
    class="sidebar-toggle-btn absolute top-4 right-4 z-40 p-2 rounded-lg border border-border bg-card hover:bg-accent transition-all duration-200 shadow-sm hover:shadow-md"
    class:toggle-with-sidebar={sidebarOpen}
    aria-label="{sidebarOpen ? 'Close Deep Researcher Agent sidebar' : 'Open Deep Researcher Agent sidebar'}"
  >
    {#if sidebarOpen}
      <PanelRightClose class="w-5 h-5 text-foreground" />
    {:else}
      <PanelRightOpen class="w-5 h-5 text-foreground" />
    {/if}
  </button>

  <!-- Main Canvas Area -->
  <CampaignCanvas
    messages={$messages}
    {isLoading}
    showExamples={$messages.length === 0}
    on:selectExample={handleExampleSelect}
  />

  <!-- Sidebar -->
  <CampaignOrchestratorSidebar
    bind:this={sidebarComponent}
    bind:isOpen={sidebarOpen}
    {isLoading}
    {progressSteps}
    {currentProgress}
    envSlug={$page.params.envSlug}
    on:sendMessage={handleSendMessage}
  />

  </div>
</div>

<style>
  /* Sidebar toggle button positioning */
  .sidebar-toggle-btn {
    transition: right 400ms ease-out;
  }

  /* Adjust toggle button position when sidebar is open on desktop */
  @media (min-width: 768px) {
    .toggle-with-sidebar {
      right: calc(24rem + 1rem); /* 384px sidebar width + 16px margin */
    }
  }

  /* Mobile: Keep toggle button in fixed position */
  @media (max-width: 767px) {
    .sidebar-toggle-btn {
      position: fixed !important;
      z-index: 60;
      right: 1rem;
    }
  }
</style>


