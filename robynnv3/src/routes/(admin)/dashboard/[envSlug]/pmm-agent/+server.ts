import { json } from "@sveltejs/kit"
import type { Request<PERSON><PERSON><PERSON> } from "./$types"
import { getProductMarketingAgent } from "$lib/agents/product-marketing-agent"
import { createAGUIAgent } from "$lib/agui/agent-adapter"
import { ENV_VARS } from "$lib/agents/shared/env"

// Input validation function
function validateInput(message: string): {
  isValid: boolean
  error?: string
  extractedInput?: string
} {
  const trimmed = message.trim()

  if (trimmed.length === 0) {
    return { isValid: false, error: "Message cannot be empty" }
  }

  if (trimmed.length > 500) {
    return {
      isValid: false,
      error: "Message too long. Please keep it under 500 characters.",
    }
  }

  return { isValid: true, extractedInput: trimmed }
}

// Convert AG-UI events to Server-Sent Events format
function formatSSE(event: any): string {
  return `data: ${JSON.stringify(event)}\n\n`
}

// Main AG-UI streaming handler
export const POST: RequestHandler = async ({ request, locals, url }) => {
  // Verify user is authenticated
  const { session, user } = locals.auth
  if (!session || !user) {
    return json({ error: "Unauthorized" }, { status: 401 })
  }

  // Check if AG-UI is enabled
  if (!ENV_VARS.AGUI_ENABLED()) {
    return json({ 
      error: "AG-UI streaming is not enabled",
      suggestion: "Set AGUI_ENABLED=true in environment variables" 
    }, { status: 503 })
  }

  // Check if client wants streaming response
  const wantsStream = url.searchParams.get("stream") === "true"

  if (wantsStream) {
    return handleStreamingRequest(request, session)
  }

  // Fallback to non-streaming response
  return handleNonStreamingRequest(request)
}

// Streaming request handler
async function handleStreamingRequest(request: Request, session: any) {
  try {
    const { message, conversationId, context } = await request.json()

    if (!message || typeof message !== "string") {
      return json({ error: "Message is required" }, { status: 400 })
    }

    // Validate input
    const validationResult = validateInput(message)
    if (!validationResult.isValid) {
      return json({ error: validationResult.error }, { status: 400 })
    }

    console.log("PMM Agent streaming request:", message.substring(0, 100))

    // Create AG-UI compatible agent
    const productMarketingAgent = await getProductMarketingAgent()
    const aguiAgent = createAGUIAgent(productMarketingAgent, {
      enableToolEmission: ENV_VARS.AGUI_STREAMING_ENABLED(),
      initialState: context || {}
    })

    // Create streaming response
    const stream = new ReadableStream({
      start(controller) {
        const encoder = new TextEncoder()
        let isClosed = false

        const sendEvent = (event: any) => {
          if (isClosed) {
            console.warn('Attempted to send event after controller was closed:', event.type)
            return false
          }
          
          // Check if controller is still available and not closed
          if (!controller || controller.desiredSize === null) {
            console.warn('Controller is closed or unavailable, skipping event:', event.type)
            isClosed = true
            return false
          }
          
          try {
            const chunk = encoder.encode(formatSSE(event))
            controller.enqueue(chunk)
            return true
          } catch (error) {
            console.error('Error sending event:', error)
            isClosed = true
            return false
          }
        }

        const closeController = () => {
          if (!isClosed) {
            isClosed = true
            try {
              controller.close()
            } catch (error) {
              console.error('Error closing controller:', error)
            }
          }
        }

        // Start the AG-UI stream with memory context
        performAGUIStreaming(message, conversationId, session, sendEvent, aguiAgent)
          .then(() => {
            closeController()
          })
          .catch((error) => {
            console.error("AG-UI streaming error:", error)
            
            // Try to send error event if possible
            if (!isClosed) {
              try {
                sendEvent({
                  type: 'ERROR',
                  error: error.message || 'Unknown streaming error',
                  timestamp: new Date().toISOString()
                })
              } catch (sendError) {
                console.error('Failed to send error event:', sendError)
              }
            }
            closeController()
          })
      },
    })

    return new Response(stream, {
      headers: {
        "Content-Type": "text/event-stream",
        "Cache-Control": "no-cache",
        "Connection": "keep-alive",
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "POST, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type"
      },
    })
  } catch (error) {
    console.error("AG-UI streaming setup error:", error)
    return json(
      { error: "An error occurred setting up the stream" },
      { status: 500 },
    )
  }
}

// Non-streaming fallback handler
async function handleNonStreamingRequest(request: Request) {
  try {
    const { message, context } = await request.json()

    if (!message || typeof message !== "string") {
      return json({ error: "Message is required" }, { status: 400 })
    }

    // Validate input
    const validationResult = validateInput(message)
    if (!validationResult.isValid) {
      return json({ error: validationResult.error }, { status: 400 })
    }

    console.log("PMM Agent non-streaming request:", message.substring(0, 100))

    // Use traditional agent execution
    const productMarketingAgent = await getProductMarketingAgent()
    const response = await productMarketingAgent.generate([
      {
        role: "user",
        content: message,
      },
    ])

    return json({
      response: response.text,
      success: true,
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    console.error("PMM Agent error:", error)
    return json(
      {
        error: "An error occurred processing your request. Please try again.",
        type: "internal_error",
      },
      { status: 500 },
    )
  }
}

// AG-UI streaming execution with memory
async function performAGUIStreaming(
  message: string,
  conversationId: string | undefined,
  session: any,
  sendEvent: (event: any) => boolean,
  aguiAgent: any
) {
  try {
    // Create message array in AG-UI format
    const messages = [{ role: 'user' as const, content: message }]

    // Prepare memory context for Mastra
    const memoryOptions = conversationId && session?.user?.id ? {
      memory: {
        resource: `user_${session.user.id}`, // User-specific memory
        thread: { id: conversationId }, // Conversation thread
      }
    } : undefined

    console.log("=== PMM AGENT MEMORY DEBUG ===")
    console.log("Session user ID:", session?.user?.id)
    console.log("Conversation ID:", conversationId)
    console.log("Memory options:", JSON.stringify(memoryOptions, null, 2))
    console.log("Message being sent:", message)
    console.log("===============================")

    // Stream events from AG-UI agent with memory
    for await (const event of aguiAgent.generateStream(messages, memoryOptions)) {
      const success = sendEvent(event)
      if (!success) {
        console.log('Stream closed, stopping AG-UI generation')
        break
      }
      
      // Add small delay between events for better UX
      await new Promise(resolve => setTimeout(resolve, 10))
    }
  } catch (error) {
    console.error("AG-UI streaming execution error:", error)
    throw error
  }
}

// Health check endpoint
export const GET: RequestHandler = async () => {
  return json({
    message: "Product Marketing Research Agent",
    description: "Elite research assistant with comprehensive analysis capabilities and AG-UI streaming",
    features: {
      streaming: ENV_VARS.AGUI_ENABLED(),
      toolEmission: ENV_VARS.AGUI_STREAMING_ENABLED()
    },
    endpoints: {
      "POST /": "Submit research request",
      "POST /?stream=true": "Submit request with AG-UI streaming",
    },
    usage: {
      input: "Research query or question",
      output: "Comprehensive research results with real-time streaming",
    },
  })
}
