<script lang="ts">
  import { page } from "$app/stores"
  import {
    Menu,
    ChevronRight,
    PanelRightClose,
    PanelRightOpen
  } from "lucide-svelte"

  // Import AG-UI components
  import AGUIAgentSidebar from "$lib/components/agui/AGUIAgentSidebar.svelte"
  import AGUICanvas from "$lib/components/agui/AGUICanvas.svelte"

  // Import AG-UI stores
  import { conversationStore } from "$lib/agui/conversation-store"

  import { writable } from 'svelte/store'

  // Sidebar state management
  const leftSidebarCollapsed = writable(false)
  const rightSidebarCollapsed = writable(false)

  // Reactive variables
  let innerWidth = $state(0)
  let isMobile = $derived(innerWidth < 768)
  let mobileMenuOpen = $state(false)
  let sidebarOpen = $state(true)
  let sidebarComponent: AGUIAgentSidebar

  // Subscribe to conversation state
  let isLoading = $derived($conversationStore.isLoading)

  // Toggle functions
  function toggleSidebar() {
    sidebarOpen = !sidebarOpen
    rightSidebarCollapsed.set(!sidebarOpen)
  }

  // Responsive behavior - auto-collapse on mobile
  $effect(() => {
    if (isMobile) {
      leftSidebarCollapsed.set(true)
      rightSidebarCollapsed.set(true)
    }
  })

  // Event handlers
  async function handleSendMessage(event: CustomEvent) {
    const { message } = event.detail
    if (!message.trim() || isLoading) return

    // The AG-UI sidebar component handles the message sending
    // via the conversation store, so we just need to populate it
    if (sidebarComponent) {
      sidebarComponent.populateInput(message)
    }
  }

  function handleSidebarClose() {
    sidebarOpen = false
  }


</script>

<svelte:window bind:innerWidth />

<style>
  .sidebar-toggle-btn {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  .sidebar-toggle-btn:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: translateY(-1px);
  }
  
  .toggle-with-sidebar {
    right: 400px;
  }
  
  @media (max-width: 768px) {
    .toggle-with-sidebar {
      right: 1rem;
    }
  }
</style>

<!-- Full height container -->
<div class="h-screen flex flex-col bg-background">
  <!-- Header -->
  <div class="flex-none bg-background border-b border-border">
    <div class="px-6 py-3">
      <div class="flex items-center justify-between">
        <div class="flex items-center gap-3">
          <div
            class="w-8 h-8 flex items-center justify-center bg-primary text-primary-foreground border-2 border-border shadow-soft-sm"
          >
            <span class="font-bold text-sm">R</span>
          </div>
          <a
            href="/"
            class="text-xl font-bold text-foreground hover:opacity-70 transition-colors"
          >
            Robynn.ai
          </a>
        </div>
        <div class="flex-none">
          <ul class="hidden sm:flex items-center gap-8 font-bold">
            <li>
              <a
                href="/sign_out"
                class="text-muted-foreground hover:text-foreground transition-colors"
              >
                Sign Out
              </a>
            </li>
            <li>
              <a
                href="/dashboard/{$page.params.envSlug}"
                class="btn-professional px-6 py-2"
              >
                Dashboard
              </a>
            </li>
          </ul>

          <div class="sm:hidden">
            <button
              onclick={() => mobileMenuOpen = !mobileMenuOpen}
              class="p-2 text-muted-foreground hover:text-foreground transition-colors"
              aria-label="Toggle menu"
            >
              <Menu class="h-5 w-5" />
            </button>

            {#if mobileMenuOpen}
              <div class="absolute right-0 top-full mt-2 w-56 bg-background border-2 border-border shadow-lg rounded-lg z-50">
                <div class="p-2">
                  <a
                    href="/sign_out"
                    class="block px-3 py-2 text-sm font-bold text-muted-foreground hover:text-foreground transition-colors"
                    onclick={() => mobileMenuOpen = false}
                  >
                    Sign Out
                  </a>
                  <a
                    href="/dashboard/{$page.params.envSlug}"
                    class="block px-3 py-2 text-sm font-bold text-muted-foreground hover:text-foreground transition-colors"
                    onclick={() => mobileMenuOpen = false}
                  >
                    Dashboard
                  </a>
                </div>
              </div>
            {/if}
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Breadcrumb Navigation -->
  <div class="bg-background border-b border-border">
    <div class="px-6 py-4">
      <nav class="flex items-center space-x-2 text-sm text-muted-foreground">
        <a
          href="/dashboard/{$page.params.envSlug}"
          class="hover:text-foreground transition-colors"
        >
          Dashboard
        </a>
        <ChevronRight class="w-4 h-4" />
        <span class="text-foreground font-medium">Product Marketing Research Agent</span>
      </nav>
    </div>
  </div>

  <!-- Responsive Sidebar + Canvas Layout -->
  <div class="flex-1 flex bg-background relative" style="height: calc(100vh - 120px);">
    <!-- Sidebar Toggle Button -->
    <button
      onclick={toggleSidebar}
      class="sidebar-toggle-btn absolute top-4 right-4 z-40 p-2 rounded-lg border border-border bg-card hover:bg-accent transition-all duration-200 shadow-sm hover:shadow-md"
      class:toggle-with-sidebar={sidebarOpen}
      aria-label="{sidebarOpen ? 'Close PMM Agent sidebar' : 'Open PMM Agent sidebar'}"
    >
      {#if sidebarOpen}
        <PanelRightClose class="w-5 h-5 text-foreground" />
      {:else}
        <PanelRightOpen class="w-5 h-5 text-foreground" />
      {/if}
    </button>

    <!-- Main Canvas Area -->
    <AGUICanvas
      on:sendMessage={handleSendMessage}
    />

    <!-- PMM Agent Sidebar -->
    <AGUIAgentSidebar
      bind:this={sidebarComponent}
      bind:isOpen={sidebarOpen}
      on:close={handleSidebarClose}
    />
  </div>
</div>
