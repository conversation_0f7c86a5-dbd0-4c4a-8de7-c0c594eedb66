<script lang="ts">
  let { children } = $props()
  let isEurope = $state(false)
  try {
    isEurope = Intl.DateTimeFormat()
      .resolvedOptions()
      .timeZone.startsWith("Europe/")
  } catch (e) {
    /* continue */
  }
</script>

<!-- Let LoginLayout.svelte handle the main container layout -->
{@render children()}

<!-- <PERSON>ie Notice for Europe (positioned absolutely to not interfere with layout) -->
{#if isEurope}
  <div class="fixed bottom-4 left-1/2 transform -translate-x-1/2 z-50">
    <div class="bg-card border border-border rounded-lg px-4 py-2 shadow-lg">
      <span class="text-sm font-bold text-muted-foreground">
        🍪 Logging in uses Cookies 🍪
      </span>
    </div>
  </div>
{/if}
