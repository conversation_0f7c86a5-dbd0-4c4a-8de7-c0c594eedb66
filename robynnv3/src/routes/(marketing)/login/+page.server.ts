import { fail, redirect } from '@sveltejs/kit';
import { superValidate } from 'sveltekit-superforms';
import { zod } from 'sveltekit-superforms/adapters';
import { signUpSchema, signInSchema } from '$lib/schemas';
import type { Actions, PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ url, locals: { safeGetSession } }) => {
  const { session } = await safeGetSession();
  
  // If user is already logged in, redirect to find-env to get proper environment slug
  if (session) {
    redirect(303, '/find-env');
  }
  
  // Get initial mode from URL params (signin or signup)
  const mode = url.searchParams.get('mode') || 'signin';
  
  // Initialize forms for both modes
  const signInForm = await superValidate(zod(signInSchema));
  const signUpForm = await superValidate(zod(signUpSchema));
  
  return {
    signInForm,
    signUpForm,
    initialMode: mode === 'signup' ? 'signup' : 'signin'
  };
};

export const actions: Actions = {
  signin: async ({ request, locals: { supabase } }) => {
    const form = await superValidate(request, zod(signInSchema));
    
    if (!form.valid) {
      return fail(400, { form, mode: 'signin' });
    }
    
    const { email, password } = form.data;
    
    const { error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });
    
    if (error) {
      return fail(400, {
        form,
        mode: 'signin',
        error: error.message
      });
    }
    
    redirect(303, '/find-env');
  },
  
  signup: async ({ request, locals: { supabase } }) => {
    const form = await superValidate(request, zod(signUpSchema));
    
    if (!form.valid) {
      return fail(400, { form, mode: 'signup' });
    }
    
    const { email, password } = form.data;
    
    const { error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        emailRedirectTo: `${new URL(request.url).origin}/login/confirm`
      }
    });
    
    if (error) {
      return fail(400, {
        form,
        mode: 'signup',
        error: error.message
      });
    }
    
    redirect(303, '/login/check_email');
  }
};
