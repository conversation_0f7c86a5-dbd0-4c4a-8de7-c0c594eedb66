import { signInSchema } from "$lib/schemas"
import { redirect } from "@sveltejs/kit"
import { fail, superValidate } from "sveltekit-superforms"
import { zod } from "sveltekit-superforms/adapters"

export const load = async ({ locals: { safeGetSession } }) => {
  const { user } = await safeGetSession()

  // If user is already authenticated (not anonymous), redirect to dashboard
  if (user && !user.is_anonymous) {
    return redirect(300, "/find-env")
  }

  const form = await superValidate(zod(signInSchema))

  return { form }
}

export const actions = {
  default: async ({ request, locals: { supabase } }) => {
    const form = await superValidate(request, zod(signInSchema))

    if (!form.valid) {
      return fail(400, { form })
    }

    const { email, password } = form.data

    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    })

    if (error) {
      console.error("Sign-in error:", error)

      // Check if it's an email not confirmed error
      if (
        error.message.includes("email not confirmed") ||
        error.message.includes("Email not confirmed")
      ) {
        redirect(303, "/login/sign_in?error=email_not_confirmed")
      }

      return fail(400, {
        form,
        error: error.message,
      })
    }

    // Check if email is confirmed
    if (data.user && !data.user.email_confirmed_at) {
      // Sign out the user since they shouldn't be signed in without email confirmation
      await supabase.auth.signOut()
      redirect(303, "/login/sign_in?error=email_not_confirmed")
    }

    // Successful sign-in
    redirect(303, "/find-env")
  },
}
