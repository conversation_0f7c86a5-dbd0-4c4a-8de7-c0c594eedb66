<script lang="ts">
  import { superForm } from "sveltekit-superforms"
  import { zodClient } from "sveltekit-superforms/adapters"
  import { signInSchema } from "$lib/schemas"
  import * as Form from "$lib/components/ui/form"
  import * as Card from "$lib/components/ui/card"
  import { Input } from "$lib/components/ui/input"
  import { Button } from "$lib/components/ui/button"
  import { Eye, EyeOff, AlertCircle } from "lucide-svelte"
  import { fly } from "svelte/transition"
  import { EmailValidationFeedback } from "$lib/components/auth"

  let { data } = $props()

  const form = superForm(data.form, {
    validators: zodClient(signInSchema),
  })

  const { form: formData, enhance, delayed, errors, constraints } = form

  // State for password visibility and field touch tracking
  let showPassword = $state(false)
  let emailTouched = $state(false)
  let passwordTouched = $state(false)
</script>

<svelte:head>
  <title>Sign in</title>
</svelte:head>

<div class="flex items-start justify-center p-4 pt-8 mb-16">
  <Card.Root class="mt-6 w-full max-w-[100%] md:max-w-[65%] lg:max-w-[35%]">
    <Card.Header>
      <Card.Title class="text-2xl font-bold text-center">Sign In</Card.Title>
      <Card.Description>Sign in to your account to continue.</Card.Description>
    </Card.Header>
    <Card.Content>
      <form method="post" use:enhance class="grid gap-4">
        <Form.Field {form} name="email">
          <Form.Control let:attrs>
            <Form.Label>Email</Form.Label>
            <Input
              bind:value={$formData.email}
              onblur={() => (emailTouched = true)}
              {...attrs}
              {...$constraints.email}
            />
          </Form.Control>
          <EmailValidationFeedback
            email={$formData.email}
            touched={emailTouched}
            showConfirmationNotice={false}
          />
          <Form.FieldErrors />
        </Form.Field>

        <Form.Field {form} name="password">
          <Form.Control let:attrs>
            <Form.Label>Password</Form.Label>
            <div class="relative">
              <Input
                bind:value={$formData.password}
                type={showPassword ? "text" : "password"}
                onfocus={() => (passwordTouched = true)}
                {...attrs}
                {...$constraints.password}
              />
              <button
                type="button"
                onclick={() => (showPassword = !showPassword)}
                class="absolute right-3 top-1/2 -translate-y-1/2 text-base-content/60 hover:text-base-content transition-colors"
              >
                {#if showPassword}
                  <EyeOff size={18} />
                {:else}
                  <Eye size={18} />
                {/if}
              </button>
            </div>
          </Form.Control>
          <Form.FieldErrors />
        </Form.Field>

        {#if $errors._errors}
          <div
            class="flex items-center gap-2 p-3 mt-2 bg-red-50 border border-red-200 rounded-lg text-red-700 font-medium dark:bg-red-950/20 dark:border-red-800/30 dark:text-red-400"
            transition:fly={{ y: -10, duration: 300 }}
          >
            <AlertCircle size={16} class="flex-shrink-0" />
            <span>{$errors._errors[0]}</span>
          </div>
        {/if}

        <Button type="submit" disabled={$delayed} class="w-full">
          {#if $delayed}
            ...
          {:else}
            Sign In
          {/if}
        </Button>
      </form>

      <div class="text-center mt-4">
        <a class="underline text-primary" href="/login/forgot_password"
          >Forgot password?</a
        >
      </div>
    </Card.Content>

    <Card.Footer>
      <div class="mt-4 mb-2">
        Don't have an account? <a class="underline" href="/login/sign_up"
          >Sign up</a
        >.
      </div>
    </Card.Footer>
  </Card.Root>
</div>
