<script lang="ts">
  import { superForm } from "sveltekit-superforms"
  import { zodClient } from "sveltekit-superforms/adapters"
  import { emailSchema } from "$lib/schemas"
  import * as Form from "$lib/components/ui/form"
  import * as Card from "$lib/components/ui/card"
  import { Input } from "$lib/components/ui/input"
  import { Button } from "$lib/components/ui/button"
  import { AlertCircle, CheckCircle2 } from "lucide-svelte"
  import { fly } from "svelte/transition"
  import { EmailValidationFeedback } from "$lib/components/auth"

  let { data } = $props()

  const form = superForm(data.form, {
    validators: zodClient(emailSchema),
  })

  const { form: formData, enhance, delayed, errors, constraints } = form

  // State for field touch tracking
  let emailTouched = $state(false)
</script>

<svelte:head>
  <title>Forgot Password</title>
</svelte:head>

<div class="flex items-start justify-center p-4 pt-8 mb-16">
  <Card.Root class="mt-6 w-full max-w-[100%] md:max-w-[65%] lg:max-w-[35%]">
    <Card.Header>
      <Card.Title class="text-2xl font-bold text-center"
        >Forgot Password</Card.Title
      >
      <Card.Description>
        Enter your email address and we'll send you a link to reset your
        password.
      </Card.Description>
    </Card.Header>
    <Card.Content>
      <form method="post" use:enhance class="grid gap-4">
        <Form.Field {form} name="email">
          <Form.Control let:attrs>
            <Form.Label>Email</Form.Label>
            <Input
              bind:value={$formData.email}
              onblur={() => (emailTouched = true)}
              {...attrs}
              {...$constraints.email}
            />
          </Form.Control>
          <EmailValidationFeedback
            email={$formData.email}
            touched={emailTouched}
            showConfirmationNotice={false}
          />
          <Form.FieldErrors />
        </Form.Field>

        {#if $errors._errors}
          <div
            class="flex items-center gap-2 p-3 mt-2 bg-red-50 border border-red-200 rounded-lg text-red-700 font-medium dark:bg-red-950/20 dark:border-red-800/30 dark:text-red-400"
            transition:fly={{ y: -10, duration: 300 }}
          >
            <AlertCircle size={16} class="flex-shrink-0" />
            <span>{$errors._errors[0]}</span>
          </div>
        {/if}

        {#if (data as any).error}
          <div
            class="flex items-center gap-2 p-3 mt-2 bg-red-50 border border-red-200 rounded-lg text-red-700 font-medium dark:bg-red-950/20 dark:border-red-800/30 dark:text-red-400"
            transition:fly={{ y: -10, duration: 300 }}
          >
            <AlertCircle size={16} class="flex-shrink-0" />
            <span>{(data as any).error}</span>
          </div>
        {/if}

        {#if (data as any).success}
          <div
            class="flex items-center gap-2 p-3 mt-2 bg-green-50 border border-green-200 rounded-lg text-green-700 font-medium dark:bg-green-950/20 dark:border-green-800/30 dark:text-green-400"
            transition:fly={{ y: -10, duration: 300 }}
          >
            <CheckCircle2 size={16} class="flex-shrink-0" />
            <span>Password reset email sent! Check your inbox.</span>
          </div>
        {/if}

        <Button type="submit" disabled={$delayed} class="w-full">
          {#if $delayed}
            ...
          {:else}
            Send Reset Link
          {/if}
        </Button>
      </form>
    </Card.Content>

    <Card.Footer>
      <div class="mt-4 mb-2 text-center">
        Remember your password? <a
          class="underline text-primary"
          href="/login/sign_in">Sign in</a
        >.
      </div>
    </Card.Footer>
  </Card.Root>
</div>
