import { emailSchema } from "$lib/schemas"
import { fail, superValidate } from "sveltekit-superforms"
import { zod } from "sveltekit-superforms/adapters"

export const load = async () => {
  const form = await superValidate(zod(emailSchema))
  return { form }
}

export const actions = {
  default: async ({ request, locals: { supabase } }) => {
    const form = await superValidate(request, zod(emailSchema))

    if (!form.valid) {
      return fail(400, { form })
    }

    const { email } = form.data

    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${new URL(request.url).origin}/login/confirm`,
    })

    if (error) {
      console.error("Password reset error:", error)
      return fail(400, {
        form,
        error: error.message,
      })
    }

    // Return success state
    return {
      form,
      success: true,
    }
  },
}
