<script lang="ts">
  import { onMount } from "svelte"
  import { browser } from "$app/environment"

  import LoginLayout from "$lib/components/auth/LoginLayout.svelte"
  import BrandPanel from "$lib/components/auth/BrandPanel.svelte"
  import AuthPanel from "$lib/components/auth/AuthPanel.svelte"
  import AuthFooter from "$lib/components/auth/AuthFooter.svelte"
  import { Button } from "$lib/components/ui/button"
  import { Input } from "$lib/components/ui/input"
  import { Label } from "$lib/components/ui/label"
  import { Separator } from "$lib/components/ui/separator"
  import { Eye, EyeOff, Mail, Lock, AlertCircle } from "lucide-svelte"

  // Lazy load validation components for better performance
  let EmailValidationFeedback: any = null
  let PasswordStrengthIndicator: any = null

  // Load validation components only when needed
  const loadValidationComponents = async () => {
    if (!EmailValidationFeedback || !PasswordStrengthIndicator) {
      const {
        EmailValidationFeedback: EmailComp,
        PasswordStrengthIndicator: PasswordComp,
      } = await import("$lib/components/auth")
      EmailValidationFeedback = EmailComp
      PasswordStrengthIndicator = PasswordComp
    }
  }

  interface Props {
    data: {
      initialMode: "signin" | "signup"
    }
    form?: any
  }

  let { data, form: actionResult }: Props = $props()

  // Form state management
  let currentMode = $state(data.initialMode)
  let isLoading = $state(false)
  let error = $state<string | null>(null)

  // Form fields
  let email = $state("")
  let password = $state("")
  let confirmPassword = $state("")
  let showPassword = $state(false)
  let showConfirmPassword = $state(false)
  let agreedToTerms = $state(false)

  // Validation state
  let emailTouched = $state(false)
  let passwordTouched = $state(false)
  let confirmPasswordTouched = $state(false)

  // Email validation
  const emailRegex =
    /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/
  const passwordRegex = /^(?=.*\d)(?=.*[a-z])(?=.*[A-Z]).{8,16}$/

  const isValidEmail = $derived(emailRegex.test(email))
  const isValidPassword = $derived(
    currentMode === "signin"
      ? password.length >= 6
      : passwordRegex.test(password),
  )
  const passwordsMatch = $derived(
    password === confirmPassword && confirmPassword.length > 0,
  )
  const isFormValid = $derived(
    currentMode === "signin"
      ? isValidEmail && isValidPassword && !isLoading
      : isValidEmail &&
          isValidPassword &&
          passwordsMatch &&
          agreedToTerms &&
          !isLoading,
  )

  // Handle mode changes
  const toggleMode = () => {
    currentMode = currentMode === "signin" ? "signup" : "signin"
    error = null

    // Clear form fields when switching modes
    password = ""
    confirmPassword = ""
    agreedToTerms = false

    // Update URL without navigation
    if (browser) {
      const url = new URL(window.location.href)
      url.searchParams.set("mode", currentMode)
      window.history.replaceState({}, "", url)
    }
  }

  // Social login disabled - providers not configured in Supabase yet

  // Handle form submission with performance optimization
  const handleSubmit = async (event: Event) => {
    event.preventDefault()

    if (!isFormValid) return

    isLoading = true
    error = null

    const formData = new FormData()
    formData.append("email", email.trim())
    formData.append("password", password)

    if (currentMode === "signup") {
      formData.append("confirmPassword", confirmPassword)
    }

    try {
      const response = await fetch(`?/${currentMode}`, {
        method: "POST",
        body: formData,
      })

      if (!response.ok) {
        const result = await response.json()
        error = result.error || "An error occurred"
      } else {
        // Success - redirect to find-env to get proper environment slug
        window.location.href = "/find-env"
      }
    } catch (err) {
      error = "Network error. Please try again."
    } finally {
      isLoading = false
    }
  }

  // Load validation components when user starts interacting with forms
  const handleEmailFocus = async () => {
    await loadValidationComponents()
  }

  const handlePasswordFocus = async () => {
    await loadValidationComponents()
  }

  // Handle action results
  onMount(() => {
    if (actionResult?.error) {
      error = actionResult.error
      currentMode = actionResult.mode || currentMode
    }
  })

  // Dynamic page title and subtitle
  const pageConfig = {
    signin: {
      title: "Welcome back",
      subtitle: "Sign in to your account to continue",
      submitText: "Sign In",
      toggleText: "Don't have an account?",
      toggleAction: "Sign up",
    },
    signup: {
      title: "Create your account",
      subtitle: "Get started with your free account",
      submitText: "Create Account",
      toggleText: "Already have an account?",
      toggleAction: "Sign in",
    },
  }

  const config = $derived(pageConfig[currentMode])
</script>

<svelte:head>
  <title>{currentMode === "signin" ? "Sign In" : "Sign Up"} | Robynn AI</title>
  <meta name="description" content={config.subtitle} />
</svelte:head>

<LoginLayout showBrandPanel={true}>
  <!-- Brand Panel Slot -->
  <BrandPanel slot="brand-panel" />

  <!-- Auth Panel Slot -->
  <div slot="auth-panel">
    <AuthPanel
      title={config.title}
      subtitle={config.subtitle}
      showHeader={true}
    >
      <!-- Error Display -->
      {#if error}
        <div
          class="mb-4 p-3 bg-destructive/10 border border-destructive/20 rounded-lg text-destructive text-sm"
        >
          {error}
        </div>
      {/if}

      <!-- Email/Password Only - Social login disabled -->
      <!-- Social login providers are not configured in Supabase yet -->

      <!-- Main Form -->
      <form on:submit={handleSubmit} class="space-y-4">
        <!-- Email Field -->
        <div class="space-y-2">
          <Label for="email" class="text-sm font-medium text-foreground">
            Email address
          </Label>
          <div class="relative">
            <div
              class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
            >
              <Mail class="h-4 w-4 text-muted-foreground" />
            </div>
            <Input
              id="email"
              name="email"
              type="email"
              autocomplete="email"
              placeholder="Enter your email"
              bind:value={email}
              on:focus={handleEmailFocus}
              on:blur={() => (emailTouched = true)}
              class="pl-10"
              disabled={isLoading}
              required
            />
          </div>

          {#if emailTouched && EmailValidationFeedback}
            <svelte:component
              this={EmailValidationFeedback}
              {email}
              touched={emailTouched}
              showConfirmationNotice={currentMode === "signup"}
            />
          {/if}
        </div>

        <!-- Password Field -->
        <div class="space-y-2">
          <Label for="password" class="text-sm font-medium text-foreground">
            Password
          </Label>
          <div class="relative">
            <div
              class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
            >
              <Lock class="h-4 w-4 text-muted-foreground" />
            </div>
            <Input
              id="password"
              name="password"
              type={showPassword ? "text" : "password"}
              autocomplete={currentMode === "signin"
                ? "current-password"
                : "new-password"}
              placeholder={currentMode === "signin"
                ? "Enter your password"
                : "Create a strong password"}
              bind:value={password}
              on:focus={handlePasswordFocus}
              on:blur={() => (passwordTouched = true)}
              class="pl-10 pr-10"
              disabled={isLoading}
              required
            />
            <button
              type="button"
              on:click={() => (showPassword = !showPassword)}
              class="absolute inset-y-0 right-0 pr-3 flex items-center text-muted-foreground hover:text-foreground transition-colors"
              disabled={isLoading}
              aria-label={showPassword ? "Hide password" : "Show password"}
            >
              {#if showPassword}
                <EyeOff class="h-4 w-4" />
              {:else}
                <Eye class="h-4 w-4" />
              {/if}
            </button>
          </div>

          {#if currentMode === "signup" && passwordTouched && PasswordStrengthIndicator}
            <svelte:component
              this={PasswordStrengthIndicator}
              {password}
              showRequirements={true}
            />
          {/if}
        </div>

        <!-- Confirm Password Field (Sign-up only) -->
        {#if currentMode === "signup"}
          <div class="space-y-2">
            <Label
              for="confirmPassword"
              class="text-sm font-medium text-foreground"
            >
              Confirm password
            </Label>
            <div class="relative">
              <div
                class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
              >
                <Lock class="h-4 w-4 text-muted-foreground" />
              </div>
              <Input
                id="confirmPassword"
                name="confirmPassword"
                type={showConfirmPassword ? "text" : "password"}
                autocomplete="new-password"
                placeholder="Confirm your password"
                bind:value={confirmPassword}
                on:blur={() => (confirmPasswordTouched = true)}
                class="pl-10 pr-10"
                disabled={isLoading}
                required
              />
              <button
                type="button"
                on:click={() => (showConfirmPassword = !showConfirmPassword)}
                class="absolute inset-y-0 right-0 pr-3 flex items-center text-muted-foreground hover:text-foreground transition-colors"
                disabled={isLoading}
                aria-label={showConfirmPassword
                  ? "Hide password"
                  : "Show password"}
              >
                {#if showConfirmPassword}
                  <EyeOff class="h-4 w-4" />
                {:else}
                  <Eye class="h-4 w-4" />
                {/if}
              </button>
            </div>

            {#if confirmPasswordTouched && !passwordsMatch}
              <div class="flex items-center gap-2 text-sm text-destructive">
                <AlertCircle class="h-4 w-4" />
                <span>Passwords do not match</span>
              </div>
            {/if}
          </div>

          <!-- Terms Agreement -->
          <div class="flex items-start space-x-3">
            <input
              id="terms"
              name="agreedToTerms"
              type="checkbox"
              bind:checked={agreedToTerms}
              class="h-4 w-4 text-primary focus:ring-primary border-border rounded mt-1"
              disabled={isLoading}
              required
            />
            <Label
              for="terms"
              class="text-sm text-muted-foreground cursor-pointer leading-relaxed"
            >
              I agree to the
              <a
                href="/terms"
                class="text-primary hover:text-primary/80 underline-offset-4 hover:underline"
                target="_blank"
              >
                Terms of Service
              </a>
              and
              <a
                href="/privacy"
                class="text-primary hover:text-primary/80 underline-offset-4 hover:underline"
                target="_blank"
              >
                Privacy Policy
              </a>
            </Label>
          </div>
        {:else}
          <!-- Remember me (Sign-in only) -->
          <div class="flex items-center space-x-2">
            <input
              id="remember"
              name="remember"
              type="checkbox"
              class="h-4 w-4 text-primary focus:ring-primary border-border rounded"
              disabled={isLoading}
            />
            <Label
              for="remember"
              class="text-sm text-muted-foreground cursor-pointer"
            >
              Remember me for 30 days
            </Label>
          </div>
        {/if}

        <!-- Submit Button -->
        <Button
          type="submit"
          size="lg"
          class="w-full btn-professional"
          disabled={!isFormValid}
        >
          {#if isLoading}
            <svg
              class="animate-spin -ml-1 mr-3 h-5 w-5"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
            >
              <circle
                class="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                stroke-width="4"
              ></circle>
              <path
                class="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              ></path>
            </svg>
            {currentMode === "signin" ? "Signing in..." : "Creating account..."}
          {:else}
            {config.submitText}
          {/if}
        </Button>
      </form>

      <!-- Mode Toggle -->
      <div class="mt-6 text-center text-sm text-muted-foreground">
        {config.toggleText}
        <button
          type="button"
          on:click={toggleMode}
          disabled={isLoading}
          class="text-primary hover:text-primary/80 transition-colors underline-offset-4 hover:underline ml-1"
        >
          {config.toggleAction}
        </button>
      </div>

      {#if currentMode === "signin"}
        <div class="text-center">
          <a
            href="/login/forgot_password"
            class="text-sm text-primary hover:text-primary/80 transition-colors underline-offset-4 hover:underline"
          >
            Forgot your password?
          </a>
        </div>
      {/if}

      <!-- Footer Slot -->
      <div slot="footer">
        <AuthFooter showBackToHome />
      </div>
    </AuthPanel>
  </div>
</LoginLayout>
