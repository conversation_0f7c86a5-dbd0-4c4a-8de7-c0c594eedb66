import { fail } from "@sveltejs/kit"
import { setError, superValidate } from "sveltekit-superforms"
import { zod } from "sveltekit-superforms/adapters"

import { handlePostSubmit } from "$lib/contact-notifier"
import { contactSchema } from "$lib/schemas"

export const load = async () => {
  const form = await superValidate(zod(contactSchema))

  return { form }
}

export const actions = {
  submitContactUs: async ({ request, locals: { supabaseServiceRole } }) => {
    const form = await superValidate(request, zod(contactSchema))

    if (!form.valid) {
      return fail(400, { form })
    }

    // Save to database
    const { error: insertError } = await supabaseServiceRole
      .from("contact_requests")
      .insert(form.data)

    if (insertError) {
      console.warn({ insertError })
      return setError(form, "Something went wrong", { status: 500 })
    }

    // Send notifications after successful submission
    await handlePostSubmit(form.data)

    return { form }
  },
}
