import { getManifestoContentService } from "$lib/services/manifesto-content";
import type { ManifestoPageData } from "$lib/types/manifesto";

export const load = async ({ parent }) => {
  // Get parent data (auth and environment from root layout)
  const parentData = await parent();

  const manifestoService = getManifestoContentService();

  try {
    const manifestoData = await manifestoService.getManifestoPageData();

    return {
      ...parentData,
      ...manifestoData,
      // Add structured data for SEO
      structuredData: {
        "@context": "https://schema.org",
        "@type": "Article",
        "headline": manifestoData.meta.title,
        "description": manifestoData.meta.description,
        "author": {
          "@type": "Person",
          "name": manifestoData.meta.author,
          "jobTitle": manifestoData.manifesto?.frontmatter.role || "Founder"
        },
        "publisher": {
          "@type": "Organization",
          "name": "<PERSON><PERSON>",
          "url": "https://robynn.ai"
        },
        "datePublished": manifestoData.meta.publishedDate,
        "dateModified": manifestoData.meta.lastModified,
        "articleSection": manifestoData.meta.category,
        "keywords": manifestoData.meta.tags.join(", "),
        "wordCount": manifestoData.manifesto?.content.split(' ').length || 0,
        "timeRequired": manifestoData.meta.readingTime
      }
    };
  } catch (error) {
    console.error("Error loading manifesto page:", error);

    // Return fallback data structure
    return {
      ...parentData,
      manifesto: null,
      meta: {
        title: "The Robynn Manifesto - Rethinking Marketing for Builders",
        description: "A founder's vision for transforming marketing from complexity to conversation through intelligent agents.",
        author: "Madhukar Kumar",
        publishedDate: new Date().toISOString(),
        lastModified: new Date().toISOString(),
        readingTime: "12 min read",
        category: "Vision",
        tags: ["manifesto", "marketing", "agents", "growth", "philosophy"]
      },
      structuredData: null
    };
  }
};
