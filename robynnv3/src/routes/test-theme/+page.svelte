<script lang="ts">
  import { page } from '$app/stores';
</script>

<svelte:head>
  <title>DaisyUI Theme Test</title>
</svelte:head>

<div class="min-h-screen bg-base-100 p-8">
  <div class="max-w-4xl mx-auto space-y-8">
    <h1 class="text-4xl font-bold text-base-content">DaisyUI Theme Integration Test</h1>
    
    <div class="alert alert-info">
      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="stroke-current shrink-0 w-6 h-6">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
      </svg>
      <span>This page tests DaisyUI components with your custom theme system.</span>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <!-- Buttons -->
      <div class="card bg-base-200 shadow-xl">
        <div class="card-body">
          <h2 class="card-title">Buttons</h2>
          <div class="space-y-4">
            <button class="btn btn-primary">Primary Button</button>
            <button class="btn btn-secondary">Secondary Button</button>
            <button class="btn btn-accent">Accent Button</button>
            <button class="btn btn-neutral">Neutral Button</button>
          </div>
        </div>
      </div>

      <!-- Form Controls -->
      <div class="card bg-base-200 shadow-xl">
        <div class="card-body">
          <h2 class="card-title">Form Controls</h2>
          <div class="form-control">
            <label class="label">
              <span class="label-text">Input field</span>
            </label>
            <input type="text" placeholder="Type here" class="input input-bordered" />
          </div>
          <div class="form-control">
            <label class="label">
              <span class="label-text">Select dropdown</span>
            </label>
            <select class="select select-bordered">
              <option disabled selected>Pick your favorite Simpson</option>
              <option>Homer</option>
              <option>Marge</option>
              <option>Bart</option>
              <option>Lisa</option>
            </select>
          </div>
        </div>
      </div>

      <!-- Colors and Status -->
      <div class="card bg-base-200 shadow-xl">
        <div class="card-body">
          <h2 class="card-title">Status Colors</h2>
          <div class="space-y-2">
            <div class="badge badge-info">Info</div>
            <div class="badge badge-success">Success</div>
            <div class="badge badge-warning">Warning</div>
            <div class="badge badge-error">Error</div>
          </div>
        </div>
      </div>

      <!-- Progress and Loading -->
      <div class="card bg-base-200 shadow-xl">
        <div class="card-body">
          <h2 class="card-title">Progress & Loading</h2>
          <progress class="progress progress-primary w-full" value="70" max="100"></progress>
          <div class="loading loading-spinner loading-lg text-primary"></div>
        </div>
      </div>
    </div>

    <!-- Theme Variables Test -->
    <div class="card bg-base-200 shadow-xl">
      <div class="card-body">
        <h2 class="card-title">Current Theme Variables</h2>
        <div class="overflow-x-auto">
          <table class="table table-zebra">
            <thead>
              <tr>
                <th>Variable</th>
                <th>Color Sample</th>
                <th>CSS Value</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>--primary</td>
                <td><div class="w-8 h-8 rounded" style="background: hsl(var(--primary));"></div></td>
                <td class="font-mono text-sm">hsl(var(--primary))</td>
              </tr>
              <tr>
                <td>--secondary</td>
                <td><div class="w-8 h-8 rounded" style="background: hsl(var(--secondary));"></div></td>
                <td class="font-mono text-sm">hsl(var(--secondary))</td>
              </tr>
              <tr>
                <td>--accent</td>
                <td><div class="w-8 h-8 rounded" style="background: hsl(var(--accent));"></div></td>
                <td class="font-mono text-sm">hsl(var(--accent))</td>
              </tr>
              <tr>
                <td>--background</td>
                <td><div class="w-8 h-8 rounded border" style="background: hsl(var(--background));"></div></td>
                <td class="font-mono text-sm">hsl(var(--background))</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <div class="text-center">
      <a href="/" class="btn btn-outline">← Back to Home</a>
    </div>
  </div>
</div>
