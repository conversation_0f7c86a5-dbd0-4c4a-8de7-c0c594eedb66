import { json } from '@sveltejs/kit'
import { env } from '$env/dynamic/private'
import type { RequestHandler } from './$types'
import { publishingRegistry } from '$lib/services/publishing-registry'
import type { OrchestratorResearchData, BatchPublishingRequest } from '$lib/types/publishing'

/**
 * Publishing API Endpoint
 * 
 * Handles publishing research results to configured destinations.
 * Supports both single and batch publishing operations.
 */

// POST /api/publishing/publish - Publish research results
export const POST: RequestHandler = async ({ request }) => {
  try {
    const body = await request.json()
    const { 
      researchData, 
      destinationIds, 
      destinations,
      options = {},
      parallel = true,
      preview = false 
    } = body
    
    // Validation
    if (!researchData) {
      return json({
        success: false,
        error: 'Research data is required'
      }, { status: 400 })
    }
    
    if (!destinationIds && !destinations) {
      return json({
        success: false,
        error: 'Either destinationIds or destinations array is required'
      }, { status: 400 })
    }
    
    // Handle single destination publishing
    if (destinationIds && Array.isArray(destinationIds) && destinationIds.length === 1) {
      try {
        const result = await publishingRegistry.publishToDestination(
          destinationIds[0],
          researchData,
          { ...options, preview }
        )
        
        return json({
          success: true,
          results: [result],
          totalDestinations: 1,
          successfulPublications: result.success ? 1 : 0,
          failedPublications: result.success ? 0 : 1,
          processingTime: result.deliveryTime
        })
        
      } catch (error) {
        console.error('Single destination publishing error:', error)
        return json({
          success: false,
          error: error instanceof Error ? error.message : 'Publishing failed',
          results: [],
          totalDestinations: 1,
          successfulPublications: 0,
          failedPublications: 1
        }, { status: 500 })
      }
    }
    
    // Handle batch publishing
    const startTime = Date.now()
    let targetDestinations: string[] = []
    
    if (destinationIds) {
      targetDestinations = destinationIds
    } else if (destinations) {
      // For custom destination configurations
      // TODO: Implement temporary destination registration
      targetDestinations = destinations.map((dest: any) => dest.id)
    }
    
    if (targetDestinations.length === 0) {
      return json({
        success: false,
        error: 'No valid destinations specified'
      }, { status: 400 })
    }
    
    // Publish to multiple destinations
    const results = await publishingRegistry.publishToMultiple(
      targetDestinations,
      researchData,
      { ...options, preview },
      parallel
    )
    
    const processingTime = Date.now() - startTime
    const successfulPublications = results.filter(r => r.success).length
    const failedPublications = results.filter(r => !r.success).length
    
    return json({
      success: successfulPublications > 0,
      results,
      totalDestinations: targetDestinations.length,
      successfulPublications,
      failedPublications,
      processingTime,
      parallel
    })
    
  } catch (error) {
    console.error('Publishing API error:', error)
    return json({
      success: false,
      error: 'Internal server error during publishing',
      results: [],
      totalDestinations: 0,
      successfulPublications: 0,
      failedPublications: 0
    }, { status: 500 })
  }
}

// POST /api/publishing/publish/test - Test destination configuration
export const PUT: RequestHandler = async ({ request }) => {
  try {
    const body = await request.json()
    const { destinationId, config } = body
    
    if (!destinationId && !config) {
      return json({
        success: false,
        error: 'Either destinationId or config is required'
      }, { status: 400 })
    }
    
    let testResult: boolean
    
    if (destinationId) {
      // Test existing destination
      testResult = await publishingRegistry.testDestination(destinationId)
    } else {
      // Test configuration without saving
      const { type, ...destConfig } = config
      
      if (!type) {
        return json({
          success: false,
          error: 'Destination type is required in config'
        }, { status: 400 })
      }
      
      // Validate configuration
      if (!publishingRegistry.validateDestinationConfig(type, destConfig)) {
        return json({
          success: false,
          error: `Invalid configuration for destination type '${type}'`,
          testPassed: false
        })
      }
      
      // Get publisher and test
      const publisher = publishingRegistry.getPublisher(type)
      if (!publisher) {
        return json({
          success: false,
          error: `No publisher available for type '${type}'`,
          testPassed: false
        })
      }
      
      testResult = await publisher.test(destConfig)
    }
    
    return json({
      success: true,
      testPassed: testResult,
      message: testResult ? 'Destination test passed' : 'Destination test failed'
    })
    
  } catch (error) {
    console.error('Destination test error:', error)
    return json({
      success: false,
      testPassed: false,
      error: error instanceof Error ? error.message : 'Test failed'
    }, { status: 500 })
  }
}

// POST /api/publishing/publish/preview - Preview formatted message
export const PATCH: RequestHandler = async ({ request }) => {
  try {
    const body = await request.json()
    const { researchData, destinationType, options = {} } = body
    
    if (!researchData) {
      return json({
        success: false,
        error: 'Research data is required'
      }, { status: 400 })
    }
    
    if (!destinationType) {
      return json({
        success: false,
        error: 'Destination type is required'
      }, { status: 400 })
    }
    
    // Get formatter for the destination type
    const formatter = publishingRegistry.getFormatter(destinationType)
    if (!formatter) {
      return json({
        success: false,
        error: `No formatter available for type '${destinationType}'`
      }, { status: 400 })
    }
    
    // Generate preview
    const preview = await formatter.preview(researchData, options)
    
    return json({
      success: true,
      preview,
      destinationType,
      options
    })
    
  } catch (error) {
    console.error('Preview generation error:', error)
    return json({
      success: false,
      error: error instanceof Error ? error.message : 'Preview generation failed'
    }, { status: 500 })
  }
}
