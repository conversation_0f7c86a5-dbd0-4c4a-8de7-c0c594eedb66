import { json } from '@sveltejs/kit'
import { env } from '$env/dynamic/private'
import type { RequestHandler } from './$types'
import { publishingRegistry, createDestination } from '$lib/services/publishing-registry'
import type { PublishingDestination } from '$lib/types/publishing'

/**
 * Publishing Destinations API Endpoint
 * 
 * Handles CRUD operations for publishing destinations configuration.
 * Follows the established API patterns from the existing codebase.
 */

// GET /api/publishing/destinations - List all destinations
export const GET: RequestHandler = async ({ url }) => {
  try {
    const type = url.searchParams.get('type')
    const enabled = url.searchParams.get('enabled')

    // Load destinations from database
    let destinations = await publishingRegistry.loadDestinationsFromDatabase()

    // Filter by type if specified
    if (type) {
      destinations = destinations.filter(dest => dest.type === type)
    }

    // Filter by enabled status if specified
    if (enabled !== null) {
      const isEnabled = enabled === 'true'
      destinations = destinations.filter(dest => dest.enabled === isEnabled)
    }

    return json({
      success: true,
      destinations,
      total: destinations.length,
      supportedTypes: publishingRegistry.getSupportedTypes()
    })

  } catch (error) {
    console.error('Error fetching publishing destinations:', error)
    return json({
      success: false,
      error: 'Failed to fetch publishing destinations',
      destinations: [],
      total: 0
    }, { status: 500 })
  }
}

// POST /api/publishing/destinations - Create new destination
export const POST: RequestHandler = async ({ request }) => {
  try {
    const body = await request.json()
    const { type, name, config, description, enabled = true } = body

    // Validation
    if (!type || !name || !config) {
      return json({
        success: false,
        error: 'Missing required fields: type, name, config'
      }, { status: 400 })
    }

    // Validate configuration for the destination type
    if (!publishingRegistry.validateDestinationConfig(type, config)) {
      return json({
        success: false,
        error: `Invalid configuration for destination type '${type}'`
      }, { status: 400 })
    }

    // Save destination to database
    const destination = await publishingRegistry.saveDestinationToDatabase({
      type,
      name,
      description,
      enabled,
      config
    })

    if (!destination) {
      return json({
        success: false,
        error: 'Failed to save destination to database'
      }, { status: 500 })
    }

    return json({
      success: true,
      destination,
      message: `Destination '${name}' created successfully`
    }, { status: 201 })

  } catch (error) {
    console.error('Error creating publishing destination:', error)
    return json({
      success: false,
      error: 'Failed to create publishing destination'
    }, { status: 500 })
  }
}

// PUT /api/publishing/destinations - Update existing destination
export const PUT: RequestHandler = async ({ request }) => {
  try {
    const body = await request.json()
    const { id, ...updates } = body
    
    if (!id) {
      return json({
        success: false,
        error: 'Destination ID is required'
      }, { status: 400 })
    }
    
    // Load destinations from database first
    await publishingRegistry.loadDestinationsFromDatabase()

    // Check if destination exists
    const existingDestination = publishingRegistry.getDestination(id)
    if (!existingDestination) {
      return json({
        success: false,
        error: `Destination with ID '${id}' not found`
      }, { status: 404 })
    }

    // Validate configuration if it's being updated
    if (updates.config) {
      const type = updates.type || existingDestination.type
      if (!publishingRegistry.validateDestinationConfig(type, updates.config)) {
        return json({
          success: false,
          error: `Invalid configuration for destination type '${type}'`
        }, { status: 400 })
      }
    }

    // Update the destination in database
    const updatedDestination = await publishingRegistry.updateDestinationInDatabase(id, updates)

    if (!updatedDestination) {
      return json({
        success: false,
        error: 'Failed to update destination'
      }, { status: 500 })
    }

    return json({
      success: true,
      destination: updatedDestination,
      message: `Destination '${updatedDestination.name}' updated successfully`
    })
    
  } catch (error) {
    console.error('Error updating publishing destination:', error)
    return json({
      success: false,
      error: 'Failed to update publishing destination'
    }, { status: 500 })
  }
}

// DELETE /api/publishing/destinations - Delete destination
export const DELETE: RequestHandler = async ({ request }) => {
  try {
    const body = await request.json()
    const { id } = body
    
    if (!id) {
      return json({
        success: false,
        error: 'Destination ID is required'
      }, { status: 400 })
    }
    
    // Check if destination exists
    const existingDestination = publishingRegistry.getDestination(id)
    if (!existingDestination) {
      return json({
        success: false,
        error: `Destination with ID '${id}' not found`
      }, { status: 404 })
    }
    
    // Remove the destination
    const success = publishingRegistry.removeDestination(id)
    
    if (!success) {
      return json({
        success: false,
        error: 'Failed to delete destination'
      }, { status: 500 })
    }
    
    return json({
      success: true,
      message: `Destination '${id}' deleted successfully`
    })
    
  } catch (error) {
    console.error('Error deleting publishing destination:', error)
    return json({
      success: false,
      error: 'Failed to delete publishing destination'
    }, { status: 500 })
  }
}
