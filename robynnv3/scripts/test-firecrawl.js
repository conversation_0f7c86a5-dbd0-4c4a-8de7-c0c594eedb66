#!/usr/bin/env node

/**
 * Firecrawl Tools Test Script
 * 
 * This script helps test the Firecrawl tools independently
 * and validates the integration before using in the main agent.
 */

import { readFileSync, existsSync } from 'fs'
import { join } from 'path'

const CONFIG_FILE = '.env'
const REQUIRED_ENV_VARS = ['FIRECRAWL_API_KEY']

function checkEnvironment() {
  console.log('🔍 Checking environment configuration...\n')
  
  if (!existsSync(CONFIG_FILE)) {
    console.error('❌ .env file not found!')
    console.log('📝 Please create a .env file with the following variables:')
    REQUIRED_ENV_VARS.forEach(varName => {
      console.log(`   ${varName}=your_value_here`)
    })
    process.exit(1)
  }

  const envContent = readFileSync(CONFIG_FILE, 'utf8')
  const missingVars = []

  REQUIRED_ENV_VARS.forEach(varName => {
    if (!envContent.includes(varName) || envContent.includes(`${varName}=`)) {
      const line = envContent.split('\n').find(line => line.startsWith(varName))
      if (!line || line.split('=')[1]?.trim() === '') {
        missingVars.push(varName)
      }
    }
  })

  if (missingVars.length > 0) {
    console.error('❌ Missing or empty environment variables:')
    missingVars.forEach(varName => {
      console.log(`   ${varName}`)
    })
    console.log('\n📝 Please add these to your .env file')
    process.exit(1)
  }

  console.log('✅ Environment configuration looks good!')
}

function displayTestInstructions() {
  console.log('\n🧪 Firecrawl Tools Testing Instructions')
  console.log('=====================================\n')
  
  console.log('1. Start the development server:')
  console.log('   pnpm run dev\n')
  
  console.log('2. Navigate to the test page:')
  console.log('   http://localhost:5173/dashboard/{your-env-slug}/firecrawl-test\n')
  
  console.log('3. Test each tool with these recommended inputs:\n')
  
  console.log('   🔍 Scrape Tool:')
  console.log('   - URL: https://stripe.com')
  console.log('   - Expected: Company info, value props, features\n')
  
  console.log('   📊 Extract Tool:')
  console.log('   - URLs: https://stripe.com')
  console.log('           https://stripe.com/pricing')
  console.log('           https://stripe.com/about')
  console.log('   - Expected: Structured data across pages\n')
  
  console.log('   🌐 Search Tool:')
  console.log('   - Query: Stripe payment processing')
  console.log('   - Expected: Relevant search results with content\n')
  
  console.log('4. Validate the enhanced display components:')
  console.log('   - WebPresenceCard should show landing page analysis')
  console.log('   - TechnicalAnalysisCard should show tech stack info\n')
  
  console.log('5. Check error handling:')
  console.log('   - Try invalid URLs')
  console.log('   - Test with empty inputs')
  console.log('   - Verify timeout handling\n')
  
  console.log('📖 For detailed testing instructions, see:')
  console.log('   FIRECRAWL_TESTING_GUIDE.md\n')
}

function displayApiEndpoints() {
  console.log('🔗 API Test Endpoints')
  console.log('====================\n')
  
  console.log('Direct API testing (using curl or Postman):')
  console.log('- POST /api/test-firecrawl-scrape')
  console.log('- POST /api/test-firecrawl-extract') 
  console.log('- POST /api/test-firecrawl-search\n')
  
  console.log('Example curl command for scrape tool:')
  console.log('curl -X POST http://localhost:5173/api/test-firecrawl-scrape \\')
  console.log('  -H "Content-Type: application/json" \\')
  console.log('  -d \'{"url": "https://stripe.com", "formats": ["markdown"]}\'\n')
}

function displayTroubleshooting() {
  console.log('🔧 Common Issues & Solutions')
  console.log('===========================\n')
  
  console.log('❌ Authentication Failed:')
  console.log('   - Check FIRECRAWL_API_KEY format (should start with "fc-")')
  console.log('   - Verify API key is active in your Firecrawl account')
  console.log('   - Check account credits/usage limits\n')
  
  console.log('❌ Timeout Errors:')
  console.log('   - Try simpler websites first (avoid heavy JS sites)')
  console.log('   - Check network connectivity')
  console.log('   - Increase timeout values in tool configuration\n')
  
  console.log('❌ Build Errors:')
  console.log('   - Run: pnpm run build')
  console.log('   - Check for TypeScript errors')
  console.log('   - Verify all imports are correct\n')
  
  console.log('❌ Display Issues:')
  console.log('   - Check browser console for errors')
  console.log('   - Verify component imports')
  console.log('   - Check CSS classes are loading\n')
}

function main() {
  const args = process.argv.slice(2)
  
  if (args.includes('--help') || args.includes('-h')) {
    console.log('Firecrawl Tools Test Script')
    console.log('Usage: node scripts/test-firecrawl.js [options]')
    console.log('')
    console.log('Options:')
    console.log('  --help, -h          Show this help message')
    console.log('  --endpoints         Show API endpoints for direct testing')
    console.log('  --troubleshoot      Show troubleshooting guide')
    console.log('')
    return
  }
  
  if (args.includes('--endpoints')) {
    displayApiEndpoints()
    return
  }
  
  if (args.includes('--troubleshoot')) {
    displayTroubleshooting()
    return
  }
  
  // Default: run environment check and show instructions
  checkEnvironment()
  displayTestInstructions()
}

main()
