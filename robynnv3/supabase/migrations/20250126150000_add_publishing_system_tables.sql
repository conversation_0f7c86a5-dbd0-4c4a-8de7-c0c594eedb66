-- Publishing System Tables Migration
-- This migration creates the necessary tables for the publishing system
-- to store publishing destinations and analytics

-- Create publishing_destinations table
CREATE TABLE IF NOT EXISTS publishing_destinations (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    type text NOT NULL CHECK (type IN ('slack', 'webhook', 'hubspot', 'salesforce')),
    name text NOT NULL,
    description text,
    enabled boolean NOT NULL DEFAULT true,
    config jsonb NOT NULL DEFAULT '{}',
    created_at timestamp with time zone NOT NULL DEFAULT now(),
    updated_at timestamp with time zone NOT NULL DEFAULT now(),
    
    -- Add constraints
    CONSTRAINT publishing_destinations_name_unique UNIQUE (name),
    CONSTRAINT publishing_destinations_config_not_empty CHECK (config IS NOT NULL)
);

-- Create publishing_analytics table
CREATE TABLE IF NOT EXISTS publishing_analytics (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    destination_id uuid NOT NULL REFERENCES publishing_destinations(id) ON DELETE CASCADE,
    research_data_hash text NOT NULL, -- Hash of the research data for deduplication
    status text NOT NULL CHECK (status IN ('success', 'failed', 'pending')),
    message_count integer NOT NULL DEFAULT 1,
    response_data jsonb,
    error_message text,
    published_at timestamp with time zone NOT NULL DEFAULT now(),
    
    -- Add indexes for performance
    CONSTRAINT publishing_analytics_status_valid CHECK (status IN ('success', 'failed', 'pending'))
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_publishing_destinations_type ON publishing_destinations(type);
CREATE INDEX IF NOT EXISTS idx_publishing_destinations_enabled ON publishing_destinations(enabled);
CREATE INDEX IF NOT EXISTS idx_publishing_analytics_destination_id ON publishing_analytics(destination_id);
CREATE INDEX IF NOT EXISTS idx_publishing_analytics_status ON publishing_analytics(status);
CREATE INDEX IF NOT EXISTS idx_publishing_analytics_published_at ON publishing_analytics(published_at);
CREATE INDEX IF NOT EXISTS idx_publishing_analytics_research_hash ON publishing_analytics(research_data_hash);

-- Add trigger to automatically update updated_at column for publishing_destinations
CREATE TRIGGER update_publishing_destinations_updated_at 
    BEFORE UPDATE ON publishing_destinations 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Insert default Slack destination (disabled by default)
INSERT INTO publishing_destinations (type, name, description, enabled, config)
VALUES (
    'slack',
    'Default Slack Workspace',
    'Default Slack webhook destination for publishing research results',
    false,
    '{
        "webhook_url": "",
        "channel": "#research",
        "username": "Research Bot",
        "icon_emoji": ":mag:",
        "message_style": "detailed"
    }'::jsonb
) ON CONFLICT (name) DO NOTHING;

-- Add RLS (Row Level Security) policies
ALTER TABLE publishing_destinations ENABLE ROW LEVEL SECURITY;
ALTER TABLE publishing_analytics ENABLE ROW LEVEL SECURITY;

-- Create policies for publishing_destinations
CREATE POLICY "Users can view publishing destinations" ON publishing_destinations
    FOR SELECT USING (true);

CREATE POLICY "Authenticated users can manage publishing destinations" ON publishing_destinations
    FOR ALL USING (auth.role() = 'authenticated');

-- Create policies for publishing_analytics
CREATE POLICY "Users can view publishing analytics" ON publishing_analytics
    FOR SELECT USING (true);

CREATE POLICY "Authenticated users can insert publishing analytics" ON publishing_analytics
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

-- Grant necessary permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON publishing_destinations TO authenticated;
GRANT SELECT, INSERT ON publishing_analytics TO authenticated;

-- Add comments for documentation
COMMENT ON TABLE publishing_destinations IS 'Stores configuration for various publishing destinations (Slack, webhooks, CRMs)';
COMMENT ON TABLE publishing_analytics IS 'Tracks publishing attempts and their results for analytics and debugging';

COMMENT ON COLUMN publishing_destinations.type IS 'Type of destination: slack, webhook, hubspot, salesforce';
COMMENT ON COLUMN publishing_destinations.config IS 'JSON configuration specific to the destination type';
COMMENT ON COLUMN publishing_analytics.research_data_hash IS 'SHA-256 hash of research data to prevent duplicate publishing';
COMMENT ON COLUMN publishing_analytics.response_data IS 'Response data from the publishing destination API';
