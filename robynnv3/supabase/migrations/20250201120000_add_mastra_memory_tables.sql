-- Mastra Memory Tables for PMM Agent
-- Creates conversation storage compatible with @mastra/pg

-- Create conversations table first (without foreign key constraints)
create table "public"."mastra_conversations" (
    "id" uuid not null default gen_random_uuid(),
    "user_id" uuid,
    "agent_name" text not null,
    "resource_id" text,
    "thread_id" text,
    "metadata" jsonb default '{}'::jsonb,
    "created_at" timestamp with time zone not null default now(),
    "updated_at" timestamp with time zone not null default now()
);

-- Add primary key to conversations table
ALTER TABLE "public"."mastra_conversations" ADD CONSTRAINT "mastra_conversations_pkey" PRIMARY KEY ("id");

-- Now create messages table with foreign key reference
create table "public"."mastra_messages" (
    "id" bigserial not null,
    "conversation_id" uuid references mastra_conversations(id) on delete cascade not null,
    "role" text not null check (role in ('user','assistant','system','tool')),
    "content" text,
    "metadata" jsonb default '{}'::jsonb,
    "created_at" timestamp with time zone not null default now()
);

-- Add primary key to messages table
ALTER TABLE "public"."mastra_messages" ADD CONSTRAINT "mastra_messages_pkey" PRIMARY KEY ("id");

-- Add foreign key constraint for user_id after tables are created
ALTER TABLE "public"."mastra_conversations" 
ADD CONSTRAINT "mastra_conversations_user_id_fkey" 
FOREIGN KEY ("user_id") REFERENCES auth.users(id) ON DELETE CASCADE;

-- Indexes for performance
CREATE INDEX mastra_conversations_user_id_idx ON public.mastra_conversations USING btree (user_id);
CREATE INDEX mastra_conversations_agent_name_idx ON public.mastra_conversations USING btree (agent_name);
CREATE INDEX mastra_conversations_resource_id_idx ON public.mastra_conversations USING btree (resource_id);
CREATE INDEX mastra_messages_conversation_id_idx ON public.mastra_messages USING btree (conversation_id);
CREATE INDEX mastra_messages_created_at_idx ON public.mastra_messages USING btree (created_at);

-- Enable Row Level Security
alter table "public"."mastra_conversations" enable row level security;
alter table "public"."mastra_messages" enable row level security;

-- RLS Policies for mastra_conversations
create policy "Users can view their own conversations" on "public"."mastra_conversations"
    as permissive for select to authenticated using (auth.uid() = user_id);

create policy "Users can insert their own conversations" on "public"."mastra_conversations"
    as permissive for insert to authenticated with check (auth.uid() = user_id);

create policy "Users can update their own conversations" on "public"."mastra_conversations"
    as permissive for update to authenticated using (auth.uid() = user_id) with check (auth.uid() = user_id);

create policy "Users can delete their own conversations" on "public"."mastra_conversations"
    as permissive for delete to authenticated using (auth.uid() = user_id);

-- RLS Policies for mastra_messages
create policy "Users can view messages from their conversations" on "public"."mastra_messages"
    as permissive for select to authenticated using (
        exists (
            select 1 from public.mastra_conversations c
            where c.id = mastra_messages.conversation_id 
            and c.user_id = auth.uid()
        )
    );

create policy "Users can insert messages to their conversations" on "public"."mastra_messages"
    as permissive for insert to authenticated with check (
        exists (
            select 1 from public.mastra_conversations c
            where c.id = mastra_messages.conversation_id 
            and c.user_id = auth.uid()
        )
    );

create policy "Users can update messages in their conversations" on "public"."mastra_messages"
    as permissive for update to authenticated using (
        exists (
            select 1 from public.mastra_conversations c
            where c.id = mastra_messages.conversation_id 
            and c.user_id = auth.uid()
        )
    ) with check (
        exists (
            select 1 from public.mastra_conversations c
            where c.id = mastra_messages.conversation_id 
            and c.user_id = auth.uid()
        )
    );

create policy "Users can delete messages from their conversations" on "public"."mastra_messages"
    as permissive for delete to authenticated using (
        exists (
            select 1 from public.mastra_conversations c
            where c.id = mastra_messages.conversation_id 
            and c.user_id = auth.uid()
        )
    );

-- Updated_at trigger for conversations
CREATE TRIGGER update_mastra_conversations_updated_at 
    BEFORE UPDATE ON mastra_conversations 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Grant permissions for authenticated users
grant delete on table "public"."mastra_conversations" to "authenticated";
grant insert on table "public"."mastra_conversations" to "authenticated";
grant references on table "public"."mastra_conversations" to "authenticated";
grant select on table "public"."mastra_conversations" to "authenticated";
grant trigger on table "public"."mastra_conversations" to "authenticated";
grant update on table "public"."mastra_conversations" to "authenticated";

grant delete on table "public"."mastra_messages" to "authenticated";
grant insert on table "public"."mastra_messages" to "authenticated";
grant references on table "public"."mastra_messages" to "authenticated";
grant select on table "public"."mastra_messages" to "authenticated";
grant trigger on table "public"."mastra_messages" to "authenticated";
grant update on table "public"."mastra_messages" to "authenticated";

-- Service role permissions
grant all on table "public"."mastra_conversations" to "service_role";
grant all on table "public"."mastra_messages" to "service_role";