-- Create <PERSON>stra messages table for conversation memory
-- This table stores conversation messages for AI agents

CREATE TABLE IF NOT EXISTS mastra_messages (
    id TEXT PRIMARY KEY,
    thread_id TEXT NOT NULL,
    content TEXT NOT NULL,
    role TEXT NOT NULL,
    type TEXT NOT NULL,
    "createdAt" TIMESTAMPTZ DEFAULT NOW(),
    "createdAtZ" TEXT,
    "resourceId" TEXT
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_mastra_messages_thread_id ON mastra_messages(thread_id);
CREATE INDEX IF NOT EXISTS idx_mastra_messages_created_at ON mastra_messages("createdAt");
CREATE INDEX IF NOT EXISTS idx_mastra_messages_resource_id ON mastra_messages("resourceId");

-- Add comments
COMMENT ON TABLE mastra_messages IS 'Stores conversation messages for Mastra AI agents';
COMMENT ON COLUMN mastra_messages.id IS 'Unique message identifier';
COMMENT ON COLUMN mastra_messages.thread_id IS 'Thread/conversation identifier';
COMMENT ON COLUMN mastra_messages.content IS 'Message content/text';
COMMENT ON COLUMN mastra_messages.role IS 'Message role (user, assistant, system, etc.)';
COMMENT ON COLUMN mastra_messages.type IS 'Message type';
COMMENT ON COLUMN mastra_messages."createdAt" IS 'Timestamp when message was created';
COMMENT ON COLUMN mastra_messages."createdAtZ" IS 'Timezone-aware creation timestamp as string';
COMMENT ON COLUMN mastra_messages."resourceId" IS 'Optional resource identifier';
