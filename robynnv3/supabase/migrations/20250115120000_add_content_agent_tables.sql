-- Content Agent Tables Migration
-- Creates tables for content documents, sessions, and citations
-- Content documents table
create table "public"."content_documents" (
    "id" uuid not null default gen_random_uuid(),
    "user_id" uuid not null,
    "environment_id" uuid not null,
    "title" text not null,
    "content" jsonb not null default '{}',
    "content_type" text not null default 'document',
    "status" text not null default 'draft',
    "metadata" jsonb not null default '{}',
    "created_at" timestamp with time zone not null default now(),
    "updated_at" timestamp with time zone not null default now()
);
-- Content sessions table (for agent interactions)
create table "public"."content_sessions" (
    "id" uuid not null default gen_random_uuid(),
    "document_id" uuid not null,
    "user_id" uuid not null,
    "session_data" jsonb not null default '{}',
    "created_at" timestamp with time zone not null default now(),
    "updated_at" timestamp with time zone not null default now()
);
-- Citations table
create table "public"."content_citations" (
    "id" uuid not null default gen_random_uuid(),
    "document_id" uuid not null,
    "citation_data" jsonb not null default '{}',
    "citation_style" text not null default 'APA',
    "position_in_content" integer,
    "created_at" timestamp with time zone not null default now()
);
-- Enable Row Level Security
alter table "public"."content_documents" enable row level security;
alter table "public"."content_sessions" enable row level security;
alter table "public"."content_citations" enable row level security;
-- Create indexes for better performance
CREATE INDEX content_documents_user_id_idx ON public.content_documents USING btree (user_id);
CREATE INDEX content_documents_environment_id_idx ON public.content_documents USING btree (environment_id);
CREATE INDEX content_documents_status_idx ON public.content_documents USING btree (status);
CREATE INDEX content_documents_content_type_idx ON public.content_documents USING btree (content_type);
CREATE INDEX content_documents_created_at_idx ON public.content_documents USING btree (created_at);
CREATE INDEX content_sessions_document_id_idx ON public.content_sessions USING btree (document_id);
CREATE INDEX content_sessions_user_id_idx ON public.content_sessions USING btree (user_id);
CREATE INDEX content_citations_document_id_idx ON public.content_citations USING btree (document_id);
CREATE INDEX content_citations_citation_style_idx ON public.content_citations USING btree (citation_style);
-- Create primary keys
CREATE UNIQUE INDEX content_documents_pkey ON public.content_documents USING btree (id);
CREATE UNIQUE INDEX content_sessions_pkey ON public.content_sessions USING btree (id);
CREATE UNIQUE INDEX content_citations_pkey ON public.content_citations USING btree (id);
alter table "public"."content_documents"
add constraint "content_documents_pkey" PRIMARY KEY using index "content_documents_pkey";
alter table "public"."content_sessions"
add constraint "content_sessions_pkey" PRIMARY KEY using index "content_sessions_pkey";
alter table "public"."content_citations"
add constraint "content_citations_pkey" PRIMARY KEY using index "content_citations_pkey";
-- Add foreign key constraints
alter table "public"."content_documents"
add constraint "content_documents_user_id_fkey" FOREIGN KEY (user_id) REFERENCES auth.users(id) ON UPDATE CASCADE ON DELETE CASCADE not valid;
alter table "public"."content_documents" validate constraint "content_documents_user_id_fkey";
alter table "public"."content_documents"
add constraint "content_documents_environment_id_fkey" FOREIGN KEY (environment_id) REFERENCES environments(id) ON UPDATE CASCADE ON DELETE CASCADE not valid;
alter table "public"."content_documents" validate constraint "content_documents_environment_id_fkey";
alter table "public"."content_sessions"
add constraint "content_sessions_document_id_fkey" FOREIGN KEY (document_id) REFERENCES content_documents(id) ON UPDATE CASCADE ON DELETE CASCADE not valid;
alter table "public"."content_sessions" validate constraint "content_sessions_document_id_fkey";
alter table "public"."content_sessions"
add constraint "content_sessions_user_id_fkey" FOREIGN KEY (user_id) REFERENCES auth.users(id) ON UPDATE CASCADE ON DELETE CASCADE not valid;
alter table "public"."content_sessions" validate constraint "content_sessions_user_id_fkey";
alter table "public"."content_citations"
add constraint "content_citations_document_id_fkey" FOREIGN KEY (document_id) REFERENCES content_documents(id) ON UPDATE CASCADE ON DELETE CASCADE not valid;
alter table "public"."content_citations" validate constraint "content_citations_document_id_fkey";
-- Add check constraints
alter table "public"."content_documents"
add constraint "content_documents_status_check" CHECK (status IN ('draft', 'published', 'archived'));
alter table "public"."content_documents"
add constraint "content_documents_content_type_check" CHECK (
        content_type IN (
            'article',
            'blog-post',
            'whitepaper',
            'document',
            'email',
            'social-media',
            'presentation',
            'report',
            'essay',
            'tutorial',
            'guide',
            'proposal'
        )
    );
alter table "public"."content_citations"
add constraint "content_citations_style_check" CHECK (
        citation_style IN (
            'APA',
            'MLA',
            'Chicago',
            'Harvard',
            'IEEE',
            'Vancouver',
            'AMA'
        )
    );
-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column() RETURNS TRIGGER AS $$ BEGIN NEW.updated_at = NOW();
RETURN NEW;
END;
$$ language 'plpgsql';
-- Add updated_at triggers
CREATE TRIGGER update_content_documents_updated_at BEFORE
UPDATE ON content_documents FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_content_sessions_updated_at BEFORE
UPDATE ON content_sessions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
-- Grant permissions for content_documents
grant delete on table "public"."content_documents" to "authenticated";
grant insert on table "public"."content_documents" to "authenticated";
grant references on table "public"."content_documents" to "authenticated";
grant select on table "public"."content_documents" to "authenticated";
grant trigger on table "public"."content_documents" to "authenticated";
grant truncate on table "public"."content_documents" to "authenticated";
grant update on table "public"."content_documents" to "authenticated";
grant delete on table "public"."content_documents" to "service_role";
grant insert on table "public"."content_documents" to "service_role";
grant references on table "public"."content_documents" to "service_role";
grant select on table "public"."content_documents" to "service_role";
grant trigger on table "public"."content_documents" to "service_role";
grant truncate on table "public"."content_documents" to "service_role";
grant update on table "public"."content_documents" to "service_role";
-- Grant permissions for content_sessions
grant delete on table "public"."content_sessions" to "authenticated";
grant insert on table "public"."content_sessions" to "authenticated";
grant references on table "public"."content_sessions" to "authenticated";
grant select on table "public"."content_sessions" to "authenticated";
grant trigger on table "public"."content_sessions" to "authenticated";
grant truncate on table "public"."content_sessions" to "authenticated";
grant update on table "public"."content_sessions" to "authenticated";
grant delete on table "public"."content_sessions" to "service_role";
grant insert on table "public"."content_sessions" to "service_role";
grant references on table "public"."content_sessions" to "service_role";
grant select on table "public"."content_sessions" to "service_role";
grant trigger on table "public"."content_sessions" to "service_role";
grant truncate on table "public"."content_sessions" to "service_role";
grant update on table "public"."content_sessions" to "service_role";
-- Grant permissions for content_citations
grant delete on table "public"."content_citations" to "authenticated";
grant insert on table "public"."content_citations" to "authenticated";
grant references on table "public"."content_citations" to "authenticated";
grant select on table "public"."content_citations" to "authenticated";
grant trigger on table "public"."content_citations" to "authenticated";
grant truncate on table "public"."content_citations" to "authenticated";
grant update on table "public"."content_citations" to "authenticated";
grant delete on table "public"."content_citations" to "service_role";
grant insert on table "public"."content_citations" to "service_role";
grant references on table "public"."content_citations" to "service_role";
grant select on table "public"."content_citations" to "service_role";
grant trigger on table "public"."content_citations" to "service_role";
grant truncate on table "public"."content_citations" to "service_role";
grant update on table "public"."content_citations" to "service_role";
-- Row Level Security Policies
-- Content Documents Policies
create policy "Users can view their own documents" on "public"."content_documents" as permissive for
select to authenticated using (auth.uid() = user_id);
create policy "Users can insert their own documents" on "public"."content_documents" as permissive for
insert to authenticated with check (auth.uid() = user_id);
create policy "Users can update their own documents" on "public"."content_documents" as permissive for
update to authenticated using (auth.uid() = user_id) with check (auth.uid() = user_id);
create policy "Users can delete their own documents" on "public"."content_documents" as permissive for delete to authenticated using (auth.uid() = user_id);
-- Content Sessions Policies
create policy "Users can view their own sessions" on "public"."content_sessions" as permissive for
select to authenticated using (auth.uid() = user_id);
create policy "Users can insert their own sessions" on "public"."content_sessions" as permissive for
insert to authenticated with check (auth.uid() = user_id);
create policy "Users can update their own sessions" on "public"."content_sessions" as permissive for
update to authenticated using (auth.uid() = user_id) with check (auth.uid() = user_id);
create policy "Users can delete their own sessions" on "public"."content_sessions" as permissive for delete to authenticated using (auth.uid() = user_id);
-- Content Citations Policies
create policy "Users can view citations for their documents" on "public"."content_citations" as permissive for
select to authenticated using (
        EXISTS (
            SELECT 1
            FROM content_documents
            WHERE content_documents.id = content_citations.document_id
                AND content_documents.user_id = auth.uid()
        )
    );
create policy "Users can insert citations for their documents" on "public"."content_citations" as permissive for
insert to authenticated with check (
        EXISTS (
            SELECT 1
            FROM content_documents
            WHERE content_documents.id = content_citations.document_id
                AND content_documents.user_id = auth.uid()
        )
    );
create policy "Users can update citations for their documents" on "public"."content_citations" as permissive for
update to authenticated using (
        EXISTS (
            SELECT 1
            FROM content_documents
            WHERE content_documents.id = content_citations.document_id
                AND content_documents.user_id = auth.uid()
        )
    ) with check (
        EXISTS (
            SELECT 1
            FROM content_documents
            WHERE content_documents.id = content_citations.document_id
                AND content_documents.user_id = auth.uid()
        )
    );
create policy "Users can delete citations for their documents" on "public"."content_citations" as permissive for delete to authenticated using (
    EXISTS (
        SELECT 1
        FROM content_documents
        WHERE content_documents.id = content_citations.document_id
            AND content_documents.user_id = auth.uid()
    )
);