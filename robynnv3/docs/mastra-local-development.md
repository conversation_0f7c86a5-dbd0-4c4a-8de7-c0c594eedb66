# Mastra Local Development Guide

This guide documents the Mastra Studio local development environment setup for the ROBYNNV3 project, including the automated synchronization system for agents and tools.

## Overview

The Mastra local development system allows you to develop and test AI agents and tools in Mastra Studio without affecting the main SvelteKit application. It automatically transforms TypeScript SvelteKit tools into pure JavaScript Mastra-compatible code.

## Architecture

### Directory Structure

```
src/
├── lib/agents/                   # SvelteKit Source (TypeScript)
│   ├── tools/                   # Original tool definitions
│   ├── content-agent.ts         # Agent definitions
│   ├── orchestrator-agent.ts    
│   └── llm-providers.ts         # LLM configuration
├── mastra/                      # Mastra Studio Target (JavaScript)
│   ├── tools/                   # Transformed tools
│   │   ├── index.js            # Auto-generated exports
│   │   └── *.js                # Transformed tool files
│   ├── agents/                 # Transformed agents
│   │   ├── index.js            # Auto-generated exports
│   │   └── *.js                # Transformed agent files
│   ├── shared/                 # Shared utilities
│   │   ├── env.js              # Environment variables
│   │   ├── schemas.js          # Zod schemas
│   │   └── llm-providers.js    # LLM configuration
│   ├── index.ts                # Main Mastra entry point
│   └── registry.ts             # Tool registry
└── scripts/
    ├── sync-mastra.js          # Main sync script
    ├── update-registry.js      # Registry updater
    └── validate-sync.js        # Validation script
```

### Key Components

1. **Sync Script** (`src/scripts/sync-mastra.js`): Transforms TypeScript SvelteKit code to JavaScript Mastra code
2. **Environment Abstraction** (`src/mastra/shared/env.js`): Provides environment variables for Mastra
3. **Schema Sharing** (`src/mastra/shared/schemas.js`): Shared Zod validation schemas
4. **LLM Provider Configuration** (`src/mastra/shared/llm-providers.js`): Transformed LLM setup

## Features

### ✅ Implemented Features

- **Automatic TypeScript to JavaScript Transformation**: Converts SvelteKit TypeScript tools and agents to pure JavaScript
- **Environment Variable Abstraction**: SvelteKit `$env/dynamic/private` → Mastra `ENV_VARS` wrapper
- **Import Path Resolution**: Automatic conversion of SvelteKit `$lib` imports to relative paths
- **Type Annotation Removal**: Strips TypeScript types while preserving functionality
- **Schema Synchronization**: Shares Zod schemas between SvelteKit and Mastra
- **Agent Integration**: Full agent support with instruction handling
- **Tool Registry Management**: Automatic tool discovery and registration
- **Build Validation**: Ensures transformations don't break either environment

### 🔧 Transformation Patterns

The sync script handles:
- Function parameter type annotations
- Return type annotations
- Generic type parameters
- Interface and type definitions
- TypeScript 'as' assertions
- Optional parameter markers
- Complex union types
- Import path conversions
- Environment variable access patterns

## Commands

### Core Commands

```bash
# Transform SvelteKit tools/agents to Mastra format
pnpm run sync:mastra

# Update tool registry
pnpm run sync:registry

# Start Mastra Studio development server
pnpm run mastra:dev

# Validate transformations
pnpm run sync:validate

# Run complete sync workflow
pnpm run sync:all
```

### Development Workflow

```bash
# 1. Make changes to SvelteKit tools/agents
# 2. Sync to Mastra format
pnpm run sync:mastra

# 3. Update registry
pnpm run sync:registry

# 4. Start Mastra Studio
pnpm run mastra:dev

# 5. Test in Studio at http://localhost:4000
```

## Environment Setup

### Required Environment Variables

Add these to your `.env.local` file:

```bash
# LLM Providers
OPENAI_API_KEY=your_openai_key
ANTHROPIC_API_KEY=your_anthropic_key
GOOGLE_GENERATIVE_AI_API_KEY=your_google_key
DEEPSEEK_API_KEY=your_deepseek_key

# Tool APIs
EXA_API_KEY=your_exa_key
BRAVE_SEARCH_API_KEY=your_brave_key
APOLLO_API_KEY=your_apollo_key
FIRECRAWL_API_KEY=your_firecrawl_key

# Mastra Configuration
LLM_PROVIDER=openai
LLM_MODEL=gpt-4o-mini
```

### Environment Abstraction

The system automatically converts:

**SvelteKit Format:**
```typescript
import { env } from "$env/dynamic/private"
const apiKey = env.EXA_API_KEY
```

**Mastra Format:**
```javascript
import { ENV_VARS } from '../shared/env.js'
const apiKey = ENV_VARS.EXA_API_KEY()
```

## Building New Agents and Tools

### Creating a New Tool

1. **Create Tool in SvelteKit** (`src/lib/agents/tools/my-new-tool.ts`):

```typescript
import { z } from "zod"
import { createTool } from "@mastra/core"
import { env } from "$env/dynamic/private"

const myToolSchema = z.object({
  input: z.string().describe("The input parameter")
})

export const myNewTool = createTool({
  id: "my-new-tool",
  description: "Description of what this tool does",
  inputSchema: myToolSchema,
  execute: async (context) => {
    const { input } = context.context
    const apiKey = env.MY_API_KEY
    
    // Tool logic here
    return { result: "success" }
  }
})
```

2. **Run Sync Process**:

```bash
pnpm run sync:mastra
pnpm run sync:registry
```

3. **Update Mastra Index** (`src/mastra/index.ts`):

```typescript
import { myNewTool } from './tools/index.js'

const mastra = new Mastra({
  name: 'ROBYNNV3',
  tools: [
    // ... existing tools
    myNewTool
  ]
})
```

4. **Test in Mastra Studio**:

```bash
pnpm run mastra:dev
```

### Creating a New Agent

1. **Create Agent in SvelteKit** (`src/lib/agents/my-new-agent.ts`):

```typescript
import { Agent } from "@mastra/core"
import { createLLMClient } from "./llm-providers"
import { myNewTool } from "./tools/my-new-tool"

const MY_AGENT_SYSTEM_PROMPT = `
You are a helpful assistant that...
`

export const myNewAgent = new Agent({
  name: "MyNewAgent",
  instructions: MY_AGENT_SYSTEM_PROMPT,
  model: createLLMClient({
    provider: "openai",
    model: "gpt-4o-mini"
  }),
  tools: [myNewTool]
})
```

2. **Run Sync Process**:

```bash
pnpm run sync:mastra
```

3. **Update Mastra Index** (`src/mastra/index.ts`):

```typescript
import { myNewAgent } from './agents/index.js'

const mastra = new Mastra({
  name: 'ROBYNNV3',
  agents: [
    // ... existing agents
    myNewAgent
  ]
})
```

### Best Practices

#### Tool Development

- **Use descriptive schemas** with `.describe()` for better Studio documentation
- **Handle errors gracefully** with try-catch blocks
- **Validate inputs** using Zod schemas
- **Use environment variables** for API keys (never hardcode)
- **Add JSDoc comments** for complex functions

#### Agent Development

- **Clear system prompts** that define the agent's role and capabilities
- **Specific tool selection** - only include tools the agent needs
- **Consistent naming** following the pattern `*Agent`
- **Modular design** - break complex agents into smaller, focused ones

#### Sync Considerations

- **Avoid complex TypeScript features** that may not transform cleanly
- **Use standard imports** - avoid dynamic or conditional imports
- **Test transformations** by running `pnpm run sync:validate`
- **Check generated code** in `src/mastra/` directories after sync

### Debugging Transformation Issues

If the sync script fails or generates incorrect code:

1. **Check the sync script patterns** in `src/scripts/sync-mastra.js`
2. **Add specific transformation rules** for your use case
3. **Test with minimal examples** first
4. **Use the validation script**: `pnpm run sync:validate`

#### Common Issues and Solutions

**Issue: TypeScript types not removed properly**
```javascript
// Add transformation pattern to sync-mastra.js
{
  pattern: /your-specific-pattern/g,
  replacement: 'corrected-replacement'
}
```

**Issue: Import paths incorrect**
```javascript
// Check import transformation patterns in TRANSFORM_PATTERNS
```

**Issue: Environment variables not accessible**
```javascript
// Ensure variable is added to src/mastra/shared/env.js
```

## Troubleshooting

### Common Problems

1. **Build Errors After Sync**
   - Run `pnpm run build` to check SvelteKit build
   - Run `pnpm run mastra:dev` to check Mastra build
   - Check transformation patterns in sync script

2. **Tools Not Appearing in Studio**
   - Verify tools are exported in `src/mastra/tools/index.js`
   - Check they're imported in `src/mastra/index.ts`
   - Restart Mastra Studio

3. **Environment Variables Not Found**
   - Check `.env.local` file
   - Verify variables are added to `src/mastra/shared/env.js`
   - Restart development server

4. **Agent Instructions Missing**
   - Check for post-processing rules in sync script
   - Verify constant names match expectations
   - Look for transformation errors in generated files

### Debug Commands

```bash
# Clear Mastra cache and restart
rm -rf .mastra
pnpm run mastra:dev

# Check transformation output
cat src/mastra/tools/your-tool.js

# Validate all transformations
pnpm run sync:validate

# Test SvelteKit build
pnpm run build
```

## Configuration Files

### Mastra Configuration

**File**: `mastra.config.ts`
- Defines Mastra Studio configuration
- Sets development server options
- Configures tool discovery paths

**File**: `src/mastra/index.ts`
- Main entry point for Mastra Studio
- Registers all tools and agents
- Creates the main Mastra instance

### Sync Configuration

**File**: `src/scripts/sync-mastra.js`
- Contains all transformation patterns
- Defines source and target directories
- Handles special cases and post-processing

## Integration with SvelteKit

The Mastra local development system is designed to be completely independent of the main SvelteKit application:

- **No shared runtime dependencies**
- **Separate environment variable handling**
- **Independent build processes**
- **Isolated testing environments**

This ensures that:
- SvelteKit application remains unaffected by Mastra development
- Tools can be tested in isolation
- Changes can be validated before integration
- Development workflows don't interfere with each other

## Future Enhancements

### Planned Features

- **Hot reloading** for tool changes
- **Automated testing** of transformed tools
- **Enhanced validation** with type checking
- **Tool versioning** and rollback capabilities
- **Performance monitoring** for transformations

### Contributing

When adding new transformation patterns or features:

1. Update the sync script with new patterns
2. Add validation tests
3. Update this documentation
4. Test with existing tools to ensure no regressions

---

## Quick Reference

### Daily Workflow
```bash
# 1. Edit SvelteKit tools/agents
# 2. Sync to Mastra
pnpm run sync:mastra && pnpm run sync:registry

# 3. Test in Studio
pnpm run mastra:dev
```

### File Locations
- **SvelteKit Tools**: `src/lib/agents/tools/`
- **SvelteKit Agents**: `src/lib/agents/`
- **Mastra Tools**: `src/mastra/tools/`
- **Mastra Agents**: `src/mastra/agents/`
- **Sync Script**: `src/scripts/sync-mastra.js`

### Key Commands
- `pnpm run sync:mastra` - Transform code
- `pnpm run mastra:dev` - Start Studio
- `pnpm run build` - Validate SvelteKit build

This system provides a robust foundation for developing and testing AI agents and tools while maintaining complete separation between development and production environments.
