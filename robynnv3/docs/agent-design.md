# Mastra Framework Agent Architecture Documentation

## System Architecture Overview

This application implements a comprehensive AI agent system using the **Mastra framework** (`@mastra/core ^0.10.10`), providing specialized agents for different business functions. The architecture follows a modular, tool-based approach where agents are composed of LLM models and specialized tools.

### Core Architecture Components

```
┌─────────────────────────────────────────────────────────────┐
│                    Frontend (SvelteKit)                     │
├─────────────────────────────────────────────────────────────┤
│  UI Components → API Endpoints → Agent Services → Tools     │
└─────────────────────────────────────────────────────────────┘

Frontend Components:
├── AgentDemo.svelte           # Demo interface for public agents
├── ContentAgentSidebar.svelte # Content agent interface
├── SEOAgentDemo.svelte       # SEO agent demo interface
└── ResearchChatArea.svelte   # Research agent chat interface

API Layer:
├── /dashboard/[envSlug]/researcher/+server.ts
├── /dashboard/[envSlug]/content-agent/+server.ts
├── /dashboard/[envSlug]/agent-seo/+server.ts
├── /dashboard/[envSlug]/campaign-orchestrator/+server.ts
└── /api/demo/researcher/+server.ts

Agent Layer:
├── company-researcher.ts      # Company research agent
├── content-agent.ts          # Content generation agent
├── seo-strategist.ts         # SEO strategy agent
└── orchestrator-agent.ts     # Campaign orchestration agent

Tool Layer:
├── apollo-*.ts               # Apollo API integrations
├── exa-search-enhanced-tool.ts # Exa search integration
├── content-generation-tool.ts # Content creation tools
├── keyword-*.ts              # SEO keyword tools
└── web-search-tool.ts        # Web search capabilities
```

## Agent Architecture Analysis

### 1. Agent Definition Pattern

All agents follow a consistent factory pattern using Mastra's `Agent` class:

```typescript
// Standard agent creation pattern
export function createAgentName(llmConfig?: LLMConfig) {
  const model = llmConfig 
    ? createLLMClient(llmConfig)
    : createLLMClient(defaultConfig)

  return new Agent({
    name: "Agent Name",
    instructions: SYSTEM_PROMPT,
    model,
    tools: {
      toolName: toolInstance,
      // ... other tools
    },
  })
}

// Default instance for application use
export const agentInstance = createAgentName()
```

### 2. LLM Provider Configuration

The system supports multiple LLM providers through a unified interface:

**File**: `src/lib/agents/llm-providers.ts`

```typescript
export type LLMProvider = 'openai' | 'anthropic' | 'google' | 'deepseek'

export function createLLMClient(config: LLMConfig) {
  switch (config.provider) {
    case 'openai':
      return createOpenAI({ apiKey: env.OPENAI_API_KEY })(config.model)
    case 'anthropic':
      return createAnthropic({ apiKey: env.ANTHROPIC_API_KEY })(config.model)
    case 'google':
      return createGoogleGenerativeAI({ apiKey: env.GOOGLE_GENERATIVE_AI_API_KEY })(config.model)
    case 'deepseek':
      return createOpenAICompatible({
        name: 'deepseek',
        apiKey: env.DEEPSEEK_API_KEY,
        baseURL: 'https://api.deepseek.com/v1'
      })(config.model)
  }
}
```

**Default Configurations**:
- **OpenAI**: `gpt-4o-mini`
- **Anthropic**: `claude-3-5-sonnet-20241022`
- **Google**: `gemini-1.5-flash`
- **DeepSeek**: `deepseek-chat`

### 3. Agent Implementations

#### Company Researcher Agent
**File**: `src/lib/agents/company-researcher.ts`
- **Purpose**: Company intelligence and research
- **Model**: Anthropic Claude 3.5 Sonnet
- **Tools**: `webSearch`
- **Features**: Citation management, comprehensive company analysis

#### Content Agent
**File**: `src/lib/agents/content-agent.ts`
- **Purpose**: Content creation and editing
- **Model**: Anthropic Claude 3.5 Sonnet
- **Tools**: 
  - `contentGeneration`: Multi-format content creation
  - `textSummarization`: Content summarization
  - `grammarStyleCorrection`: Grammar and style checking
  - `outlineGeneration`: Content structure planning
  - `citationManagement`: Reference management
  - `webSearch`: Research capabilities

#### SEO Strategist Agent
**File**: `src/lib/agents/seo-strategist.ts`
- **Purpose**: SEO strategy and keyword research
- **Model**: OpenAI GPT-4o-mini
- **Tools**:
  - `webSearch`: Competitive research
  - `get_keyword_volume`: Search volume analysis
  - `get_keyword_difficulty`: SEO difficulty scoring
  - `get_related_keywords`: Keyword expansion
  - `get_domain_intersection`: Competitor analysis
  - `get_keywords_for_site`: Site keyword analysis

#### Orchestrator Agent
**File**: `src/lib/agents/orchestrator-agent.ts`
- **Purpose**: Campaign orchestration and company intelligence
- **Model**: Anthropic Claude 3.5 Sonnet
- **Tools**:
  - `apollo_search_company`: Company enrichment
  - `apollo_find_companies`: Competitor discovery
  - `apollo_find_contacts`: Contact information
  - `exa_search`: Enhanced web intelligence

## Tool Integration Patterns

### 1. Tool Creation Pattern

All tools follow Mastra's `createTool` pattern with Zod schema validation:

```typescript
import { z } from "zod"
import { createTool } from "@mastra/core"

const toolSchema = z.object({
  param1: z.string().describe("Parameter description"),
  param2: z.enum(["option1", "option2"]).describe("Enum parameter"),
  param3: z.number().optional().describe("Optional parameter"),
})

export const toolName = createTool({
  id: "tool-identifier",
  description: "Tool description for the LLM",
  inputSchema: toolSchema,
  execute: async (context) => {
    const { param1, param2, param3 } = context.context
    
    // Tool implementation
    try {
      // API calls, processing, etc.
      return {
        success: true,
        data: result,
        metadata: additionalInfo
      }
    } catch (error) {
      throw new Error(`Tool error: ${error.message}`)
    }
  }
})
```

### 2. External API Integration

Tools integrate with various external APIs:

**Apollo API Tools** (`apollo-*.ts`):
- Company enrichment and contact discovery
- Handles API timeouts and error cases
- Provides mock data fallbacks for testing

**Exa Search Tool** (`exa-search-enhanced-tool.ts`):
- Enhanced web search with content extraction
- Competitor intelligence gathering
- Configurable search types and result limits

**DataForSEO Tools** (`keyword-*.ts`):
- Keyword volume and difficulty analysis
- Related keyword discovery
- Domain intersection analysis

### 3. Tool Registration

Tools are registered with agents through the `tools` object:

```typescript
return new Agent({
  name: "Agent Name",
  instructions: SYSTEM_PROMPT,
  model,
  tools: {
    // Key becomes the function name available to the LLM
    toolFunctionName: toolInstance,
    anotherTool: anotherToolInstance,
  },
})
```

## Page-to-Agent Connection Flow

### 1. Frontend Component → API Endpoint

**Pattern**: Svelte components make HTTP requests to SvelteKit API routes

```typescript
// Frontend component (e.g., ContentAgentSidebar.svelte)
const response = await fetch(
  `/dashboard/${envSlug}/content-agent?stream=true`,
  {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      message: userMessage,
      documentId,
      action,
      currentContent: contextToUse,
      selectedText: selectedText.trim(),
      contextType
    })
  }
)
```

### 2. API Endpoint → Agent Invocation

**Pattern**: API routes handle authentication, validation, and agent calls

```typescript
// API endpoint (e.g., content-agent/+server.ts)
export const POST: RequestHandler = async ({ request, locals, url, params }) => {
  // 1. Authentication check
  const { session, user } = locals.auth
  if (!session || !user) {
    return json({ error: "Unauthorized" }, { status: 401 })
  }

  // 2. Request parsing and validation
  const { message, documentId, action, currentContent } = await request.json()
  
  // 3. Agent invocation with timeout
  const response = await Promise.race([
    contentAgent.generate([{
      role: "user",
      content: contextualMessage,
    }]),
    new Promise((_, reject) => 
      setTimeout(() => reject(new Error("Timeout")), 120000)
    ),
  ])

  // 4. Response processing and return
  return json({ response: response.text })
}
```

### 3. Streaming Response Pattern

For real-time progress updates, the system uses Server-Sent Events:

```typescript
// Streaming handler
async function handleStreamingRequest(request, user, supabase, envSlug) {
  const stream = new ReadableStream({
    start(controller) {
      const send = (data) => {
        controller.enqueue(`data: ${JSON.stringify(data)}\n\n`)
      }

      // Progress updates
      send({ step: 1, action: "Processing request", progress: 20 })
      
      // Agent generation
      const response = await agent.generate([{ role: "user", content: message }])
      
      // Final result
      send({ step: 5, response: response.text, progress: 100 })
      controller.close()
    }
  })

  return new Response(stream, {
    headers: { 'Content-Type': 'text/event-stream' }
  })
}
```

### 4. State Management

**Environment State**: `src/lib/states/environment.svelte.ts`
```typescript
export const setEnvironmentState = (environment: Tables<"environments"> | null) => {
  const state = $state({ value: environment })
  return setContext(ENVIRONMENT_KEY, state)
}
```

**Navigation State**: `src/lib/stores/navigation.ts`
```typescript
export const navigationState = writable<NavigationState>({
  isAgentActive: false,
  activeAgentId: null,
  showPersistentNav: false,
  items: defaultNavigationItems
})
```

## Configuration and Environment

### Required Environment Variables

```bash
# LLM Providers
OPENAI_API_KEY=your_openai_key
ANTHROPIC_API_KEY=your_anthropic_key
GOOGLE_GENERATIVE_AI_API_KEY=your_google_key
DEEPSEEK_API_KEY=your_deepseek_key

# External APIs
APOLLO_API_KEY=your_apollo_key
EXA_API_KEY=your_exa_key
DATAFORSEO_LOGIN=your_dataforseo_login
DATAFORSEO_PASSWORD=your_dataforseo_password

# Configuration
LLM_PROVIDER=anthropic  # Default provider
LLM_MODEL=claude-3-5-sonnet-20241022  # Default model
```

### Agent Endpoints

| Agent | Endpoint | Purpose |
|-------|----------|---------|
| Company Researcher | `/dashboard/[envSlug]/researcher` | Company intelligence |
| Content Agent | `/dashboard/[envSlug]/content-agent` | Content creation |
| SEO Strategist | `/dashboard/[envSlug]/agent-seo` | SEO strategy |
| Campaign Orchestrator | `/dashboard/[envSlug]/campaign-orchestrator` | Campaign management |
| Demo Researcher | `/api/demo/researcher` | Public demo |
| Demo SEO | `/api/demo/seo` | Public SEO demo |

## Error Handling and Timeouts

### Timeout Management
All agent calls implement 120-second timeouts:

```typescript
const response = await Promise.race([
  agent.generate([{ role: "user", content: message }]),
  new Promise((_, reject) => 
    setTimeout(() => reject(new Error("Agent timeout after 120 seconds")), 120000)
  ),
])
```

### Error Response Pattern
```typescript
try {
  // Agent operation
} catch (error) {
  console.error('Agent error:', error)
  return json({ 
    error: error instanceof Error ? error.message : "Unknown error" 
  }, { status: 500 })
}
```

### Tool Error Handling
Tools provide fallback mechanisms:

```typescript
// Apollo tool with mock data fallback
if (!apiKey) {
  console.error('Apollo API credentials missing - returning mock data')
  return {
    success: true,
    company: mockCompanyData,
    note: 'Mock data returned - API credentials not configured'
  }
}
```

## Code Walkthrough: Key Files and Their Roles

### Core Agent Files

#### 1. `src/lib/agents/index.ts` - Central Export Hub
```typescript
export * from "./llm-providers"
export * from "./company-researcher"
export * from "./seo-strategist"
export * from "./content-agent"
export * from "./orchestrator-agent"
export * from "./tools/web-search-tool"
// ... all tool exports
```
**Role**: Provides centralized imports for all agent-related functionality.

#### 2. `src/lib/agents/llm-providers.ts` - LLM Abstraction Layer
**Key Functions**:
- `createLLMClient(config: LLMConfig)`: Creates provider-specific LLM clients
- `getDefaultConfig(provider: LLMProvider)`: Returns default configurations
- `createDefaultLLMClient()`: Environment-based client creation

**Usage Pattern**:
```typescript
const model = createLLMClient({
  provider: "anthropic",
  model: "claude-3-5-sonnet-20241022"
})
```

#### 3. Agent Implementation Files
Each agent file follows this structure:
- **System Prompt**: Detailed instructions for the agent's behavior
- **Factory Function**: `createAgentName(llmConfig?: LLMConfig)`
- **Default Instance**: `export const agentInstance = createAgentName()`
- **Tool Registration**: Tools mapped to function names for LLM access

### API Route Structure

#### 1. Authentication Pattern
All protected routes implement this pattern:
```typescript
const { session, user } = locals.auth
if (!session || !user) {
  return json({ error: "Unauthorized" }, { status: 401 })
}
```

#### 2. Streaming vs Non-Streaming
Routes support both modes:
```typescript
const wantsStream = url.searchParams.get("stream") === "true"
if (wantsStream) {
  return handleStreamingRequest(request, user, supabase, params.envSlug)
}
```

#### 3. Request Processing Pipeline
1. **Authentication**: Verify user session
2. **Validation**: Parse and validate request body
3. **Context Preparation**: Build contextual messages
4. **Agent Invocation**: Call agent with timeout
5. **Response Processing**: Format and return results

### Tool Implementation Structure

#### Standard Tool Pattern
```typescript
// 1. Schema Definition
const toolSchema = z.object({
  param: z.string().describe("Parameter description")
})

// 2. Tool Creation
export const toolName = createTool({
  id: "unique-tool-id",
  description: "LLM-facing description",
  inputSchema: toolSchema,
  execute: async (context) => {
    // Implementation with error handling
  }
})
```

#### External API Integration Example
**Apollo Search Company Tool** (`apollo-search-company-tool.ts`):
```typescript
// Environment check with fallback
const apiKey = env.APOLLO_API_KEY
if (!apiKey) {
  return mockDataResponse()
}

// API call with timeout
const controller = new AbortController()
const timeoutId = setTimeout(() => controller.abort(), 30000)

const response = await fetch(url.toString(), {
  method: 'GET',
  headers: { 'X-Api-Key': apiKey },
  signal: controller.signal
})

clearTimeout(timeoutId)
```

### Frontend Integration Patterns

#### 1. Component-to-API Communication
**Standard Fetch Pattern**:
```typescript
const response = await fetch(`/dashboard/${envSlug}/agent-endpoint`, {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ message: userInput })
})
```

#### 2. Streaming Response Handling
```typescript
const reader = response.body?.getReader()
const decoder = new TextDecoder()

while (true) {
  const { done, value } = await reader.read()
  if (done) break

  const chunk = decoder.decode(value)
  const lines = chunk.split('\n')

  for (const line of lines) {
    if (line.startsWith('data: ')) {
      const data = JSON.parse(line.slice(6))
      // Handle progress update
    }
  }
}
```

#### 3. State Management Integration
Components use Svelte stores for state management:
```typescript
// Message store updates
messages.update(msgs => [
  ...msgs,
  {
    id: generateId(),
    role: 'user',
    content: messageText,
    timestamp: new Date()
  }
])
```

## Integration Patterns and Best Practices

### 1. Agent Composition Pattern
Agents are composed of:
- **Instructions**: Detailed system prompts defining behavior
- **Model**: LLM client configured for specific provider/model
- **Tools**: Object mapping function names to tool instances

### 2. Tool Naming Convention
- **File Names**: `kebab-case-tool.ts`
- **Export Names**: `camelCaseTool`
- **Tool IDs**: `kebab-case-id`
- **Function Names**: `snake_case` or `camelCase` (as exposed to LLM)

### 3. Error Handling Strategy
- **API Level**: HTTP status codes with error messages
- **Agent Level**: Timeout handling with Promise.race
- **Tool Level**: Graceful degradation with fallbacks
- **Frontend Level**: User-friendly error messages

### 4. Configuration Management
- **Environment Variables**: Centralized in `.env` files
- **Default Configs**: Defined in `llm-providers.ts`
- **Runtime Config**: Passed through function parameters

### 5. Security Patterns
- **API Keys**: Server-side only, never exposed to client
- **Authentication**: Session-based with Supabase
- **Authorization**: Environment-based access control
- **Input Validation**: Zod schemas for all inputs

## Performance Optimization

### 1. Timeout Management
- **Agent Calls**: 120-second timeout for complex operations
- **API Calls**: 30-second timeout for external services
- **Streaming**: Real-time progress updates to prevent perceived delays

### 2. Caching Strategies
- **Demo Responses**: Pre-computed responses for demo endpoints
- **Rate Limiting**: Implemented for public demo endpoints
- **Connection Pooling**: Managed by underlying HTTP clients

### 3. Resource Management
- **Memory**: Agents instantiated once and reused
- **Connections**: Proper cleanup of streaming connections
- **Error Recovery**: Graceful handling of failed operations

## Troubleshooting Common Issues

### 1. Agent Not Responding
**Symptoms**: Requests timeout or hang indefinitely
**Solutions**:
- Check API key configuration in environment variables
- Verify network connectivity to LLM providers
- Review agent timeout settings (default 120s)
- Check for rate limiting on external APIs

### 2. Tool Execution Failures
**Symptoms**: Tools return errors or unexpected results
**Solutions**:
- Validate tool input schemas match expected parameters
- Check external API credentials and quotas
- Review tool error handling and fallback mechanisms
- Verify environment variable configuration

### 3. Streaming Issues
**Symptoms**: Progress updates not appearing or connection drops
**Solutions**:
- Ensure proper Server-Sent Events headers
- Check client-side stream reading implementation
- Verify controller cleanup in streaming handlers
- Review network proxy/firewall settings

### 4. Authentication Problems
**Symptoms**: Unauthorized errors or session issues
**Solutions**:
- Verify Supabase configuration and RLS policies
- Check session token validity and refresh logic
- Review environment-based access controls
- Validate user permissions for specific agents

### 5. Performance Issues
**Symptoms**: Slow response times or high resource usage
**Solutions**:
- Optimize agent system prompts for clarity
- Review tool execution efficiency
- Implement proper caching strategies
- Monitor external API response times

## Development Workflow

### Adding a New Agent

1. **Create Agent File**: `src/lib/agents/new-agent.ts`
2. **Define System Prompt**: Detailed instructions for agent behavior
3. **Select Tools**: Choose appropriate tools for agent capabilities
4. **Create Factory Function**: Follow standard agent creation pattern
5. **Export from Index**: Add to `src/lib/agents/index.ts`
6. **Create API Route**: Add endpoint in `src/routes/(admin)/dashboard/[envSlug]/`
7. **Build Frontend**: Create UI components for agent interaction
8. **Add Navigation**: Update navigation store with new agent
9. **Test Integration**: Verify end-to-end functionality

### Adding a New Tool

1. **Create Tool File**: `src/lib/agents/tools/new-tool.ts`
2. **Define Schema**: Use Zod for input validation
3. **Implement Execute Function**: Handle API calls and processing
4. **Add Error Handling**: Implement fallbacks and error recovery
5. **Export from Index**: Add to agent index exports
6. **Register with Agent**: Add to appropriate agent's tools object
7. **Test Tool Function**: Verify tool execution and erools aror cases
8. **Document Usage**: Add to agent system prompts if needed

---

*This documentation provides a comprehensive guide to the Mastra framework integration. For specific implementation details, refer to the actual code files and their inline documentation.*
