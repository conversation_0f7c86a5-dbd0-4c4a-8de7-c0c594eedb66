# Design System Documentation

## Design System Overview

### Core Design Principles
Our application follows a **dual-theme design system** with two distinct visual identities:

1. **Professional Theme**: Warm, cream-based palette with subtle shadows and refined typography
2. **NEO Theme**: Bold, brutalist design with sharp edges, high contrast, and geometric elements

### Visual Identity
- **Typography**: Plus Jakarta Sans as primary font family with system fallbacks
- **Design Philosophy**: Product-centric marketing with engineering aesthetics
- **Animation**: Motion One-powered animations with performance optimization and reduced motion support

## Theme Configuration

### Theme Files Structure
```
src/lib/themes.ts          # Theme definitions and CSS variables
src/lib/server/theme.ts     # Server-side theme selection
src/lib/components/ThemeProvider.svelte  # Client-side theme injection
```

### Theme Selection
- **Environment Variable**: `SITE_THEME` (defaults to 'NEO')
- **Dynamic Switching**: Themes are injected via `ThemeProvider` component
- **DaisyUI Integration**: Maps to `professional` and `neo` DaisyUI themes

### CSS Variables Structure
Both themes use HSL-based CSS variables for consistency:
```css
--background: hsl(...)
--foreground: hsl(...)
--primary: hsl(...)
--secondary: hsl(...)
--accent: hsl(...)
--muted: hsl(...)
--border: hsl(...)
--destructive: hsl(...)
```

## Component Libraries

### Primary Libraries
- **Tailwind CSS**: `^4.0.0` - Utility-first CSS framework
- **DaisyUI**: `^5.0.43` - Component library built on Tailwind
- **shadcn/ui (Svelte)**: Custom implementation using Bits UI primitives
- **Bits UI**: `^0.21.16` - Headless component primitives
- **Tailwind Variants**: `^0.2.1` - Component variant management

### UI Component Architecture
```
src/lib/components/ui/
├── button/           # Button variants and styles
├── card/            # Card components
├── dialog/          # Modal and dialog components
├── form/            # Form components with Formsnap
├── alert/           # Alert and notification components
├── label/           # Form labels
├── separator/       # Divider components
└── accordion/       # Collapsible content
```

### Animation Libraries
- **Motion One**: `@motionone/svelte ^10.16.4` - High-performance animations
- **Custom Animation System**: Located in `src/lib/animations/`

## Design Patterns

### Hero Section Pattern
The hero section uses a **3-column grid layout** for feature cards:
```html
<div class="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-5xl mx-auto">
  <!-- Feature cards -->
</div>
```

### Card Components
Standard card pattern with hover effects:
```css
.linear-card {
  background: var(--card);
  border: 1px solid var(--border);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--shadow);
}
```

### Button Variants
Using Tailwind Variants for consistent button styling:
- `default`: Primary brand button
- `destructive`: Error/delete actions
- `outline`: Secondary actions
- `secondary`: Muted actions
- `ghost`: Minimal styling
- `link`: Text-based links

### Navigation Patterns
- **Fixed Navigation**: Sticky header with backdrop blur
- **Mobile Menu**: Slide-out overlay with focus trapping
- **Persistent Navigation**: Floating tool panel for dashboard

## File Structure

### Key Design-Related Directories
```
src/
├── app.css                 # Global styles and theme utilities
├── lib/
│   ├── themes.ts          # Theme definitions
│   ├── components/
│   │   ├── ui/           # shadcn/ui components
│   │   ├── ThemeProvider.svelte
│   │   └── ContactModal.svelte
│   └── animations/        # Animation system
│       ├── index.ts      # Core animations
│       ├── actions.ts    # Svelte actions
│       ├── performance.ts # Performance optimization
│       └── svelte-transitions.ts
├── routes/
│   └── +layout.svelte    # Global layout with theme provider
└── content/
    └── home/
        └── hero.md       # Hero section content
```

### Configuration Files
- `tailwind.config.js`: Tailwind and DaisyUI configuration
- `components.json`: shadcn/ui configuration
- `postcss.config.js`: PostCSS configuration

## Design Tokens

### Color Palettes

#### Professional Theme
- **Primary**: `hsl(var(--primary))` - Warm accent color
- **Background**: `hsl(48 33.3333% 97.0588%)` - Warm cream
- **Card**: `hsl(48 33.3333% 97.0588%)` - Matching background
- **Border**: Subtle warm borders
- **Text**: `hsl(48 19.6078% 20%)` - Dark warm text

#### NEO Theme
- **Primary**: `hsl(0 100% 60%)` - Bold red
- **Secondary**: `hsl(47.9 95.8% 53.1%)` - Bright yellow
- **Accent**: `hsl(260.1077 108.4472% 67.2897%)` - Purple
- **Background**: `hsl(0 0% 100%)` - Pure white
- **Border**: `hsl(0 0% 0%)` - Pure black

### Typography Scale
- **Font Family**: "Plus Jakarta Sans" with system fallbacks
- **Heading Classes**: `.linear-heading` with tight line-height
- **Body Classes**: `.linear-body` with comfortable line-height
- **Mono Classes**: `.linear-mono` for code/technical content

### Spacing System
- **Base Unit**: 0.25rem (4px)
- **Component Padding**: Consistent 1rem (16px) for cards
- **Section Spacing**: 3rem (48px) between major sections
- **Grid Gaps**: 1.5rem (24px) for card grids

### Shadow System
```css
--shadow-sm: Subtle shadows for cards
--shadow: Standard component shadows
--shadow-lg: Elevated component shadows
--shadow-xl: Modal and overlay shadows
```

## Responsive Design

### Breakpoints
Following Tailwind's default breakpoints:
- **sm**: 640px and up
- **md**: 768px and up (primary mobile breakpoint)
- **lg**: 1024px and up
- **xl**: 1280px and up
- **2xl**: 1536px and up

### Mobile-First Approach
- Grid layouts collapse to single column on mobile
- Touch targets minimum 44px for accessibility
- Optimized animations for mobile performance
- Backdrop blur effects for mobile navigation

### Responsive Patterns
```css
/* Hero section responsive text */
.linear-heading {
  @apply text-5xl md:text-7xl;
}

/* Grid responsive layout */
.grid {
  @apply grid-cols-1 md:grid-cols-3;
}
```

## Accessibility

### Focus Management
- **Focus Trapping**: Implemented in modals and mobile menus
- **Keyboard Navigation**: Full keyboard support for all interactive elements
- **Focus Indicators**: Custom focus rings using `--ring` color

### ARIA Support
- **Semantic HTML**: Proper use of headings, landmarks, and roles
- **ARIA Labels**: Comprehensive labeling for screen readers
- **Live Regions**: For dynamic content updates

### Motion Accessibility
- **Reduced Motion**: Respects `prefers-reduced-motion` preference
- **Performance Optimization**: GPU acceleration and will-change properties
- **Adaptive Animations**: Adjusts complexity based on device capabilities

### Touch Accessibility
- **Minimum Touch Targets**: 44px minimum for all interactive elements
- **Touch Optimization**: `touch-action: manipulation` for better responsiveness
- **Spacing**: Adequate spacing between touch targets

### Color Accessibility
- **Contrast Ratios**: Meets WCAG AA standards
- **Color Independence**: Information not conveyed by color alone
- **Theme Support**: Both light themes maintain accessibility standards

## Implementation Guidelines

### Adding New Components

1. **Create Component Structure**:
   ```bash
   src/lib/components/ui/my-component/
   ├── index.ts          # Exports and variants
   ├── my-component.svelte  # Main component
   └── my-component-*.svelte  # Sub-components
   ```

2. **Use Tailwind Variants**:
   ```typescript
   import { tv } from "tailwind-variants";

   export const myComponentVariants = tv({
     base: "base-classes",
     variants: {
       variant: {
         default: "default-classes",
         secondary: "secondary-classes"
       }
     }
   });
   ```

3. **Follow Accessibility Patterns**:
   - Use semantic HTML elements
   - Include proper ARIA attributes
   - Implement keyboard navigation
   - Ensure minimum touch targets (44px)

### Theme Customization

To modify themes, edit `src/lib/themes.ts`:

```typescript
export const CUSTOM_THEME = `
:root {
  --primary: hsl(your-color);
  --background: hsl(your-background);
  /* ... other variables */
}
`;
```

### Animation Best Practices

1. **Use Motion One Actions**:
   ```svelte
   <div use:fadeIn={{ delay: 0.2, duration: 0.8 }}>
     Content
   </div>
   ```

2. **Respect Reduced Motion**:
   ```typescript
   if (prefersReducedMotion()) return {};
   ```

3. **Optimize Performance**:
   - Use `will-change` sparingly
   - Prefer `transform` and `opacity` for animations
   - Clean up animations on component destroy

### Grid Layout Patterns

#### Hero Feature Cards (3-Column)
```html
<div class="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-5xl mx-auto">
  <div class="linear-card p-6 rounded-2xl">Card 1</div>
  <div class="linear-card p-6 rounded-2xl">Card 2</div>
  <div class="linear-card p-6 rounded-2xl">Card 3</div>
</div>
```

#### Dashboard Grid
```html
<div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
  <!-- Responsive dashboard cards -->
</div>
```

## Common Patterns Reference

### Modal Implementation
```svelte
<!-- Use ContactModal.svelte as reference -->
<script>
  import { trapFocus, handleEscape } from '$lib/utils/accessibility';
</script>

<div class="fixed inset-0 z-50" on:keydown={trapFocus}>
  <!-- Modal content -->
</div>
```

### Form Styling
```html
<input class="input-brutal" />
<!-- Provides consistent form styling across themes -->
```

### Button Usage
```svelte
<Button variant="default" size="lg">Primary Action</Button>
<Button variant="outline">Secondary Action</Button>
<Button variant="ghost">Minimal Action</Button>
```

### Card Components
```html
<div class="linear-card p-6 rounded-lg">
  <!-- Card content with consistent styling -->
</div>
```

## Troubleshooting

### Common Issues

1. **Theme Not Loading**: Check `ThemeProvider` is included in root layout
2. **Animations Not Working**: Verify Motion One is imported correctly
3. **Responsive Issues**: Use mobile-first approach with `md:` prefixes
4. **Accessibility Warnings**: Add proper ARIA labels and keyboard handlers

### Performance Optimization

1. **Lazy Load Components**: Use dynamic imports for heavy components
2. **Optimize Images**: Use appropriate formats and sizes
3. **Minimize Bundle Size**: Tree-shake unused utilities
4. **GPU Acceleration**: Use `transform: translateZ(0)` for animated elements

---

*This documentation serves as the single source of truth for our design system. For implementation details, refer to the specific component files and theme configurations.*

## Quick Reference

### File Locations
- **Themes**: `src/lib/themes.ts`
- **Global Styles**: `src/app.css`
- **Components**: `src/lib/components/ui/`
- **Animations**: `src/lib/animations/`
- **Config**: `tailwind.config.js`, `components.json`

### Key Classes
- **Typography**: `.linear-heading`, `.linear-body`, `.linear-mono`
- **Cards**: `.linear-card`, `.card-professional`, `.card-brutal`
- **Buttons**: `.btn-professional`, `.linear-btn-primary`
- **Layout**: `.linear-grid`, `.linear-hero`
- **Animations**: `.linear-fade-in`, `.motion-safe`
