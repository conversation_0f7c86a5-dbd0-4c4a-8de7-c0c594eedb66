# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Package Management
- **Install dependencies**: `pnpm install` (use pnpm, not npm)
- **Clean install**: `pnpm run clean` (removes node_modules, .svelte-kit, build artifacts)

### Development
- **Start dev server**: `pnpm run dev` (runs on http://localhost:5173)
- **Build for production**: `pnpm run build`
- **Preview production build**: `pnpm run preview`

### Testing & Quality
- **Run tests**: `pnpm run test` (vitest in watch mode)
- **Run tests once**: `pnpm run test_run`
- **Run single test**: `pnpm run test -- filename.test.ts`
- **Lint code**: `pnpm run lint` (ESLint)
- **Format code**: `pnpm run format` (Prettier)
- **Check formatting**: `pnpm run format_check`
- **Type check**: `pnpm run check` (svelte-check)
- **Type check watch**: `pnpm run check:watch`

Always run `pnpm run lint` and `pnpm run check` after making changes to ensure code quality.

## Project Architecture

### Tech Stack
- **Frontend**: SvelteKit 2.0 with Svelte 5, TypeScript, TailwindCSS, DaisyUI
- **Backend**: Supabase (auth, database, RLS policies)
- **Payments**: Stripe integration with webhooks
- **Email**: Resend for transactional emails
- **AI/Agents**: Mastra framework with OpenAI, Anthropic, Google models
- **Testing**: Vitest with jsdom for DOM testing
- **Styling**: TailwindCSS with DaisyUI components and Bits UI

### Key Directory Structure
```
src/
├── routes/
│   ├── (admin)/           # Protected admin routes
│   │   └── dashboard/     # User dashboard with [envSlug] dynamic routing
│   └── (marketing)/       # Public marketing pages
│       ├── auth/          # Authentication pages
│       ├── blog/          # Blog engine with posts
│       ├── login/         # Sign in/up flow
│       └── pricing/       # Pricing page
├── lib/
│   ├── agents/           # AI agents (company researcher, etc.)
│   ├── components/       # Reusable UI components
│   │   └── ui/          # Shadcn-svelte components
│   ├── emails/          # Email templates
│   ├── states/          # Svelte 5 state management
│   └── supabase/        # Supabase client and types
supabase/
├── migrations/          # Database schema migrations
└── config.toml         # Supabase configuration
```

### Authentication Flow
- Uses Supabase Auth with email/password and OAuth
- OAuth providers configured in `src/routes/(marketing)/login/login_config.ts`
- Protected routes use `(admin)` group with auth checks in `+layout.server.ts`
- Anonymous users redirect to `/login` when accessing protected routes

### Database & Migrations
- Supabase Postgres with Row Level Security (RLS)
- Run migrations: Apply SQL files in `supabase/migrations/` chronologically
- Database schema includes profiles, environments, and subscription tables
- Use `PRIVATE_SUPABASE_SERVICE_ROLE` for server-side operations

### AI Agent System
- Built with Mastra framework
- Company researcher agent with web search capabilities
- LLM providers: OpenAI, Anthropic, Google (configured in `src/lib/agents/llm-providers.ts`)
- Agent endpoints in `src/routes/(admin)/dashboard/[envSlug]/researcher/+server.ts`

### Payment Integration
- Stripe Checkout for subscriptions
- Pricing plans in `src/lib/pricing_plans.ts`
- Webhook handling for subscription events
- Portal for self-service billing management

### Configuration Files
- Site config: `src/lib/config.ts` (WebsiteName, WebsiteBaseUrl, etc.)
- Environment variables: Use `.env.local` for local development
- Required env vars: `PUBLIC_SUPABASE_URL`, `PUBLIC_SUPABASE_ANON_KEY`, `PRIVATE_SUPABASE_SERVICE_ROLE`, `PRIVATE_STRIPE_API_KEY`

## Development Patterns

### Route Organization
- `(admin)` routes require authentication
- `(marketing)` routes are public
- Dynamic routes use `[envSlug]` for environment-specific content
- Use `+layout.server.ts` for server-side auth checks
- Use `+page.server.ts` for server-side data loading

### Component Architecture
- Shadcn-svelte components in `src/lib/components/ui/`
- Custom components follow SvelteKit conventions
- Use Bits UI for headless component primitives
- TailwindCSS for styling with DaisyUI theme system

### Form Handling
- Use Superforms with Zod schemas for validation
- Form schemas defined in `src/lib/schemas.ts`
- Server actions handle form submissions in `+page.server.ts`

### State Management
- Svelte 5 runes for reactive state
- Environment state in `src/lib/states/environment.svelte.ts`
- Server state managed through SvelteKit's load functions

### Testing Strategy
- Vitest for unit tests with globals enabled
- JSDOM for DOM testing
- Test files use `.test.ts` suffix
- Server-side tests use `.server.test.ts` suffix

## Important Notes

- Always use `pnpm` instead of `npm` for package management
- Run linting and type checking before committing changes
- Follow the existing authentication patterns for new protected routes
- Use environment variables for API keys and sensitive configuration
- Stripe integration uses test keys in development - never commit live keys
- The project includes a functional demo with sample data and test payment flows