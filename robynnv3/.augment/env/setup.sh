#!/bin/bash
set -e

# Update system packages
sudo apt-get update

# Remove existing Node.js installation
sudo apt-get remove -y nodejs npm
sudo apt-get autoremove -y

# Remove any existing NodeSource repositories
sudo rm -f /etc/apt/sources.list.d/nodesource.list
sudo apt-get update

# Install Node.js 20.x (required for this project)
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install pnpm globally using npm (since it comes with Node.js)
sudo npm install -g pnpm

# Navigate to workspace directory
cd /mnt/persist/workspace

# Install project dependencies using pnpm
pnpm install

# Add pnpm and node to PATH in user profile
echo 'export PATH="/usr/bin:$PATH"' >> $HOME/.profile
echo 'export NODE_PATH="/usr/lib/node_modules"' >> $HOME/.profile

# Source the profile to make changes available
source $HOME/.profile

# Verify installations
echo "Node.js version: $(node --version)"
echo "npm version: $(npm --version)"
echo "pnpm version: $(pnpm --version)"

# Create a minimal .env.local file for testing (to avoid build errors)
cat > .env.local << 'EOF'
# Minimal environment variables for testing
PRIVATE_RESEND_API_KEY=test_key_for_testing
OPENAI_API_KEY=test_key_for_testing
PUBLIC_SUPABASE_URL=https://test.supabase.co
PUBLIC_SUPABASE_ANON_KEY=test_key
PRIVATE_SUPABASE_SERVICE_ROLE_KEY=test_key
STRIPE_SECRET_KEY=sk_test_test
PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_test
EOF

# Run SvelteKit sync to generate TypeScript configuration
pnpm exec svelte-kit sync

echo "Setup completed successfully!"
echo "Note: Some tests may fail due to email template loading in test environment, but core functionality tests should pass."