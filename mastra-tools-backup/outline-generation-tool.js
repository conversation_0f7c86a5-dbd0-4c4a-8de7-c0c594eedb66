// Auto-generated Mastra tool from outline-generation-tool.ts
// This file is automatically generated by src/scripts/sync-mastra.js
// Do not edit manually - changes will be overwritten

import { z } from "zod"
import { createTool } from "@mastra/core"

const outlineGenerationSchema = z.object({
  topic: z
    .string()
    .min(3)
    .describe("The main topic or subject for the outline"),
  contentType: z
    .enum([
      "article",
      "blog-post",
      "whitepaper", 
      "presentation",
      "report",
      "essay",
      "research-paper",
      "case-study",
      "tutorial",
      "guide",
      "book-chapter",
      "proposal"
    ])
    .describe("Type of content the outline is for"),
  targetAudience: z
    .enum([
      "general",
      "technical",
      "executive",
      "academic",
      "beginner",
      "intermediate", 
      "expert",
      "students",
      "professionals"
    ])
    .default("general")
    .describe("Target audience for the content"),
  outlineDepth: z
    .enum([
      "shallow", // 1-2 levels (main sections only)
      "medium", // 2-3 levels (sections and subsections)
      "deep" // 3-4 levels (detailed hierarchy)
    ])
    .default("medium")
    .describe("How detailed the outline should be"),
  structureType: z
    .enum([
      "chronological",
      "problem-solution",
      "cause-effect",
      "compare-contrast",
      "hierarchical",
      "sequential",
      "thematic",
      "argumentative"
    ])
    .default("hierarchical")
    .describe("Organizational structure for the outline"),
  estimatedLength: z
    .enum([
      "short", // 500-1000 words
      "medium", // 1000-2500 words
      "long", // 2500-5000 words
      "extended" // 5000+ words
    ])
    .default("medium")
    .describe("Estimated length of the final content"),
  includeIntroduction: z
    .boolean()
    .default(true)
    .describe("Whether to include introduction section"),
  includeConclusion: z
    .boolean()
    .default(true)
    .describe("Whether to include conclusion section"),
  keyPoints: z
    .array(z.string())
    .optional()
    .describe("Specific key points or topics that must be covered"),
  researchQuestions: z
    .array(z.string())
    .optional()
    .describe("Research questions the content should address"),
  includeCallToAction: z
    .boolean()
    .default(false)
    .describe("Whether to include a call-to-action section")
})

export const outlineGenerationTool = createTool({
  id: "outline-generation",
  description: "Generate structured, hierarchical outlines for various types of content",
  inputSchema: outlineGenerationSchema,
  execute: async (context) => {
    const {
      topic,
      contentType,
      targetAudience,
      outlineDepth,
      structureType,
      estimatedLength,
      includeIntroduction,
      includeConclusion,
      keyPoints,
      researchQuestions,
      includeCallToAction
    } = context.context

    // Define content ,
      "blog-post": {
        defaultSections: ["Hook", "Introduction", "Main Points", "Personal Insights", "Call to Action"],
        format: "engaging blog post structure"
      },
      whitepaper: {
        defaultSections: ["Executive Summary", "Problem Statement", "Solution Overview", "Detailed Analysis", "Benefits", "Implementation", "Conclusion"],
        format: "authoritative whitepaper structure"
      },
      presentation: {
        defaultSections: ["Title Slide", "Agenda", "Problem/Opportunity", "Solution", "Benefits", "Next Steps"],
        format: "presentation slide structure"
      },
      report: {
        defaultSections: ["Executive Summary", "Methodology", "Findings", "Analysis", "Recommendations", "Conclusion"],
        format: "formal report structure"
      },
      essay: {
        defaultSections: ["Introduction", "Thesis Statement", "Body Paragraphs", "Supporting Evidence", "Conclusion"],
        format: "academic essay structure"
      },
      "research-paper": {
        defaultSections: ["Abstract", "Introduction", "Literature Review", "Methodology", "Results", "Discussion", "Conclusion", "References"],
        format: "academic research paper structure"
      },
      "case-study": {
        defaultSections: ["Overview", "Challenge", "Solution", "Implementation", "Results", "Lessons Learned"],
        format: "business case study structure"
      },
      tutorial: {
        defaultSections: ["Introduction", "Prerequisites", "Step-by-Step Instructions", "Examples", "Troubleshooting", "Summary"],
        format: "instructional tutorial structure"
      },
      guide: {
        defaultSections: ["Overview", "Getting Started", "Main Sections", "Best Practices", "Common Pitfalls", "Resources"],
        format: "comprehensive guide structure"
      },
      "book-chapter": {
        defaultSections: ["Chapter Introduction", "Key Concepts", "Detailed Exploration", "Examples", "Chapter Summary"],
        format: "book chapter structure"
      },
      proposal: {
        defaultSections: ["Executive Summary", "Problem Statement", "Proposed Solution", "Timeline", "Budget", "Benefits", "Next Steps"],
        format: "business proposal structure"
      }
    }

    // Define structure // Define depth level specifications
    const depthSpecs = {
      shallow: {
        levels: 2,
        description: "Main sections with brief subsections",
        detail: "High-level overview with key points"
      },
      medium: {
        levels: 3,
        description: "Sections, subsections, and key points",
        detail: "Balanced detail with clear hierarchy"
      },
      deep: {
        levels: 4,
        description: "Detailed hierarchy with specific talking points",
        detail: "Comprehensive breakdown with specific details"
      }
    }

    const contentStructure = contentStructures[contentType]
    const depthSpec = depthSpecs[outlineDepth]

    // Build the outline generation prompt
    let outlinePrompt = `Generate a detailed ${outlineDepth} outline for a ${contentType} about "${topic}".

OUTLINE REQUIREMENTS:
- Content Type: ${contentType} (${contentStructure.format})
- Target Audience: ${targetAudience}
- Structure Type: ${structureType} (${structureApproaches[structureType]})
- Outline Depth: ${outlineDepth} (${depthSpec.levels} levels - ${depthSpec.description})
- Estimated Length: ${estimatedLength}

STRUCTURE APPROACH:
${structureApproaches[structureType]}

DEFAULT SECTIONS TO CONSIDER:
${contentStructure.defaultSections.map((section, index) => `${index + 1}. ${section}`).join('\n')}

OUTLINE SPECIFICATIONS:
- Create ${depthSpec.levels} levels of hierarchy
- Use clear, descriptive headings
- Include specific talking points for each section
- Ensure logical flow between sections
- ${depthSpec.detail}`

    if (includeIntroduction) {
      outlinePrompt += `\n- Include a compelling introduction section`
    }

    if (includeConclusion) {
      outlinePrompt += `\n- Include a strong conclusion section`
    }

    if (includeCallToAction) {
      outlinePrompt += `\n- Include a call-to-action section`
    }

    if (keyPoints && keyPoints.length > 0) {
      outlinePrompt += `\n- Must cover these key points: ${keyPoints.join(', ')}`
    }

    if (researchQuestions && researchQuestions.length > 0) {
      outlinePrompt += `\n- Address these research questions: ${researchQuestions.join('; ')}`
    }

    // Add formatting instructions
    outlinePrompt += `\n\nFORMAT INSTRUCTIONS:
- Use hierarchical numbering (1., 1.1, 1.1.1, etc.)
- Each section should have a clear, descriptive title
- Include 2-4 key points or subtopics under each main section
- Add brief descriptions for complex topics
- Ensure the outline flows logically from start to finish

Generate the complete ${outlineDepth} outline now:`

    // Calculate estimated sections based on content ,
      medium: { main: 5, sub: 15, total: 20 },
      long: { main: 7, sub: 25, total: 32 },
      extended: { main: 10, sub: 40, total: 50 }
    }

    const estimatedSections = sectionEstimates[estimatedLength]

    return {
      success: true,
      outlineConfig: {
        topic,
        contentType,
        targetAudience,
        outlineDepth,
        structureType,
        estimatedLength,
        levels: depthSpec.levels
      },
      contentStructure: {
        format: contentStructure.format,
        defaultSections: contentStructure.defaultSections,
        structureApproach: structureApproaches[structureType]
      },
      estimatedSections,
      outlinePrompt,
      metadata: {
        includeIntroduction,
        includeConclusion,
        includeCallToAction,
        keyPoints: keyPoints || [],
        researchQuestions: researchQuestions || [],
        depthDescription: depthSpec.description,
        structureDescription: structureApproaches[structureType]
      }
    }
  }
})
