// Auto-generated Mastra tool from apollo-find-contacts-tool.ts
// This file is automatically generated by src/scripts/sync-mastra.js
// Do not edit manually - changes will be overwritten

import { z } from 'zod'
import { createTool } from '@mastra/core'
import { ENV_VARS } from '../shared/env.js'

// Retry configuration
const RETRY_CONFIG = {
  maxRetries: 3,
  baseDelay: 1000, // 1 second
  maxDelay: 10000, // 10 seconds
  backoffMultiplier: 2
}

// Helper function for exponential backoff retry
async function retryWithBackoff(
  operation,
  context,
  maxRetries = RETRY_CONFIG.maxRetries
) {
  let lastError
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation()
    } catch (error) {
      lastError = error

      // Don't retry on certain errors
      if (error instanceof Error && (
        error.message.includes('401') || // Unauthorized
        error.message.includes('403') || // Forbidden
        error.message.includes('404')    // Not found
      )) {
        throw error
      }

      if (attempt === maxRetries) {
        console.error(`${context} failed after ${maxRetries} attempts:`, lastError.message)
        throw lastError
      }

      const delay = Math.min(
        RETRY_CONFIG.baseDelay * Math.pow(RETRY_CONFIG.backoffMultiplier, attempt - 1),
        RETRY_CONFIG.maxDelay
      )

      console.warn(`${context} attempt ${attempt} failed, retrying in ${delay}ms:`, lastError.message)
      await new Promise(resolve => setTimeout(resolve, delay))
    }
  }

  throw lastError
}

const apolloFindContactsSchema = z.object({
  company_domains: z.array(z.string()).describe('Array of company domains to find contacts for'),
  job_titles: z.array(z.string()).optional().describe('Job titles to search for (e.g., CEO, CTO, VP, "Software Engineer", "Product Manager")'),
  seniority_levels: z.array(z.string()).optional().describe('Seniority levels: senior, director, vp, c_suite, manager, individual_contributor'),
  departments: z.array(z.string()).optional().describe('Departments: engineering, sales, marketing, operations, finance, hr, legal'),
  company_names: z.array(z.string()).optional().describe('Company names when domain search fails'),
  limit_per_company: z.number().optional().default(5).describe('Maximum contacts per company (max 10)'),
  use_fallback_search: z.boolean().optional().default(true).describe('Enable fallback search strategies when primary search fails')
})

export const apolloFindContactsTool = createTool({
  id: 'apollo_find_contacts',
  description: 'Retrieve contacts for companies using Apollo API',
  inputSchema: apolloFindContactsSchema,
  execute: async (context) => {
    const {
      company_domains,
      job_titles,
      seniority_levels,
      departments,
      company_names,
      limit_per_company,
      use_fallback_search
    } = context.context

    const apiKey = ENV_VARS.APOLLO_API_KEY()
    if (!apiKey) {
      throw new Error('Apollo API key is required but not configured. Please set APOLLO_API_KEY in your environment variables.')
    }

    // Enhanced search function with optimized strategy order (based on testing results)
    async function searchContactsForCompany(domain, companyName) {
      const searchStrategies = [
        // Strategy 1: Company name search (MOST EFFECTIVE - prioritized based on testing)
        () => companyName ? searchWithFilters(domain, {
          q_organization_name: companyName,
          ...(job_titles?.length && { person_titles: job_titles.slice(0, 3) }) // Limit to top 3 titles
        }) : Promise.resolve([]),

        // Strategy 2: Domain + specific filters (fallback)
        () => use_fallback_search ? searchWithFilters(domain, {
          q_organization_domains: [domain],
          ...(job_titles?.length && { person_titles: job_titles }),
          ...(seniority_levels?.length && { person_seniorities: seniority_levels }),
          ...(departments?.length && { q_person_departments: departments })
        }) : Promise.resolve([]),

        // Strategy 3: Domain + broader seniority (fallback)
        () => use_fallback_search ? searchWithFilters(domain, {
          q_organization_domains: [domain],
          person_seniorities: ['director', 'vp', 'c_suite', 'manager']
        }) : Promise.resolve([]),

        // Strategy 4: Domain only (broadest fallback)
        () => use_fallback_search ? searchWithFilters(domain, {
          q_organization_domains: [domain]
        }) : Promise.resolve([])
      ]

      const strategyNames = [
        'Company name search (PRIMARY - most effective)',
        'Domain + specific filters (fallback)',
        'Domain + broader seniority (fallback)',
        'Domain only (broadest fallback)'
      ]

      for (let i = 0; i < searchStrategies.length; i++) {
        try {
          console.log(`🔄 Trying ${strategyNames[i]} for ${domain}${companyName ? ` (${companyName})` : ''}`)
          const contacts = await searchStrategies[i]()
          if (contacts.length > 0) {
            console.log(`✅ ${strategyNames[i]} succeeded for ${domain}: found ${contacts.length} contacts`)
            return contacts.slice(0, limit_per_company)
          }
          console.log(`⚠️ ${strategyNames[i]} returned no results for ${domain}`)
        } catch (error) {
          console.warn(`❌ ${strategyNames[i]} failed for ${domain}:`, error instanceof Error ? error.message : error)
          if (i === searchStrategies.length - 1) {
            throw error // Re-throw on final strategy failure
          }
        }

        // Add delay between strategies to be respectful to API
        if (i < searchStrategies.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 1500))
        }
      }

      return []
    }

    // Core search function with retry logic
    async function searchWithFilters(domain, filters) {
      return retryWithBackoff(async () => {
        const searchFilters = {
          ...filters,
          page: 1,
          per_page: Math.min(limit_per_company, 10)
        }

        console.log(`🔍 Apollo search for ${domain}:`, JSON.stringify(searchFilters, null, 2))

        const controller = new AbortController()
        const timeoutId = setTimeout(() => controller.abort(), 30000)

        try {
          const response = await fetch('https://api.apollo.io/api/v1/mixed_people/search', {
            method: 'POST',
            headers: {
              'X-Api-Key': apiKey,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(searchFilters),
            signal: controller.signal
          })

          clearTimeout(timeoutId)

          if (!response.ok) {
            const errorText = await response.text().catch(() => 'Unknown error')
            throw new Error(`Apollo API error ${response.status}: ${errorText}`)
          }

          const data = await response.json()

          if (!data.people || !Array.isArray(data.people)) {
            console.log(`📭 No people data in response for ${domain}`)
            return []
          }

          return data.people.map((person) => ({
            id: person.id,
            first_name: person.first_name,
            last_name: person.last_name,
            name: person.name,
            title: person.title,
            email: person.email,
            linkedin_url: person.linkedin_url,
            company_name: person.organization?.name,
            company_domain: domain,
            seniority: person.seniority,
            department: person.departments?.[0],
            location: [person.city, person.state, person.country].filter(Boolean).join(', '),
            phone: person.phone_numbers?.[0]?.sanitized_number,
            confidence_score: 0.85 // High confidence for Apollo data
          }))
        } finally {
          clearTimeout(timeoutId)
        }
      }, `Apollo search for ${domain}`)
    }

    try {
      const contactsByCompany = {}
      let totalContacts = 0
      let successfulCompanies = 0

      // Process each company domain
      for (let i = 0; i < company_domains.length; i++) {
        const domain = company_domains[i]
        const companyName = company_names?.[i]

        try {
          console.log(`\n🏢 Processing company ${i + 1}/${company_domains.length}: ${domain}`)

          const contacts = await searchContactsForCompany(domain, companyName)
          contactsByCompany[domain] = contacts
          totalContacts += contacts.length

          if (contacts.length > 0) {
            successfulCompanies++
          }

          // Rate limiting: Add delay between companies
          if (i < company_domains.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 1500)) // 1.5 second delay
          }

        } catch (error) {
          console.error(`💥 All strategies failed for domain ${domain}:`, error instanceof Error ? error.message : error)
          contactsByCompany[domain] = []
        }
      }

      const successRate = company_domains.length > 0 ? (successfulCompanies / company_domains.length) * 100 : 0

      console.log(`\n📊 Apollo People Finder Results:`)
      console.log(`   Companies processed: ${company_domains.length}`)
      console.log(`   Successful searches: ${successfulCompanies}`)
      console.log(`   Success rate: ${successRate.toFixed(1)}%`)
      console.log(`   Total contacts found: ${totalContacts}`)

      return {
        success: true,
        contacts_by_company: contactsByCompany,
        total_contacts: totalContacts,
        companies_processed: company_domains.length,
        successful_companies: successfulCompanies,
        success_rate: successRate,
        search_strategies_used: use_fallback_search ? 'Multiple fallback strategies' : 'Primary strategy only',
        confidence_score: successRate >= 60 ? 0.85 : successRate >= 30 ? 0.70 : 0.50
      }

    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('Apollo API request timeout after 30 seconds')
      }
      
      console.error('Apollo find contacts error:', error)
      throw new Error(`Apollo API error: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }
})
