/**
 * Firecrawl API Types and Interfaces
 * 
 * This file contains all TypeScript interfaces and types for Firecrawl API integration
 * following our established patterns for external API integrations.
 */

// Base Firecrawl API Response Structure
export interface FirecrawlBaseResponse {
  success: boolean
  error?: string
  warning?: string
}

// Common extraction options used across tools
export interface FirecrawlExtractionOptions {
  onlyMainContent?: boolean
  includeTags?: string[]
  excludeTags?: string[]
  formats?: ('markdown' | 'html' | 'rawHtml' | 'screenshot')[]
}

// User interaction actions for scraping
export interface FirecrawlAction {
  type: 'click' | 'scroll' | 'wait' | 'type'
  selector?: string
  text?: string
  coordinate?: [number, number]
  milliseconds?: number
}

// AI-powered extraction configuration
export interface FirecrawlExtractSchema {
  prompt: string
  schema?: Record<string, any>
}

// Location targeting for search
export interface FirecrawlLocation {
  country?: string
  language?: string
}

// Search options and filters
export interface FirecrawlSearchOptions {
  tbs?: string // Time-based search (e.g., 'qdr:d' for past day)
  filter?: string
  location?: FirecrawlLocation
}

// ============================================================================
// SCRAPE TOOL INTERFACES
// ============================================================================

export interface FirecrawlScrapeRequest {
  url: string
  formats?: ('markdown' | 'html' | 'rawHtml' | 'screenshot')[]
  onlyMainContent?: boolean
  includeTags?: string[]
  excludeTags?: string[]
  waitFor?: number
  actions?: FirecrawlAction[]
  location?: FirecrawlLocation
  extractSchema?: FirecrawlExtractSchema
}

export interface FirecrawlScrapeResult {
  markdown?: string
  html?: string
  rawHtml?: string
  screenshot?: string
  metadata: {
    title: string
    description?: string
    language?: string
    sourceURL: string
    statusCode: number
    error?: string
  }
  extractedData?: Record<string, any>
}

export interface FirecrawlScrapeResponse extends FirecrawlBaseResponse {
  data?: FirecrawlScrapeResult
}

// ============================================================================
// EXTRACT TOOL INTERFACES  
// ============================================================================

export interface FirecrawlExtractRequest {
  urls: string[]
  schema: Record<string, any>
  prompt: string
  allowExternalLinks?: boolean
  limit?: number
  extractOptions?: FirecrawlExtractionOptions
}

export interface FirecrawlExtractResult {
  url: string
  extractedData: Record<string, any>
  metadata: {
    title: string
    description?: string
    statusCode: number
    error?: string
  }
}

export interface FirecrawlExtractResponse extends FirecrawlBaseResponse {
  data?: FirecrawlExtractResult[]
  total?: number
  processed?: number
}

// ============================================================================
// SEARCH TOOL INTERFACES
// ============================================================================

export interface FirecrawlSearchRequest {
  query: string
  limit?: number
  country?: string
  language?: string
  searchOptions?: FirecrawlSearchOptions
  extractOptions?: FirecrawlExtractionOptions
}

export interface FirecrawlSearchResult {
  url: string
  title: string
  description: string
  markdown?: string
  html?: string
  metadata: {
    sourceURL: string
    statusCode: number
    error?: string
  }
}

export interface FirecrawlSearchResponse extends FirecrawlBaseResponse {
  data?: FirecrawlSearchResult[]
  total?: number
}

// ============================================================================
// ENHANCED RESEARCH DATA STRUCTURES
// ============================================================================

// Enhanced company intelligence with Firecrawl data
export interface FirecrawlCompanyIntelligence {
  web_presence: {
    landing_page_analysis: {
      value_propositions: string[]
      pricing_strategy: string
      target_messaging: string
      call_to_actions: string[]
      confidence_score: number
      source: "Firecrawl Scrape"
      last_updated: string
    }
    content_strategy: {
      blog_topics: string[]
      content_frequency: string
      seo_focus: string[]
      content_types: string[]
      confidence_score: number
      source: "Firecrawl Extract"
      last_updated: string
    }
    market_positioning: {
      competitive_mentions: string[]
      industry_trends: string[]
      thought_leadership: string[]
      brand_messaging: string[]
      confidence_score: number
      source: "Firecrawl Search"
      last_updated: string
    }
  }
  technical_analysis: {
    technology_stack: string[]
    performance_metrics: {
      load_time?: number
      mobile_friendly?: boolean
      seo_score?: number
    }
    security_features: string[]
    accessibility_score?: number
  }
}

// Tool execution context for consistent error handling
export interface FirecrawlToolContext {
  apiKey: string
  baseUrl: string
  timeout: number
  retryAttempts: number
}

// Mock data structure for testing
export interface FirecrawlMockData {
  scrape: FirecrawlScrapeResult
  extract: FirecrawlExtractResult[]
  search: FirecrawlSearchResult[]
}

// Rate limiting and quota information
export interface FirecrawlQuotaInfo {
  remaining: number
  limit: number
  resetTime: string
}

// Error types specific to Firecrawl API
export type FirecrawlErrorType = 
  | 'authentication_error'
  | 'rate_limit_exceeded' 
  | 'invalid_url'
  | 'extraction_failed'
  | 'timeout_error'
  | 'quota_exceeded'
  | 'unknown_error'

export interface FirecrawlError {
  type: FirecrawlErrorType
  message: string
  statusCode?: number
  retryAfter?: number
}
