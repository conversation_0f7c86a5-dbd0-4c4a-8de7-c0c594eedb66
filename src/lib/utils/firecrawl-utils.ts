/**
 * Firecrawl API Utilities
 * 
 * This file contains utility functions for Firecrawl API integration
 * including error handling, mock data generation, and API helpers.
 */

import { env } from '$env/dynamic/private'
import type { 
  FirecrawlError, 
  FirecrawlErrorType, 
  FirecrawlMockData,
  FirecrawlScrapeResult,
  FirecrawlExtractResult,
  FirecrawlSearchResult,
  FirecrawlToolContext
} from '$lib/types/firecrawl'

// ============================================================================
// CONFIGURATION
// ============================================================================

export const FIRECRAWL_CONFIG = {
  baseUrl: 'https://api.firecrawl.dev',
  timeout: 30000, // 30 seconds
  retryAttempts: 2,
  maxUrlsPerExtract: 100,
  maxSearchResults: 100,
  defaultFormats: ['markdown'] as const,
  rateLimit: {
    requestsPerMinute: 60,
    burstLimit: 10
  }
} as const

// ============================================================================
// API HELPERS
// ============================================================================

/**
 * Get Firecrawl API configuration with authentication
 */
export function getFirecrawlContext(): FirecrawlToolContext {
  const apiKey = env.FIRECRAWL_API_KEY
  
  return {
    apiKey: apiKey || '',
    baseUrl: FIRECRAWL_CONFIG.baseUrl,
    timeout: FIRECRAWL_CONFIG.timeout,
    retryAttempts: FIRECRAWL_CONFIG.retryAttempts
  }
}

/**
 * Check if Firecrawl API is configured
 */
export function isFirecrawlConfigured(): boolean {
  return !!env.FIRECRAWL_API_KEY
}

/**
 * Create authenticated headers for Firecrawl API requests
 */
export function createFirecrawlHeaders(apiKey: string): Record<string, string> {
  return {
    'Authorization': `Bearer ${apiKey}`,
    'Content-Type': 'application/json',
    'User-Agent': 'Robynn-AI/1.0'
  }
}

/**
 * Create AbortController with timeout for API requests
 */
export function createTimeoutController(timeoutMs: number = FIRECRAWL_CONFIG.timeout): {
  controller: AbortController
  timeoutId: NodeJS.Timeout
} {
  const controller = new AbortController()
  const timeoutId = setTimeout(() => controller.abort(), timeoutMs)
  
  return { controller, timeoutId }
}

// ============================================================================
// ERROR HANDLING
// ============================================================================

/**
 * Parse Firecrawl API error response
 */
export function parseFirecrawlError(error: any, statusCode?: number): FirecrawlError {
  let errorType: FirecrawlErrorType = 'unknown_error'
  let message = 'An unknown error occurred'
  let retryAfter: number | undefined

  if (error instanceof Error && error.name === 'AbortError') {
    errorType = 'timeout_error'
    message = 'Request timeout after 30 seconds'
  } else if (statusCode) {
    switch (statusCode) {
      case 401:
        errorType = 'authentication_error'
        message = 'Invalid or missing Firecrawl API key'
        break
      case 429:
        errorType = 'rate_limit_exceeded'
        message = 'Firecrawl API rate limit exceeded'
        // Try to extract retry-after header if available
        break
      case 400:
        errorType = 'invalid_url'
        message = 'Invalid URL or request parameters'
        break
      case 402:
        errorType = 'quota_exceeded'
        message = 'Firecrawl API quota exceeded'
        break
      case 500:
      case 502:
      case 503:
        errorType = 'extraction_failed'
        message = 'Firecrawl service temporarily unavailable'
        break
      default:
        message = `Firecrawl API error: ${statusCode}`
    }
  } else if (error instanceof Error) {
    message = `Firecrawl API error: ${error.message}`
  }

  return {
    type: errorType,
    message,
    statusCode,
    retryAfter
  }
}

/**
 * Handle Firecrawl API errors with appropriate fallbacks
 */
export function handleFirecrawlError(error: FirecrawlError): never {
  console.error('Firecrawl API Error:', error)
  
  // Provide user-friendly error messages
  switch (error.type) {
    case 'authentication_error':
      throw new Error('Firecrawl API authentication failed. Please check your API key.')
    case 'rate_limit_exceeded':
      const retryMessage = error.retryAfter ? ` Retry after ${error.retryAfter} seconds.` : ''
      throw new Error(`Firecrawl API rate limit exceeded.${retryMessage}`)
    case 'quota_exceeded':
      throw new Error('Firecrawl API quota exceeded. Please upgrade your plan or try again later.')
    case 'timeout_error':
      throw new Error('Firecrawl API request timed out. The target website may be slow to respond.')
    case 'invalid_url':
      throw new Error('Invalid URL provided. Please check the URL format and try again.')
    case 'extraction_failed':
      throw new Error('Failed to extract content from the target website. The site may be blocking automated access.')
    default:
      throw new Error(error.message)
  }
}

// ============================================================================
// MOCK DATA GENERATION
// ============================================================================

/**
 * Generate mock scrape result for testing
 */
export function generateMockScrapeResult(url: string): FirecrawlScrapeResult {
  const domain = new URL(url).hostname
  
  return {
    markdown: `# ${domain} - Mock Content\n\nThis is mock content for testing purposes. The actual page content would appear here.\n\n## Key Features\n- Feature 1\n- Feature 2\n- Feature 3\n\n## About Us\nMock company description and information.`,
    metadata: {
      title: `${domain} - Mock Page Title`,
      description: `Mock description for ${domain}`,
      language: 'en',
      sourceURL: url,
      statusCode: 200
    },
    extractedData: {
      company_name: `Mock Company (${domain})`,
      value_proposition: 'Mock value proposition for testing',
      pricing: 'Contact for pricing',
      features: ['Feature 1', 'Feature 2', 'Feature 3']
    }
  }
}

/**
 * Generate mock extract results for testing
 */
export function generateMockExtractResults(urls: string[]): FirecrawlExtractResult[] {
  return urls.slice(0, 5).map((url, index) => {
    const domain = new URL(url).hostname
    
    return {
      url,
      extractedData: {
        title: `Mock Page ${index + 1}`,
        company: `Mock Company ${index + 1}`,
        description: `Mock description for ${domain}`,
        category: 'Technology',
        employees: `${(index + 1) * 50}-${(index + 1) * 100}`,
        founded: 2020 - index
      },
      metadata: {
        title: `${domain} - Mock Page ${index + 1}`,
        description: `Mock description for page ${index + 1}`,
        statusCode: 200
      }
    }
  })
}

/**
 * Generate mock search results for testing
 */
export function generateMockSearchResults(query: string, limit: number): FirecrawlSearchResult[] {
  const results: FirecrawlSearchResult[] = []
  
  for (let i = 0; i < Math.min(limit, 10); i++) {
    const domain = `example${i + 1}.com`
    const url = `https://${domain}`
    
    results.push({
      url,
      title: `Mock Search Result ${i + 1} for "${query}"`,
      description: `This is a mock search result description for ${query}. It contains relevant information about the search topic.`,
      markdown: `# Mock Search Result ${i + 1}\n\nContent related to "${query}" would appear here.\n\n## Key Points\n- Point 1 about ${query}\n- Point 2 about ${query}\n- Point 3 about ${query}`,
      metadata: {
        sourceURL: url,
        statusCode: 200
      }
    })
  }
  
  return results
}

/**
 * Generate complete mock data set
 */
export function generateFirecrawlMockData(): FirecrawlMockData {
  const mockUrl = 'https://example.com'
  const mockUrls = ['https://example1.com', 'https://example2.com', 'https://example3.com']
  
  return {
    scrape: generateMockScrapeResult(mockUrl),
    extract: generateMockExtractResults(mockUrls),
    search: generateMockSearchResults('test query', 5)
  }
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Validate URL format and accessibility
 */
export function validateFirecrawlUrl(url: string): boolean {
  try {
    const parsedUrl = new URL(url)
    // Only allow http and https protocols
    return ['http:', 'https:'].includes(parsedUrl.protocol)
  } catch {
    return false
  }
}

/**
 * Clean and normalize URL for Firecrawl API
 */
export function normalizeUrl(url: string): string {
  try {
    const parsedUrl = new URL(url)
    // Remove fragment and normalize
    parsedUrl.hash = ''
    return parsedUrl.toString()
  } catch {
    return url
  }
}

/**
 * Extract domain from URL for categorization
 */
export function extractDomain(url: string): string {
  try {
    return new URL(url).hostname
  } catch {
    return 'unknown'
  }
}

/**
 * Calculate confidence score based on data quality
 */
export function calculateFirecrawlConfidence(
  hasContent: boolean,
  hasMetadata: boolean,
  hasExtractedData: boolean,
  statusCode: number
): number {
  let score = 0.5 // Base score
  
  if (statusCode === 200) score += 0.2
  if (hasContent) score += 0.2
  if (hasMetadata) score += 0.1
  if (hasExtractedData) score += 0.2
  
  return Math.min(score, 1.0)
}
