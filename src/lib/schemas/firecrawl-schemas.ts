/**
 * Firecrawl API Zod Schemas
 * 
 * This file contains all Zod validation schemas for Firecrawl API tools
 * following our established patterns for input validation and type safety.
 */

import { z } from 'zod'

// ============================================================================
// COMMON SCHEMAS
// ============================================================================

// Common extraction options schema
export const firecrawlExtractionOptionsSchema = z.object({
  onlyMainContent: z.boolean().default(true).describe("Extract only main content, removing navigation and ads"),
  includeTags: z.array(z.string()).optional().describe("HTML tags to include in extraction"),
  excludeTags: z.array(z.string()).optional().describe("HTML tags to exclude from extraction"),
  formats: z.array(z.enum(["markdown", "html", "rawHtml", "screenshot"])).default(["markdown"]).describe("Output formats to return")
})

// User interaction actions schema
export const firecrawlActionSchema = z.object({
  type: z.enum(["click", "scroll", "wait", "type"]).describe("Type of action to perform"),
  selector: z.string().optional().describe("CSS selector for the element to interact with"),
  text: z.string().optional().describe("Text to type (for 'type' action)"),
  coordinate: z.tuple([z.number(), z.number()]).optional().describe("X,Y coordinates for click action"),
  milliseconds: z.number().optional().describe("Milliseconds to wait (for 'wait' action)")
})

// AI extraction schema configuration
export const firecrawlExtractSchemaConfig = z.object({
  prompt: z.string().min(10).describe("Natural language prompt describing what data to extract"),
  schema: z.record(z.any()).optional().describe("JSON schema defining the structure of data to extract")
})

// Location targeting schema
export const firecrawlLocationSchema = z.object({
  country: z.string().length(2).optional().describe("Two-letter country code (e.g., 'US', 'UK')"),
  language: z.string().length(2).optional().describe("Two-letter language code (e.g., 'en', 'es')")
})

// Search options schema
export const firecrawlSearchOptionsSchema = z.object({
  tbs: z.string().optional().describe("Time-based search filters (e.g., 'qdr:d' for past day, 'qdr:w' for past week)"),
  filter: z.string().optional().describe("Additional search filters and operators"),
  location: firecrawlLocationSchema.optional().describe("Geographic targeting for search results")
})

// ============================================================================
// SCRAPE TOOL SCHEMA
// ============================================================================

export const firecrawlScrapeSchema = z.object({
  url: z.string().url().describe("URL to scrape and analyze"),
  formats: z.array(z.enum(["markdown", "html", "rawHtml", "screenshot"]))
    .default(["markdown"])
    .describe("Output formats to return from the scraped page"),
  onlyMainContent: z.boolean()
    .default(true)
    .describe("Extract only main content, removing navigation, ads, and boilerplate"),
  includeTags: z.array(z.string())
    .optional()
    .describe("Specific HTML tags to include in extraction (e.g., ['article', 'main', 'section'])"),
  excludeTags: z.array(z.string())
    .optional()
    .describe("HTML tags to exclude from extraction (e.g., ['nav', 'footer', 'aside'])"),
  waitFor: z.number()
    .min(0)
    .max(30000)
    .optional()
    .describe("Milliseconds to wait after page load before extraction (max 30 seconds)"),
  actions: z.array(firecrawlActionSchema)
    .optional()
    .describe("User interactions to simulate before extraction (clicking tabs, scrolling, etc.)"),
  location: firecrawlLocationSchema
    .optional()
    .describe("Geographic location for the request (affects content localization)"),
  extractSchema: firecrawlExtractSchemaConfig
    .optional()
    .describe("AI-powered extraction configuration for structured data extraction")
})

// ============================================================================
// EXTRACT TOOL SCHEMA
// ============================================================================

export const firecrawlExtractSchema = z.object({
  urls: z.array(z.string().url())
    .min(1)
    .max(100)
    .describe("URLs to extract data from (max 100 URLs per request)"),
  schema: z.record(z.any())
    .describe("JSON schema defining the structure of data to extract from each page"),
  prompt: z.string()
    .min(10)
    .describe("Natural language description of what data to extract and how to interpret it"),
  allowExternalLinks: z.boolean()
    .default(false)
    .describe("Whether to follow and extract data from external links found on the pages"),
  limit: z.number()
    .min(1)
    .max(1000)
    .default(10)
    .describe("Maximum number of pages to process per URL pattern"),
  extractOptions: firecrawlExtractionOptionsSchema
    .optional()
    .describe("Additional extraction options for content filtering and formatting")
})

// ============================================================================
// SEARCH TOOL SCHEMA
// ============================================================================

export const firecrawlSearchSchema = z.object({
  query: z.string()
    .min(1)
    .describe("Search query to execute (supports Google search operators)"),
  limit: z.number()
    .min(1)
    .max(100)
    .default(10)
    .describe("Number of search results to return and scrape (max 100)"),
  country: z.string()
    .length(2)
    .optional()
    .describe("Two-letter country code for geo-targeted search (e.g., 'US', 'UK', 'CA')"),
  language: z.string()
    .length(2)
    .optional()
    .describe("Two-letter language code for search results (e.g., 'en', 'es', 'fr')"),
  searchOptions: firecrawlSearchOptionsSchema
    .optional()
    .describe("Advanced search options including time filters and additional parameters"),
  extractOptions: firecrawlExtractionOptionsSchema
    .optional()
    .describe("Content extraction options for search results")
})

// ============================================================================
// VALIDATION HELPERS
// ============================================================================

// URL validation helper
export const validateUrl = (url: string): boolean => {
  try {
    new URL(url)
    return true
  } catch {
    return false
  }
}

// Schema validation helper for extract tool
export const validateExtractSchema = (schema: Record<string, any>): boolean => {
  // Basic validation - ensure schema has at least one property
  return Object.keys(schema).length > 0
}

// Action sequence validation
export const validateActionSequence = (actions: z.infer<typeof firecrawlActionSchema>[]): boolean => {
  // Ensure actions are in logical order and have required properties
  for (const action of actions) {
    switch (action.type) {
      case 'click':
        if (!action.selector && !action.coordinate) return false
        break
      case 'type':
        if (!action.selector || !action.text) return false
        break
      case 'wait':
        if (!action.milliseconds) return false
        break
      case 'scroll':
        // Scroll can work without additional parameters
        break
    }
  }
  return true
}

// Search query validation helper
export const validateSearchQuery = (query: string): boolean => {
  // Ensure query is not just whitespace and has reasonable length
  return query.trim().length > 0 && query.length <= 500
}

// Export type inference helpers
export type FirecrawlScrapeInput = z.infer<typeof firecrawlScrapeSchema>
export type FirecrawlExtractInput = z.infer<typeof firecrawlExtractSchema>
export type FirecrawlSearchInput = z.infer<typeof firecrawlSearchSchema>
export type FirecrawlAction = z.infer<typeof firecrawlActionSchema>
export type FirecrawlExtractionOptions = z.infer<typeof firecrawlExtractionOptionsSchema>
export type FirecrawlLocation = z.infer<typeof firecrawlLocationSchema>
export type FirecrawlSearchOptions = z.infer<typeof firecrawlSearchOptionsSchema>
export type FirecrawlExtractSchemaConfig = z.infer<typeof firecrawlExtractSchemaConfig>
